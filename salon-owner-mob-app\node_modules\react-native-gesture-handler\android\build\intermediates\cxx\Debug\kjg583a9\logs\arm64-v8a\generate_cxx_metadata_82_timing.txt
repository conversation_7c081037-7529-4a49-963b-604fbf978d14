# C/C++ build system timings
generate_cxx_metadata
  [gap of 45ms]
  create-invalidation-state 57ms
  [gap of 12ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 49ms]
  create-invalidation-state 62ms
  [gap of 18ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 147ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 55ms]
  create-invalidation-state 64ms
  [gap of 16ms]
  write-metadata-json-to-file 27ms
generate_cxx_metadata completed in 162ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 60ms]
  create-invalidation-state 62ms
  [gap of 22ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 166ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 37ms]
  create-invalidation-state 30ms
  write-metadata-json-to-file 27ms
generate_cxx_metadata completed in 94ms

