import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import { offerService } from '../services/offerService';
import {
  CreateOfferRequest,
  UpdateOfferRequest,
  OfferSearchFilters,
  BulkOfferOperation
} from '../types/offer';
import { azureBlobStorage } from '../config/azure';
import { pushNotificationService } from '../services/pushNotificationService';

class OfferController {
  // Create a new offer
  async createOffer(req: Request, res: Response): Promise<void> {
    try {
      console.log('🎯 OfferController: Creating offer with data:', req.body);

      const offerData: CreateOfferRequest = req.body;

      // Handle image upload
      let imageUrl: string | undefined;
      if (req.file) {
        try {
          // Generate a unique blob name for the offer image
          const blobName = `offer-images/${uuidv4()}${path.extname(req.file.originalname)}`;

          // Upload to Azure Blob Storage
          imageUrl = await azureBlobStorage.uploadBlob(
            blobName,
            req.file.buffer,
            req.file.mimetype
          );
          console.log('✅ Offer image uploaded:', imageUrl);
        } catch (uploadError) {
          console.error('❌ Error uploading offer image:', uploadError);
          // Continue without image
        }
      }

      // Create offer
      const offer = await offerService.create({
        ...offerData,
        image: imageUrl,
      });

      console.log('✅ OfferController: Offer created successfully:', offer.id);

      // Send push notifications to applicable customers if offer is active
      if (offer.isActive) {
        try {
          console.log('🔔 OfferController: Sending push notifications for new offer:', offer.id);
          // Send notifications asynchronously to avoid blocking the response
          pushNotificationService.sendOfferNotification(offer.id).catch(error => {
            console.error('❌ OfferController: Error sending push notifications:', error);
          });
        } catch (error) {
          console.error('❌ OfferController: Error initiating push notifications:', error);
          // Don't fail the offer creation if notifications fail
        }
      }

      res.status(201).json({
        success: true,
        data: {
          data: offer
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error creating offer:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error creating offer'
      });
    }
  }

  // Get all offers
  async getOffers(req: Request, res: Response): Promise<void> {
    try {
      console.log('🔍 OfferController: Fetching offers with query:', req.query);

      const filters: OfferSearchFilters = {
        status: req.query.status as any,
        discountType: req.query.discountType as any,
        customerType: req.query.customerType as any,
        frequency: req.query.frequency as any,
        serviceCategory: req.query.serviceCategory as string,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as any || 'DESC',
        search: req.query.search as string,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      };

      // Parse date filters
      if (req.query.validFrom) {
        filters.validFrom = new Date(req.query.validFrom as string);
      }
      if (req.query.validUntil) {
        filters.validUntil = new Date(req.query.validUntil as string);
      }

      const result = await offerService.findAll(filters);

      console.log('✅ OfferController: Found offers:', result.total);

      res.status(200).json({
        success: true,
        data: {
          data: result.offers,
          total: result.total,
          filters
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error fetching offers:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error fetching offers'
      });
    }
  }

  // Get offers by salon
  async getOffersBySalon(req: Request, res: Response): Promise<void> {
    try {
      const { salonId } = req.params;
      console.log('🔍 OfferController: Fetching offers for salon:', salonId);

      const filters: OfferSearchFilters = {
        status: req.query.status as any,
        discountType: req.query.discountType as any,
        customerType: req.query.customerType as any,
        frequency: req.query.frequency as any,
        serviceCategory: req.query.serviceCategory as string,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as any || 'DESC',
        search: req.query.search as string,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      };

      // Parse date filters
      if (req.query.validFrom) {
        filters.validFrom = new Date(req.query.validFrom as string);
      }
      if (req.query.validUntil) {
        filters.validUntil = new Date(req.query.validUntil as string);
      }

      const result = await offerService.findBySalon(salonId, filters);

      console.log('✅ OfferController: Found salon offers:', result.total);

      res.status(200).json({
        success: true,
        data: {
          data: result.offers,
          total: result.total,
          filters
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error fetching salon offers:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error fetching salon offers'
      });
    }
  }

  // Get offer by ID
  async getOffer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      console.log('🔍 OfferController: Fetching offer:', id);

      const offer = await offerService.findById(id);

      console.log('✅ OfferController: Found offer:', offer.title);

      res.status(200).json({
        success: true,
        data: {
          data: offer
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error fetching offer:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error fetching offer'
      });
    }
  }

  // Update offer
  async updateOffer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      console.log('🔄 OfferController: Updating offer:', id);

      const updateData: UpdateOfferRequest = req.body;

      // Handle image upload
      if (req.file) {
        try {
          // Generate a unique blob name for the offer image
          const blobName = `offer-images/${uuidv4()}${path.extname(req.file.originalname)}`;

          // Upload to Azure Blob Storage
          const imageUrl = await azureBlobStorage.uploadBlob(
            blobName,
            req.file.buffer,
            req.file.mimetype
          );
          updateData.image = imageUrl;
          console.log('✅ Offer image updated:', imageUrl);
        } catch (uploadError) {
          console.error('❌ Error uploading offer image:', uploadError);
          // Continue without updating image
        }
      }

      // Get the original offer to check if status changed
      const originalOffer = await offerService.findById(id);
      const wasInactive = !originalOffer.isActive;

      const offer = await offerService.update(id, updateData);

      console.log('✅ OfferController: Offer updated successfully:', offer.title);

      // Send push notifications if offer was just activated
      if (wasInactive && offer.isActive) {
        try {
          console.log('🔔 OfferController: Sending push notifications for activated offer:', offer.id);
          // Send notifications asynchronously to avoid blocking the response
          pushNotificationService.sendOfferNotification(offer.id).catch(error => {
            console.error('❌ OfferController: Error sending push notifications:', error);
          });
        } catch (error) {
          console.error('❌ OfferController: Error initiating push notifications:', error);
          // Don't fail the offer update if notifications fail
        }
      }

      res.status(200).json({
        success: true,
        data: {
          data: offer
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error updating offer:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error updating offer'
      });
    }
  }

  // Delete offer
  async deleteOffer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      console.log('🗑️ OfferController: Deleting offer:', id);

      await offerService.delete(id);

      console.log('✅ OfferController: Offer deleted successfully');

      res.status(200).json({
        success: true,
        data: {
          message: 'Offer deleted successfully'
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error deleting offer:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error deleting offer'
      });
    }
  }

  // Bulk operations
  async bulkOperation(req: Request, res: Response): Promise<void> {
    try {
      console.log('🔄 OfferController: Performing bulk operation:', req.body);

      const operation: BulkOfferOperation = req.body;

      await offerService.bulkOperation(operation);

      console.log('✅ OfferController: Bulk operation completed successfully');

      res.status(200).json({
        success: true,
        data: {
          message: 'Bulk operation completed successfully'
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error performing bulk operation:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error performing bulk operation'
      });
    }
  }

  // Get offer statistics
  async getOfferStats(req: Request, res: Response): Promise<void> {
    try {
      const { salonId } = req.query;
      console.log('📊 OfferController: Fetching offer statistics for salon:', salonId);

      const stats = await offerService.getStats(salonId as string);

      console.log('✅ OfferController: Statistics fetched successfully');

      res.status(200).json({
        success: true,
        data: {
          data: stats
        }
      });
    } catch (error: any) {
      console.error('❌ OfferController: Error fetching statistics:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error fetching statistics'
      });
    }
  }

  // Publish offer (send notifications to applicable customers)
  async publishOffer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      console.log('📢 OfferController: Publishing offer:', id);

      // Get offer details to validate
      const offer = await offerService.findById(id);
      if (!offer) {
        res.status(404).json({
          success: false,
          error: 'Offer not found'
        });
        return;
      }

      // Check if offer is active
      if (!offer.isActive) {
        res.status(400).json({
          success: false,
          error: 'Cannot publish inactive offer. Please activate the offer first.'
        });
        return;
      }

      // Check if offer is still valid
      const now = new Date();
      if (new Date(offer.validUntil) <= now) {
        res.status(400).json({
          success: false,
          error: 'Cannot publish expired offer'
        });
        return;
      }

      // Send push notifications to applicable customers
      try {
        console.log('🔔 OfferController: Sending push notifications for published offer:', id);
        await pushNotificationService.sendOfferNotification(id);
        console.log('✅ OfferController: Push notifications sent successfully');

        res.status(200).json({
          success: true,
          data: {
            message: 'Offer published successfully! Notifications have been sent to applicable customers.'
          }
        });
      } catch (notificationError: any) {
        console.error('❌ OfferController: Error sending push notifications:', notificationError);

        // Check if it's a Firebase configuration error
        if (notificationError.message && notificationError.message.includes('404')) {
          res.status(200).json({
            success: true,
            data: {
              message: 'Offer published successfully, but push notifications are not configured. Please check Firebase settings.'
            }
          });
        } else if (notificationError.message && notificationError.message.includes('No applicable customers')) {
          res.status(200).json({
            success: true,
            data: {
              message: 'Offer published successfully, but no applicable customers were found for this offer.'
            }
          });
        } else {
          // For other errors, still return success but with a warning
          res.status(200).json({
            success: true,
            data: {
              message: 'Offer published successfully, but there was an issue sending notifications. Please try again later.'
            }
          });
        }
        return;
      }
    } catch (error: any) {
      console.error('❌ OfferController: Error publishing offer:', error);
      res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Error publishing offer'
      });
    }
  }
}

export const offerController = new OfferController();

// Export individual methods for route binding
export const {
  createOffer,
  getOffers,
  getOffersBySalon,
  getOffer,
  updateOffer,
  deleteOffer,
  bulkOperation,
  getOfferStats,
  publishOffer
} = offerController;
