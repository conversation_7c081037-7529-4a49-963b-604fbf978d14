import { AppDataSource } from '../config/typeorm';
import { permissionService } from '../services/permissionService';

/**
 * Initialize default permissions in the database
 * This script can be run independently or as part of application startup
 */
export async function initializePermissions(): Promise<void> {
  try {
    console.log('🔍 Initializing database connection...');
    
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    console.log('🔍 Initializing default permissions...');
    
    // Initialize default permissions using the permission service
    await permissionService.initializeDefaultPermissions();
    
    console.log('✅ Default permissions initialization completed successfully');
    
    // List all permissions to verify
    const result = await permissionService.getAllPermissions();
    console.log(`📊 Total permissions in database: ${result.total}`);
    
    // Group by category for better overview
    const permissionsByCategory = await permissionService.getPermissionsByCategory();
    
    console.log('\n📋 Permissions by category:');
    Object.entries(permissionsByCategory).forEach(([category, permissions]) => {
      if (permissions.length > 0) {
        console.log(`\n${category}:`);
        permissions.forEach(permission => {
          console.log(`  - ${permission.code}: ${permission.name}`);
        });
      }
    });
    
  } catch (error) {
    console.error('❌ Error initializing permissions:', error);
    throw error;
  }
}

/**
 * Run the script if called directly
 */
if (require.main === module) {
  initializePermissions()
    .then(() => {
      console.log('\n🎉 Permission initialization script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Permission initialization script failed:', error);
      process.exit(1);
    });
}
