-- =====================================================
-- Initialize Default Permissions for Salon Management System
-- =====================================================
-- This script creates the basic permissions required for the salon management system
-- Run this script after creating the permissions table

-- Ensure we're working with the correct database
-- \c salon_management_db;

-- Insert default permissions with proper UUIDs and timestamps
INSERT INTO permissions (
    id, 
    code, 
    name, 
    description, 
    category, 
    resource, 
    action, 
    "isActive", 
    "sortOrder", 
    "createdAt", 
    "updatedAt"
) VALUES 
-- =====================================================
-- SERVICES PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_SERVICES',
    'View Services',
    'View salon services and pricing information',
    'SERVICES',
    'services',
    'read',
    true,
    1,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_SERVICES',
    'Manage Services',
    'Create, edit, and delete salon services',
    'SERVICES',
    'services',
    'write',
    true,
    2,
    NOW(),
    NOW()
),

-- =====================================================
-- OFFERS PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_OFFERS',
    'View Offers',
    'View salon offers and promotions',
    'OFFERS',
    'offers',
    'read',
    true,
    3,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_OFFERS',
    'Manage Offers',
    'Create, edit, and delete salon offers',
    'OFFERS',
    'offers',
    'write',
    true,
    4,
    NOW(),
    NOW()
),

-- =====================================================
-- BOOKINGS PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_BOOKINGS',
    'View Bookings',
    'View customer bookings and appointments',
    'BOOKINGS',
    'bookings',
    'read',
    true,
    5,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_BOOKINGS',
    'Manage Bookings',
    'Create, edit, and cancel customer bookings',
    'BOOKINGS',
    'bookings',
    'write',
    true,
    6,
    NOW(),
    NOW()
),

-- =====================================================
-- CUSTOMERS PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_CUSTOMERS',
    'View Customers',
    'View customer information and history',
    'CUSTOMERS',
    'customers',
    'read',
    true,
    7,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_CUSTOMERS',
    'Manage Customers',
    'Edit customer information and preferences',
    'CUSTOMERS',
    'customers',
    'write',
    true,
    8,
    NOW(),
    NOW()
),

-- =====================================================
-- REPORTS PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_REPORTS',
    'View Reports',
    'View salon analytics and reports',
    'REPORTS',
    'reports',
    'read',
    true,
    9,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_REPORTS',
    'Manage Reports',
    'Create and configure custom reports',
    'REPORTS',
    'reports',
    'write',
    true,
    10,
    NOW(),
    NOW()
),

-- =====================================================
-- STAFF PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_STAFF',
    'View Staff',
    'View other staff members and their information',
    'STAFF',
    'staff',
    'read',
    true,
    11,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_STAFF',
    'Manage Staff',
    'Manage other staff members (supervisor role)',
    'STAFF',
    'staff',
    'write',
    true,
    12,
    NOW(),
    NOW()
),

-- =====================================================
-- SALON PERMISSIONS
-- =====================================================
(
    gen_random_uuid(),
    'VIEW_SALON_SETTINGS',
    'View Salon Settings',
    'View salon configuration and settings',
    'SALON',
    'salon',
    'read',
    true,
    13,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'MANAGE_SALON_SETTINGS',
    'Manage Salon Settings',
    'Edit salon configuration and settings',
    'SALON',
    'salon',
    'write',
    true,
    14,
    NOW(),
    NOW()
)
-- Use ON CONFLICT to avoid duplicate entries if script is run multiple times
ON CONFLICT (code) DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify all permissions were inserted
SELECT 
    category,
    COUNT(*) as permission_count
FROM permissions 
WHERE "isActive" = true
GROUP BY category
ORDER BY category;

-- Show all permissions with details
SELECT 
    code,
    name,
    category,
    description,
    "sortOrder"
FROM permissions 
WHERE "isActive" = true
ORDER BY category, "sortOrder";

-- Show total count
SELECT COUNT(*) as total_permissions FROM permissions WHERE "isActive" = true;

COMMIT;
