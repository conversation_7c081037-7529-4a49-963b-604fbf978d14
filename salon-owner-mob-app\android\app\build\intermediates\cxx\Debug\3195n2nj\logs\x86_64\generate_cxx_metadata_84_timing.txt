# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 39ms
  [gap of 13ms]
generate_cxx_metadata completed in 86ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 14ms]
generate_cxx_metadata completed in 27ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 23ms
  [gap of 10ms]
generate_cxx_metadata completed in 47ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 46ms
  [gap of 19ms]
generate_cxx_metadata completed in 95ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 80ms
  [gap of 63ms]
generate_cxx_metadata completed in 166ms

# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 743ms
    [gap of 34ms]
  generate-prefab-packages completed in 777ms
  execute-generate-process
    exec-configure 600ms
    [gap of 100ms]
  execute-generate-process completed in 700ms
  [gap of 66ms]
generate_cxx_metadata completed in 1543ms

