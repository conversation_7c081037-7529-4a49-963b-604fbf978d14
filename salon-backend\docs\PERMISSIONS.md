# Permission Management System

This document explains how to set up and manage the permission system for the salon management application.

## Overview

The permission system uses a separate `permissions` table to store all available permissions, and a `staff_permissions` table to assign permissions to staff members for specific salons.

## Database Schema

### Permissions Table
- `id` - UUID primary key
- `code` - Unique permission code (e.g., 'VIEW_SERVICES')
- `name` - Human-readable name (e.g., 'View Services')
- `description` - Detailed description of what the permission allows
- `category` - Permission category (SERVICES, OFFERS, BOOKINGS, etc.)
- `resource` - Resource this permission applies to
- `action` - Action this permission allows (read, write)
- `isActive` - Whether the permission is active
- `sortOrder` - Display order for UI

### Staff Permissions Table
- `id` - UUID primary key
- `staffSalonAccessId` - Reference to staff salon access
- `permissionId` - Reference to permission
- `isActive` - Whether this assignment is active
- `grantedBy` - Who granted this permission
- `grantedAt` - When it was granted
- `revokedBy` - Who revoked this permission (if applicable)
- `revokedAt` - When it was revoked (if applicable)
- `expiresAt` - When this permission expires (optional)

## Default Permissions

The system includes 14 default permissions across 7 categories:

### Services (2 permissions)
- `VIEW_SERVICES` - View salon services and pricing
- `MANAGE_SERVICES` - Create, edit, and delete salon services

### Offers (2 permissions)
- `VIEW_OFFERS` - View salon offers and promotions
- `MANAGE_OFFERS` - Create, edit, and delete salon offers

### Bookings (2 permissions)
- `VIEW_BOOKINGS` - View customer bookings and appointments
- `MANAGE_BOOKINGS` - Create, edit, and cancel bookings

### Customers (2 permissions)
- `VIEW_CUSTOMERS` - View customer information and history
- `MANAGE_CUSTOMERS` - Edit customer information and preferences

### Reports (2 permissions)
- `VIEW_REPORTS` - View salon analytics and reports
- `MANAGE_REPORTS` - Create and configure custom reports

### Staff (2 permissions)
- `VIEW_STAFF` - View other staff members and their information
- `MANAGE_STAFF` - Manage other staff members (supervisor role)

### Salon (2 permissions)
- `VIEW_SALON_SETTINGS` - View salon configuration and settings
- `MANAGE_SALON_SETTINGS` - Edit salon configuration and settings

## Setup Instructions

### Method 1: Automatic Initialization (Recommended)
The permissions are automatically initialized when the server starts. No manual action required.

### Method 2: Manual Script Execution
```bash
# Run the permission initialization script
npm run init:permissions

# Or use the seed command
npm run db:seed
```

### Method 3: Direct SQL Execution
```bash
# Execute the SQL script directly in your database
psql -d your_database_name -f sql/initialize-permissions.sql
```

### Method 4: TypeORM Migration
```bash
# Run the migration
npm run migration:run
```

## Verification

After initialization, verify the permissions were created:

```sql
-- Check total permissions
SELECT COUNT(*) as total_permissions FROM permissions WHERE "isActive" = true;

-- Check permissions by category
SELECT category, COUNT(*) as count 
FROM permissions 
WHERE "isActive" = true 
GROUP BY category 
ORDER BY category;

-- View all permissions
SELECT code, name, category, description 
FROM permissions 
WHERE "isActive" = true 
ORDER BY category, "sortOrder";
```

## API Endpoints

### Get Available Permissions
```
GET /api/v1/staff/permissions
```

Returns all active permissions formatted for UI use.

### Create Staff with Permissions
```
POST /api/v1/staff
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "salonIds": ["salon-1-id", "salon-2-id"],
  "permissions": ["VIEW_SERVICES", "MANAGE_BOOKINGS"]
}
```

## Usage Examples

### Check Staff Permission
```typescript
// Check if staff has permission for a specific salon
const hasPermission = await staffService.checkStaffPermission(
  staffId, 
  salonId, 
  'VIEW_SERVICES'
);
```

### Get Staff Permissions
```typescript
// Get all permissions for a staff member at a salon
const staffAccess = await staffAccessRepository.findOne({
  where: { staffId, salonId },
  relations: ['staffPermissions', 'staffPermissions.permission']
});

const permissions = staffAccess.activePermissions;
```

## Adding New Permissions

### 1. Add to Default Permissions
Edit `src/entities/Permission.ts` and add to `DEFAULT_PERMISSIONS` array:

```typescript
{
  code: 'NEW_PERMISSION',
  name: 'New Permission',
  description: 'Description of what this permission allows',
  category: PermissionCategory.SERVICES,
  resource: 'resource_name',
  action: 'read',
  sortOrder: 15,
}
```

### 2. Run Initialization
```bash
npm run init:permissions
```

### 3. Update Frontend
Update the mobile app to handle the new permission in the UI.

## Troubleshooting

### Permissions Not Loading
1. Check database connection
2. Verify permissions table exists
3. Run initialization script manually
4. Check server logs for errors

### Permission API Returning 500 Error
1. Check route order in `staffRoutes.ts`
2. Verify permission service is properly imported
3. Check database permissions

### Staff Can't Access Features
1. Verify staff has correct permissions assigned
2. Check permission codes match exactly
3. Verify staff salon access is active
4. Check permission expiration dates

## Security Considerations

1. **Permission Validation** - Always validate permissions on the backend
2. **Audit Trail** - All permission changes are logged with who/when
3. **Expiration** - Permissions can have expiration dates
4. **Revocation** - Permissions can be revoked without deletion
5. **Principle of Least Privilege** - Grant minimum necessary permissions

## Performance Considerations

1. **Caching** - Consider caching permission checks for frequently accessed data
2. **Indexing** - Database indexes on permission lookup fields
3. **Batch Operations** - Use batch operations for multiple permission assignments
4. **Lazy Loading** - Load permissions only when needed

## Backup and Recovery

1. **Regular Backups** - Include permissions table in regular database backups
2. **Permission Export** - Export permission configurations for disaster recovery
3. **Version Control** - Keep permission definitions in version control
4. **Testing** - Test permission system in staging environment before production
