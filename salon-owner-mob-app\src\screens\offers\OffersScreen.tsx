import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import {
  DarkTitle,
  DarkBody,
  WhiteTitle,
} from '../../components/common/Typography';
import { Colors, Gradients } from '../../constants/colors';
import { offerService, OfferFilters } from '../../services/offerService';

const { width, height } = Dimensions.get('window');

interface Offer {
  id: string;
  title: string;
  description: string;
  discountType: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_SERVICE';
  discountValue: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  validFrom: string | Date;
  validUntil: string | Date;
  isActive: boolean;
  usageLimit?: number;
  usedCount: number;
  code: string;
  image?: string;

  // Service-specific targeting
  applicableServices: string[]; // Service IDs or 'all'
  serviceCategories: string[]; // Service categories

  // Customer segmentation
  customerType: 'ALL' | 'NEW' | 'EXISTING' | 'VIP' | 'LOYALTY_TIER';
  loyaltyTierRequired?: string;
  minVisitsRequired?: number;
  maxUsagePerCustomer?: number;

  // Date and time controls
  validDays: string[]; // ['MONDAY', 'TUESDAY', etc.] or 'ALL'
  validTimeSlots: {
    startTime: string; // '09:00'
    endTime: string;   // '17:00'
  }[];
  blackoutDates: (string | Date)[]; // Excluded dates

  // Duration and frequency
  offerDuration: {
    type: 'DAYS' | 'WEEKS' | 'MONTHS' | 'CUSTOM';
    value: number;
  };
  frequency: 'ONE_TIME' | 'RECURRING' | 'LIMITED_USES';
  cooldownPeriod?: number; // Days between uses for same customer

  // Advanced settings
  combinableWithOtherOffers: boolean;
  requiresAdvanceBooking?: number; // Hours in advance
  autoApply: boolean; // Apply automatically if conditions met
  priority: number; // For offer stacking
}

export const OffersScreen: React.FC = () => {
  const navigation = useNavigation<StackNavigationProp<any>>();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();

  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [publishingOffers, setPublishingOffers] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<OfferFilters>({
    sortBy: 'createdAt',
    sortOrder: 'DESC',
  });

  // Load offers from API
  const loadOffers = useCallback(async (showLoading = true) => {
    if (!selectedSalon) return;

    try {
      if (showLoading) setLoading(true);

      console.log('🔍 OffersScreen: Loading offers with filters:', filters);
      const response = await offerService.getOffersBySalon(selectedSalon.id, filters);

      setOffers(response.data || []);
      console.log('✅ OffersScreen: Loaded offers:', response.data);
    } catch (error: any) {
      console.error('❌ OffersScreen: Error loading offers:', error);
      showToast('Failed to load offers', 'error', 3000);
      setOffers([]); // Set empty array on error
    } finally {
      if (showLoading) setLoading(false);
      setRefreshing(false);
    }
  }, [selectedSalon, filters, showToast]);

  // Focus effect to reload offers
  useFocusEffect(
    useCallback(() => {
      loadOffers();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadOffers(false);
  };

  const handleAddOffer = () => {
    navigation.navigate('AddOffer', { salonId: selectedSalon?.id }); 
  };

  const handleViewOffer = (offer: Offer) => {
    navigation.navigate('ViewOfferDetails', { offerId: offer.id });
  };

  const handleEditOffer = (offer: Offer) => {
    navigation.navigate('EditOffer', { offerId: offer.id });
  };

  const handleToggleOfferStatus = async (offerId: string, newStatus: boolean) => {
    try {
      console.log('🔄 OffersScreen: Toggling offer status:', offerId, newStatus);

      await offerService.updateOffer(offerId, { isActive: newStatus });

      // Update local state
      setOffers(prev => prev.map(offer =>
        offer.id === offerId ? { ...offer, isActive: newStatus } : offer
      ));

      showToast(`Offer ${newStatus ? 'activated' : 'deactivated'}`, 'success', 2000);
      console.log('✅ OffersScreen: Offer status updated successfully');
    } catch (error: any) {
      console.error('❌ OffersScreen: Error updating offer status:', error);
      showToast('Failed to update offer status', 'error', 3000);
    }
  };

  const handleDeleteOffer = (offerId: string) => {
    const offer = offers.find(o => o.id === offerId);
    Alert.alert(
      'Delete Offer',
      `Are you sure you want to delete "${offer?.title}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ OffersScreen: Deleting offer:', offerId);

              await offerService.deleteOffer(offerId);

              // Update local state
              setOffers(prev => prev.filter(offer => offer.id !== offerId));

              showToast('Offer deleted successfully', 'success', 2000);
              console.log('✅ OffersScreen: Offer deleted successfully');
            } catch (error: any) {
              console.error('❌ OffersScreen: Error deleting offer:', error);
              showToast('Failed to delete offer', 'error', 3000);
            }
          },
        },
      ]
    );
  };

  const handlePublishOffer = async (offerId: string) => {
    const offer = offers.find(o => o.id === offerId);
    if (!offer) return;

    // Check if offer is active
    if (!offer.isActive) {
      showToast('Please activate the offer before publishing', 'error', 3000);
      return;
    }

    // Check if offer is expired
    const now = new Date();
    if (new Date(offer.validUntil) <= now) {
      showToast('Cannot publish expired offer', 'error', 3000);
      return;
    }

    Alert.alert(
      'Publish Offer',
      `Send push notifications to applicable customers for "${offer.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Publish',
          style: 'default',
          onPress: async () => {
            try {
              console.log('📢 OffersScreen: Publishing offer:', offerId);

              // Add to publishing set to show loading state
              setPublishingOffers(prev => new Set(prev).add(offerId));

              await offerService.publishOffer(offerId);

              showToast('Offer published! Notifications sent to customers.', 'success', 4000);
              console.log('✅ OffersScreen: Offer published successfully');
            } catch (error: any) {
              console.error('❌ OffersScreen: Error publishing offer:', error);
              showToast(error.message || 'Failed to publish offer', 'error', 3000);
            } finally {
              // Remove from publishing set
              setPublishingOffers(prev => {
                const newSet = new Set(prev);
                newSet.delete(offerId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  // Get statistics
  const getOfferStats = () => {
    const total = offers.length;
    const active = offers.filter(o => o.isActive).length;
    const inactive = offers.filter(o => !o.isActive).length;
    const totalUsage = offers.reduce((sum, o) => sum + o.usedCount, 0);

    return { total, active, inactive, totalUsage };
  };

  const stats = getOfferStats();

  // Helper functions for display
  const formatDate = (date: string | Date | undefined): string => {
    if (!date) return 'N/A';
    try {
      return new Date(date).toLocaleDateString();
    } catch (error) {
      console.warn('Error formatting date:', date, error);
      return 'Invalid Date';
    }
  };

  const getDiscountDisplay = (item: Offer) => {
    switch (item.discountType) {
      case 'PERCENTAGE':
        return `${item.discountValue}%`;
      case 'FIXED_AMOUNT':
        return `$${item.discountValue}`;
      case 'BUY_ONE_GET_ONE':
        return 'BOGO';
      case 'FREE_SERVICE':
        return 'FREE';
      default:
        return `${item.discountValue}%`;
    }
  };

  const getCustomerTypeDisplay = (customerType: string) => {
    switch (customerType) {
      case 'NEW': return '🆕 New Customers';
      case 'EXISTING': return '👥 Existing Customers';
      case 'VIP': return '⭐ VIP Customers';
      case 'LOYALTY_TIER': return '🏆 Loyalty Members';
      case 'ALL': return '👤 All Customers';
      default: return '👤 All Customers';
    }
  };

  const getValidDaysDisplay = (validDays: string[]) => {
    if (validDays.includes('ALL')) return 'All Days';
    if (validDays.length === 7) return 'All Days';
    if (validDays.includes('SATURDAY') && validDays.includes('SUNDAY') && validDays.length === 2) {
      return 'Weekends';
    }
    if (validDays.length === 5 && !validDays.includes('SATURDAY') && !validDays.includes('SUNDAY')) {
      return 'Weekdays';
    }
    return validDays.join(', ');
  };

  const getServiceDisplay = (item: Offer) => {
    if (item.applicableServices.includes('all')) return 'All Services';
    if (item.serviceCategories.includes('ALL')) return 'All Services';
    if (item.serviceCategories.length > 0) {
      return item.serviceCategories.join(', ');
    }
    return `${item.applicableServices.length} Service(s)`;
  };

  // Render enhanced offer item
  const renderOfferItem = ({ item }: { item: Offer }) => (
    <View style={styles.offerCard}>
      {/* Header with title, status, and customer type */}
      <View style={styles.offerHeader}>
        <View style={styles.offerTitleContainer}>
          <DarkTitle level={4} style={styles.offerTitle}>
            {item.title}
          </DarkTitle>
          <View style={styles.badgeContainer}>
            <View style={[
              styles.statusBadge,
              { backgroundColor: item.isActive ? Colors.success : Colors.gray400 }
            ]}>
              <DarkBody size="small" style={styles.statusText}>
                {item.isActive ? 'Active' : 'Inactive'}
              </DarkBody>
            </View>
            <View style={[styles.customerTypeBadge, { backgroundColor: Colors.info + '20' }]}>
              <DarkBody size="small" style={[styles.statusText, { color: Colors.info }]}>
                {getCustomerTypeDisplay(item.customerType)}
              </DarkBody>
            </View>
          </View>
        </View>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => handleEditOffer(item)}
        >
          <Icon name="edit" size={20} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      <DarkBody size="medium" style={styles.offerDescription}>
        {item.description}
      </DarkBody>

      {/* Enhanced offer details */}
      <View style={styles.offerDetails}>
        <View style={styles.discountContainer}>
          <LinearGradient
            colors={[Colors.primary + '20', Colors.primary + '10']}
            style={styles.discountBadge}
          >
            <DarkTitle level={3} style={styles.discountValue}>
              {getDiscountDisplay(item)}
            </DarkTitle>
            <DarkBody size="small" style={styles.discountLabel}>
              {item.discountType === 'BUY_ONE_GET_ONE' ? 'OFFER' : 'OFF'}
            </DarkBody>
          </LinearGradient>
        </View>

        <View style={styles.offerInfo}>
          <View style={styles.infoRow}>
            <Icon name="confirmation-number" size={14} color={Colors.textSecondary} />
            <DarkBody size="small" style={styles.infoText}>
              {item.code}
            </DarkBody>
          </View>
          <View style={styles.infoRow}>
            <Icon name="people" size={14} color={Colors.textSecondary} />
            <DarkBody size="small" style={styles.infoText}>
              {item.usedCount}/{item.usageLimit || '∞'}
            </DarkBody>
          </View>
          <View style={styles.infoRow}>
            <Icon name="schedule" size={14} color={Colors.textSecondary} />
            <DarkBody size="small" style={styles.infoText}>
              {formatDate(item.validUntil)}
            </DarkBody>
          </View>
        </View>
      </View>

      {/* Service and timing details */}
      <View style={styles.detailsSection}>
        <View style={styles.detailRow}>
          <Icon name="design-services" size={14} color={Colors.primary} />
          <DarkBody size="small" style={styles.detailText}>
            {getServiceDisplay(item)}
          </DarkBody>
        </View>
        <View style={styles.detailRow}>
          <Icon name="event" size={14} color={Colors.primary} />
          <DarkBody size="small" style={styles.detailText}>
            {getValidDaysDisplay(item.validDays)}
          </DarkBody>
        </View>
        {item.validTimeSlots.length > 0 && (
          <View style={styles.detailRow}>
            <Icon name="access-time" size={14} color={Colors.primary} />
            <DarkBody size="small" style={styles.detailText}>
              {item.validTimeSlots[0].startTime} - {item.validTimeSlots[0].endTime}
            </DarkBody>
          </View>
        )}
        {item.requiresAdvanceBooking && (
          <View style={styles.detailRow}>
            <Icon name="schedule" size={14} color={Colors.warning} />
            <DarkBody size="small" style={[styles.detailText, { color: Colors.warning }]}>
              Book {item.requiresAdvanceBooking}h in advance
            </DarkBody>
          </View>
        )}
      </View>

      {/* Advanced features indicators */}
      <View style={styles.featuresSection}>
        {item.autoApply && (
          <View style={styles.featureTag}>
            <Icon name="auto-awesome" size={12} color={Colors.success} />
            <DarkBody size="small" style={[styles.featureText, { color: Colors.success }]}>
              Auto Apply
            </DarkBody>
          </View>
        )}
        {item.combinableWithOtherOffers && (
          <View style={styles.featureTag}>
            <Icon name="layers" size={12} color={Colors.info} />
            <DarkBody size="small" style={[styles.featureText, { color: Colors.info }]}>
              Combinable
            </DarkBody>
          </View>
        )}
        {item.frequency === 'RECURRING' && (
          <View style={styles.featureTag}>
            <Icon name="repeat" size={12} color={Colors.primary} />
            <DarkBody size="small" style={[styles.featureText, { color: Colors.primary }]}>
              Recurring
            </DarkBody>
          </View>
        )}
        {item.cooldownPeriod && (
          <View style={styles.featureTag}>
            <Icon name="timer" size={12} color={Colors.warning} />
            <DarkBody size="small" style={[styles.featureText, { color: Colors.warning }]}>
              {item.cooldownPeriod}d cooldown
            </DarkBody>
          </View>
        )}
      </View>

      {/* Action buttons */}
      <View style={styles.offerActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.viewButton]}
          onPress={() => handleViewOffer(item)}
        >
          <Icon name="visibility" size={14} color={Colors.white} />
          <DarkBody size="small" style={styles.actionButtonText}>
            View
          </DarkBody>
        </TouchableOpacity>

        {/* Publish button - only show for active, non-expired offers */}
        {item.isActive && new Date(item.validUntil) > new Date() && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.publishButton,
              publishingOffers.has(item.id) && styles.publishButtonLoading
            ]}
            onPress={() => handlePublishOffer(item.id)}
            disabled={publishingOffers.has(item.id)}
          >
            {publishingOffers.has(item.id) ? (
              <Icon name="hourglass-empty" size={14} color={Colors.white} />
            ) : (
              <Icon name="notifications" size={14} color={Colors.white} />
            )}
            <DarkBody size="small" style={styles.actionButtonText}>
              {publishingOffers.has(item.id) ? 'Publishing...' : 'Publish'}
            </DarkBody>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, styles.toggleButton]}
          onPress={() => handleToggleOfferStatus(item.id, !item.isActive)}
        >
          <Icon
            name={item.isActive ? 'pause' : 'play-arrow'}
            size={14}
            color={Colors.white}
          />
          <DarkBody size="small" style={styles.actionButtonText}>
            {item.isActive ? 'Pause' : 'Activate'}
          </DarkBody>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteOffer(item.id)}
        >
          <Icon name="delete" size={14} color={Colors.white} />
          <DarkBody size="small" style={styles.actionButtonText}>
            Delete
          </DarkBody>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!selectedSalon) {
    return (
      <View style={styles.noSalonContainer}>
        <Icon name="business" size={width * 0.2} color={Colors.gray400} />
        <DarkTitle level={3} textAlign="center" style={styles.noSalonText}>
          No salon selected
        </DarkTitle>
        <DarkBody size="medium" textAlign="center" style={styles.noSalonSubtext}>
          Please select a salon to manage offers
        </DarkBody>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Gradients.purplePrimary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <View style={styles.headerLeft}>
              <WhiteTitle level={1} style={styles.headerTitle}>Offers</WhiteTitle>
              <View style={styles.salonInfo}>
                <Icon name="business" size={16} color={Colors.white} style={styles.salonIcon} />
                <DarkBody size="medium" style={styles.salonName}>
                  {selectedSalon.name}
                </DarkBody>
              </View>
            </View>
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddOffer}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                style={styles.addButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Icon name="add" size={20} color={Colors.white} />
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Stats Cards */}
          <View style={styles.statsContainer}>
            <View style={styles.statsRow}>
              <View style={[styles.statItem, styles.totalCard]}>
                <Icon name="local-offer" size={16} color={Colors.white} />
                <WhiteTitle level={4} style={styles.statNumber}>{stats.total}</WhiteTitle>
                <DarkBody size="small" style={styles.statLabel}>Total</DarkBody>
              </View>

              <View style={[styles.statItem, styles.activeCard]}>
                <Icon name="check-circle" size={16} color={Colors.white} />
                <WhiteTitle level={4} style={styles.statNumber}>{stats.active}</WhiteTitle>
                <DarkBody size="small" style={styles.statLabel}>Active</DarkBody>
              </View>

              <View style={[styles.statItem, styles.inactiveCard]}>
                <Icon name="pause-circle-filled" size={16} color={Colors.white} />
                <WhiteTitle level={4} style={styles.statNumber}>{stats.inactive}</WhiteTitle>
                <DarkBody size="small" style={styles.statLabel}>Inactive</DarkBody>
              </View>

              <View style={[styles.statItem, styles.usageCard]}>
                <Icon name="trending-up" size={16} color={Colors.white} />
                <WhiteTitle level={4} style={styles.statNumber}>{stats.totalUsage}</WhiteTitle>
                <DarkBody size="small" style={styles.statLabel}>Uses</DarkBody>
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>

      {/* Offers List */}
      <View style={styles.listWrapper}>
        {offers.length > 0 && (
          <View style={styles.listHeader}>
            <DarkTitle level={4} style={styles.listTitle}>
              Your Offers ({offers.length})
            </DarkTitle>
          </View>
        )}
        
        <FlatList
          data={offers}
          renderItem={renderOfferItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
              progressBackgroundColor={Colors.white}
            />
          }
          ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
          ListEmptyComponent={
            !loading ? (
              <View style={styles.emptyContainer}>
                <LinearGradient
                  colors={[Colors.primary + '20', Colors.primary + '10']}
                  style={styles.emptyIconContainer}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Icon name="local-offer" size={width * 0.12} color={Colors.primary} />
                </LinearGradient>
                <DarkTitle level={3} textAlign="center" style={styles.emptyTitle}>
                  No offers found
                </DarkTitle>
                <DarkBody size="medium" textAlign="center" style={styles.emptyText}>
                  Create your first offer to attract customers and boost sales.
                </DarkBody>
                <TouchableOpacity
                  style={styles.addFirstButton}
                  onPress={handleAddOffer}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={Gradients.purplePrimary}
                    style={styles.addFirstButtonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Icon name="add" size={20} color={Colors.white} />
                    <WhiteTitle level={4} style={styles.addFirstButtonText}>
                      Create First Offer
                    </WhiteTitle>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            ) : null
          }
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  noSalonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.08,
    backgroundColor: Colors.background,
  },
  noSalonText: {
    marginTop: height * 0.02,
    color: Colors.textPrimary,
  },
  noSalonSubtext: {
    marginTop: height * 0.01,
    color: Colors.textSecondary,
  },
  header: {
    paddingTop: height * 0.06,
    paddingBottom: height * 0.03,
  },
  headerContent: {
    paddingHorizontal: width * 0.05,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: height * 0.025,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    letterSpacing: -0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  salonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: height * 0.008,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: width * 0.03,
    paddingVertical: height * 0.008,
    borderRadius: width * 0.04,
  },
  salonIcon: {
    marginRight: width * 0.02,
    opacity: 0.9,
  },
  salonName: {
    color: Colors.white,
    fontWeight: '600',
    fontSize: 15,
  },
  addButton: {
    backgroundColor: 'transparent',
  },
  addButtonGradient: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    marginTop: height * 0.015,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: width * 0.02,
    paddingVertical: height * 0.015,
    paddingHorizontal: width * 0.02,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  totalCard: {
    // No border needed for inline style
  },
  activeCard: {
    // No border needed for inline style
  },
  inactiveCard: {
    // No border needed for inline style
  },
  usageCard: {
    // No border needed for inline style
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    marginTop: height * 0.002,
  },
  statLabel: {
    color: Colors.white,
    opacity: 0.9,
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: height * 0.002,
  },
  listWrapper: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
  },
  listHeader: {
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  listTitle: {
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  listContainer: {
    paddingHorizontal: width * 0.05,
    paddingTop: height * 0.015,
    paddingBottom: height * 0.1,
  },
  itemSeparator: {
    height: height * 0.015,
  },
  offerCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.04,
    padding: width * 0.04,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  offerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: height * 0.01,
  },
  offerTitleContainer: {
    flex: 1,
    marginRight: width * 0.02,
  },
  offerTitle: {
    color: Colors.textPrimary,
    fontWeight: '600',
    marginBottom: height * 0.005,
  },
  badgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: width * 0.015,
    marginTop: height * 0.005,
  },
  statusBadge: {
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.003,
    borderRadius: width * 0.02,
  },
  customerTypeBadge: {
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.003,
    borderRadius: width * 0.02,
    borderWidth: 1,
    borderColor: Colors.info + '30',
  },
  statusText: {
    color: Colors.white,
    fontSize: 10,
    fontWeight: '600',
  },
  editButton: {
    padding: width * 0.01,
  },
  offerDescription: {
    color: Colors.textSecondary,
    marginBottom: height * 0.015,
    lineHeight: 20,
  },
  offerDetails: {
    flexDirection: 'row',
    marginBottom: height * 0.015,
  },
  discountContainer: {
    marginRight: width * 0.04,
  },
  discountBadge: {
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.015,
    borderRadius: width * 0.03,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.primary + '30',
  },
  discountValue: {
    color: Colors.primary,
    fontWeight: '700',
    fontSize: 20,
  },
  discountLabel: {
    color: Colors.primary,
    fontWeight: '600',
    fontSize: 10,
    marginTop: -2,
  },
  offerInfo: {
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.005,
  },
  infoText: {
    color: Colors.textSecondary,
    marginLeft: width * 0.02,
    fontSize: 12,
  },
  detailsSection: {
    marginTop: height * 0.015,
    paddingTop: height * 0.015,
    borderTopWidth: 1,
    borderTopColor: Colors.gray100,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.008,
  },
  detailText: {
    color: Colors.textSecondary,
    marginLeft: width * 0.02,
    fontSize: 12,
    flex: 1,
  },
  featuresSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: width * 0.02,
    marginTop: height * 0.015,
    paddingTop: height * 0.015,
    borderTopWidth: 1,
    borderTopColor: Colors.gray100,
  },
  featureTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.gray100,
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.02,
    gap: width * 0.01,
  },
  featureText: {
    fontSize: 10,
    fontWeight: '600',
  },
  offerActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: width * 0.015,
    marginTop: height * 0.015,
    paddingTop: height * 0.015,
    borderTopWidth: 1,
    borderTopColor: Colors.gray100,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width * 0.025,
    paddingVertical: height * 0.008,
    borderRadius: width * 0.02,
    gap: width * 0.01,
  },
  viewButton: {
    backgroundColor: Colors.info,
  },
  publishButton: {
    backgroundColor: Colors.success,
  },
  publishButtonLoading: {
    backgroundColor: Colors.gray400,
    opacity: 0.7,
  },
  toggleButton: {
    backgroundColor: Colors.primary,
  },
  deleteButton: {
    backgroundColor: Colors.error,
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: 11,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: height * 0.08,
    paddingHorizontal: width * 0.08,
    backgroundColor: Colors.white,
    marginHorizontal: width * 0.05,
    marginTop: height * 0.02,
    borderRadius: width * 0.04,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  emptyIconContainer: {
    width: width * 0.24,
    height: width * 0.24,
    borderRadius: width * 0.12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: height * 0.02,
  },
  emptyTitle: {
    marginTop: height * 0.01,
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  emptyText: {
    marginTop: height * 0.015,
    color: Colors.textSecondary,
    lineHeight: 22,
    textAlign: 'center',
  },
  addFirstButton: {
    marginTop: height * 0.03,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  addFirstButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: height * 0.018,
    paddingHorizontal: width * 0.08,
    borderRadius: width * 0.03,
  },
  addFirstButtonText: {
    marginLeft: width * 0.02,
    fontWeight: '600',
  },
});
