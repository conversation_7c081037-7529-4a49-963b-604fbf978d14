/**
 * Salon Owner Mobile App
 * Production-level React Native application
 *
 * @format
 */

import React, { useState } from 'react';
import { StatusBar, StyleSheet, View } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { AuthProvider } from './src/context/AuthContext';
import { ToastProvider } from './src/context/ToastContext';
import { SalonProvider } from './src/context/SalonContext';
import { NotificationProvider } from './src/context/NotificationContext';
import { CustomerSalonProvider } from './src/context/CustomerSalonContext';
import { RootNavigator } from './src/navigation/RootNavigator';
import { AuthNavigator } from './src/navigation/AuthNavigator';
import { SplashScreen } from './src/screens/SplashScreen';
import { Colors } from './src/constants/colors';

function App(): React.JSX.Element {
  // Debug flag - set to true to show LoginScreen directly
  const DEBUG_SHOW_LOGIN_DIRECTLY = false;

  // Temporarily disable splash screen to debug navigation
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  if (showSplash) {
    return <SplashScreen onFinish={handleSplashFinish} />;
  }

  // Debug mode - show AuthNavigator directly (with navigation support)
  if (DEBUG_SHOW_LOGIN_DIRECTLY) {
    return (
      <ToastProvider>
        <View style={styles.container}>
          <StatusBar
            barStyle="light-content"
            backgroundColor="transparent"
            translucent
          />
          <NavigationContainer>
            <AuthNavigator />
          </NavigationContainer>
        </View>
      </ToastProvider>
    );
  }

  return (
    <ToastProvider>
      <AuthProvider>
        <SalonProvider>
          <CustomerSalonProvider>
            <NotificationProvider>
              <View style={styles.container}>
                <StatusBar
                  barStyle="light-content"
                  backgroundColor="transparent"
                  translucent
                />
                <RootNavigator />
              </View>
            </NotificationProvider>
          </CustomerSalonProvider>
        </SalonProvider>
      </AuthProvider>
    </ToastProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});

export default App;
