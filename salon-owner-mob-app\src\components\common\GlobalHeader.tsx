import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Image,
  SafeAreaView,
  Platform,
  Text,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../../context/AuthContext';
import { useSalon } from '../../context/SalonContext';
import { useNotifications } from '../../context/NotificationContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { SalonOwnerStackParamList } from '../../types';
import { Colors } from '../../constants/colors';

const { width, height } = Dimensions.get('window');

type GlobalHeaderNavigationProp = StackNavigationProp<SalonOwnerStackParamList>;

interface GlobalHeaderProps {
  showBackButton?: boolean;
  onBackPress?: () => void;
  backgroundColor?: string;
  statusBarStyle?: 'light-content' | 'dark-content';
}

export const GlobalHeader: React.FC<GlobalHeaderProps> = ({
  showBackButton = false,
  onBackPress,
  backgroundColor = Colors.background,
  statusBarStyle = 'dark-content',
}) => {
  const { user } = useAuth();
  const { selectedSalon, isLoading: salonLoading } = useSalon();
  const { notificationCount } = useNotifications();
  const navigation = useNavigation<GlobalHeaderNavigationProp>();

  const handleNotificationPress = () => {
    console.log('🔔 Notification pressed');
    navigation.navigate('Notifications');
  };

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar barStyle={statusBarStyle} backgroundColor={backgroundColor} />

      {/* Safe Area for Status Bar */}
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          {/* Left Side - Back Button and Logo */}
          <View style={styles.leftSection}>
            {showBackButton && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={onBackPress}
                activeOpacity={0.7}
              >
                <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
              </TouchableOpacity>
            )}

            {/* Salon Logo for ADMIN and STAFF users */}
            {(user?.role === 'ADMIN' || user?.role === 'STAFF') && selectedSalon?.logo && !salonLoading && (
              <TouchableOpacity
                style={styles.salonLogoContainer}
                activeOpacity={0.8}
              >
                <Image
                  source={{ uri: selectedSalon.logo }}
                  style={styles.salonLogo}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            )}

            {/* App Logo for CUSTOMER users */}
            {user?.role === 'CUSTOMER' && (
              <View style={styles.appLogoContainer}>
                <Icon name="content-cut" size={width * 0.055} color={Colors.primary} />
              </View>
            )}
          </View>

          {/* Right Side - Notification Only */}
          <View style={styles.rightSection}>
            {/* Notification Button */}
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={handleNotificationPress}
              activeOpacity={0.7}
            >
              <View style={styles.notificationIconContainer}>
                <Icon name="notifications" size={22} color={Colors.textPrimary} />
                {/* Notification Badge */}
                {notificationCount.unread > 0 && (
                  <View style={styles.notificationBadge}>
                    <Text style={styles.notificationBadgeText}>
                      {notificationCount.unread > 99 ? '99+' : notificationCount.unread}
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: 1000, // Ensure it's above other content
    backgroundColor: Colors.gray700,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.textSecondary}10`,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
    marginTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight,
  },
  safeArea: {
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Math.max(width * 0.04, 16),
    paddingVertical: Math.max(height * 0.012, 8),
    minHeight: Math.max(height * 0.065, 52),
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  backButton: {
    padding: Math.max(width * 0.02, 8),
    marginRight: Math.max(width * 0.025, 10),
    borderRadius: 20,
    backgroundColor: `${Colors.textSecondary}08`,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  salonLogoContainer: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  salonLogo: {
    width: Math.max(width * 0.1, 36),
    height: Math.max(width * 0.1, 36),
    borderRadius: Math.max(width * 0.0475, 18),
    borderWidth: 2,
    borderColor: Colors.white,
  },
  appLogoContainer: {
    width: Math.max(width * 0.095, 36),
    height: Math.max(width * 0.095, 36),
    borderRadius: Math.max(width * 0.0475, 18),
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationButton: {
    padding: Math.max(width * 0.025, 10),
    borderRadius: Math.max(width * 0.05, 20),
    // backgroundColor: `${Colors.textSecondary}12`,
    minWidth: Math.max(width * 0.11, 44),
    minHeight: Math.max(width * 0.11, 44),
    justifyContent: 'center',
    alignItems: 'center',
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.08,
    // shadowRadius: 2,
    // elevation: 2,
  },
  notificationIconContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: Colors.white,
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
});
