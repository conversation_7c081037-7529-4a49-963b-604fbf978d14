import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { staffController } from '../controllers/staffController';
import { protect, authorize } from '../middlewares/authMiddleware';
import multer from 'multer';


const router = Router();

// Configure multer for staff profile image upload
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (_req, file, cb) => {
    // Allow only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

/**
 * @swagger
 * tags:
 *   name: Staff Management
 *   description: Staff management operations for salon owners
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     StaffMember:
 *       type: object
 *       required:
 *         - firstName
 *         - lastName
 *         - email
 *         - password
 *         - salonId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         firstName:
 *           type: string
 *           maxLength: 50
 *         lastName:
 *           type: string
 *           maxLength: 50
 *         email:
 *           type: string
 *           format: email
 *         phoneNumber:
 *           type: string
 *           maxLength: 20
 *         password:
 *           type: string
 *           minLength: 6
 *         salonId:
 *           type: string
 *           format: uuid
 *         position:
 *           type: string
 *           maxLength: 100
 *         hourlyRate:
 *           type: number
 *           format: decimal
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *             enum: [VIEW_SERVICES, MANAGE_SERVICES, VIEW_OFFERS, MANAGE_OFFERS, VIEW_BOOKINGS, MANAGE_BOOKINGS, VIEW_CUSTOMERS, MANAGE_CUSTOMERS, VIEW_REPORTS, MANAGE_STAFF]
 *         workingHours:
 *           type: object
 *           additionalProperties:
 *             type: object
 *             properties:
 *               start:
 *                 type: string
 *                 format: time
 *               end:
 *                 type: string
 *                 format: time
 *               isWorking:
 *                 type: boolean
 *         startDate:
 *           type: string
 *           format: date
 *         notes:
 *           type: string
 *         status:
 *           type: string
 *           enum: [ACTIVE, SUSPENDED, REVOKED]
 */

// Apply authentication to all routes
router.use(protect);

/**
 * @swagger
 * /api/staff:
 *   post:
 *     summary: Create a new staff member
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/StaffMember'
 *     responses:
 *       201:
 *         description: Staff member created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.post(
  '/',
  authorize('ADMIN'),
  upload.single('profileImage'),
  [
    body('firstName')
      .notEmpty()
      .withMessage('First name is required')
      .isLength({ max: 50 })
      .withMessage('First name must be less than 50 characters'),
    body('lastName')
      .notEmpty()
      .withMessage('Last name is required')
      .isLength({ max: 50 })
      .withMessage('Last name must be less than 50 characters'),
    body('email')
      .isEmail()
      .withMessage('Valid email is required')
      .normalizeEmail(),
    body('phoneNumber')
      .optional()
      .isMobilePhone('any')
      .withMessage('Valid phone number is required'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long'),
    body('salonIds')
      .isArray({ min: 1 })
      .withMessage('At least one salon ID is required')
      .custom((salonIds) => {
        return salonIds.every((id: string) => {
          return typeof id === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
        });
      })
      .withMessage('All salon IDs must be valid UUIDs'),
    body('position')
      .optional()
      .custom((position) => {
        if (!position) return true; // Optional field

        // Define valid position enum values
        const validPositions = [
          'SALON_MANAGER', 'ASSISTANT_MANAGER', 'SUPERVISOR',
          'SENIOR_HAIR_STYLIST', 'HAIR_STYLIST', 'JUNIOR_HAIR_STYLIST', 'HAIR_COLORIST', 'BARBER',
          'ESTHETICIAN', 'MAKEUP_ARTIST', 'EYEBROW_SPECIALIST', 'LASH_TECHNICIAN',
          'NAIL_TECHNICIAN', 'NAIL_ARTIST',
          'MASSAGE_THERAPIST', 'SPA_THERAPIST',
          'RECEPTIONIST', 'ASSISTANT', 'APPRENTICE', 'CLEANER',
          'FREELANCER', 'CONSULTANT', 'TRAINER', 'OTHER'
        ];

        return validPositions.includes(position);
      })
      .withMessage('Invalid position provided'),
    body('hourlyRate')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Hourly rate must be a positive number'),
    body('permissions')
      .optional()
      .isArray()
      .withMessage('Permissions must be an array')
      .custom((permissions) => {
        // Define valid permission codes (these should match the Permission entity)
        const validPermissions = [
          'VIEW_SERVICES', 'MANAGE_SERVICES',
          'VIEW_OFFERS', 'MANAGE_OFFERS',
          'VIEW_BOOKINGS', 'MANAGE_BOOKINGS',
          'VIEW_CUSTOMERS', 'MANAGE_CUSTOMERS',
          'VIEW_REPORTS', 'MANAGE_REPORTS',
          'VIEW_STAFF', 'MANAGE_STAFF',
          'VIEW_SALON_SETTINGS', 'MANAGE_SALON_SETTINGS'
        ];
        return permissions.every((p: string) => validPermissions.includes(p));
      })
      .withMessage('Invalid permissions provided'),
    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Valid start date is required'),
  ],
  staffController.createStaff
);

/**
 * @swagger
 * /api/staff/salon/{salonId}:
 *   get:
 *     summary: Get staff members for a salon
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: salonId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [ACTIVE, SUSPENDED, REVOKED]
 *       - in: query
 *         name: position
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [firstName, lastName, position, startDate, createdAt]
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Staff members retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.get(
  '/salon/:salonId',
  authorize('ADMIN', 'STAFF'),
  [
    param('salonId')
      .isUUID()
      .withMessage('Valid salon ID is required'),
  ],
  staffController.getStaffBySalon
);

/**
 * @swagger
 * /api/staff/permissions:
 *   get:
 *     summary: Get available staff permissions
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Available permissions retrieved successfully
 */
router.get(
  '/permissions',
  authorize('ADMIN'),
  staffController.getAvailablePermissions
);

/**
 * @swagger
 * /api/staff/{staffId}:
 *   get:
 *     summary: Get staff member by ID
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: staffId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: salonId
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Staff member retrieved successfully
 *       404:
 *         description: Staff member not found
 */
router.get(
  '/:staffId',
  authorize('ADMIN', 'STAFF'),
  [
    param('staffId')
      .isUUID()
      .withMessage('Valid staff ID is required'),
  ],
  staffController.getStaffById
);

/**
 * @swagger
 * /api/staff/{staffId}:
 *   put:
 *     summary: Update staff member
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: staffId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/StaffMember'
 *     responses:
 *       200:
 *         description: Staff member updated successfully
 *       404:
 *         description: Staff member not found
 */
router.put(
  '/:staffId',
  authorize('ADMIN'),
  upload.single('profileImage'),
  [
    param('staffId')
      .isUUID()
      .withMessage('Valid staff ID is required'),
    body('salonId')
      .isUUID()
      .withMessage('Valid salon ID is required'),
  ],
  staffController.updateStaff
);

/**
 * @swagger
 * /api/staff/{staffId}:
 *   delete:
 *     summary: Delete/deactivate staff member
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: staffId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - salonId
 *             properties:
 *               salonId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Staff member deleted successfully
 *       404:
 *         description: Staff member not found
 */
router.delete(
  '/:staffId',
  authorize('ADMIN'),
  [
    param('staffId')
      .isUUID()
      .withMessage('Valid staff ID is required'),
    body('salonId')
      .isUUID()
      .withMessage('Valid salon ID is required'),
  ],
  staffController.deleteStaff
);

/**
 * @swagger
 * /api/staff/my/salons:
 *   get:
 *     summary: Get my accessible salons (for authenticated staff)
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: My salons retrieved successfully
 */
router.get(
  '/my/salons/:staffId',
  authorize('STAFF'),
  staffController.getMySalons
);

/**
 * @swagger
 * /api/staff/{staffId}/salons:
 *   get:
 *     summary: Get salons accessible by staff member
 *     tags: [Staff Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: staffId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Staff salons retrieved successfully
 */
router.get(
  '/:staffId/salons',
  authorize('ADMIN', 'STAFF'),
  [
    param('staffId')
      .isUUID()
      .withMessage('Valid staff ID is required'),
  ],
  staffController.getStaffSalons
);

export default router;
