# C/C++ build system timings
generate_cxx_metadata
  [gap of 55ms]
  create-invalidation-state 100ms
  [gap of 30ms]
generate_cxx_metadata completed in 185ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 17ms
  [gap of 10ms]
generate_cxx_metadata completed in 42ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 38ms
  [gap of 15ms]
generate_cxx_metadata completed in 71ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 37ms]
  create-invalidation-state 32ms
  [gap of 17ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 104ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 29ms
  [gap of 17ms]
generate_cxx_metadata completed in 62ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 10ms]
generate_cxx_metadata completed in 23ms

