import { Request, Response } from 'express';
import { customerSalonAccessService } from '../services/customerSalonAccessService';
import logger from '../utils/logger';

class CustomerSalonAccessController {
  // Get customer's accessible salons
  async getCustomerSalons(req: Request, res: Response): Promise<void> {
    try {
       const { customerId } = req.params;
    console.log("customerId",customerId);
    
      if (!customerId) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
        return;
      }

      console.log('🔍 CustomerSalonAccess: Getting salons for customer:', customerId);

      const salons = await customerSalonAccessService.getCustomerSalons(customerId);

      console.log('✅ CustomerSalonAccess: Found', salons.length, 'accessible salons');

      res.status(200).json({
        success: true,
        data: {
          data: salons,
          count: salons.length,
        }
      });
    } catch (error: any) {
      console.error('❌ CustomerSalonAccess: Error getting customer salons:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Error fetching customer salons'
      });
    }
  }

  // Get salon's customers (for salon owners/admins)
  async getSalonCustomers(req: Request, res: Response): Promise<void> {
    try {
      const { salonId } = req.params;

      if (!salonId) {
        res.status(400).json({
          success: false,
          error: 'Salon ID is required'
        });
        return;
      }

      console.log('🔍 CustomerSalonAccess: Getting customers for salon:', salonId);

      const customers = await customerSalonAccessService.getSalonCustomers(salonId);

      console.log('✅ CustomerSalonAccess: Found', customers.length, 'customers');

      res.status(200).json({
        success: true,
        data: {
          data: customers,
          count: customers.length,
        }
      });
    } catch (error: any) {
      console.error('❌ CustomerSalonAccess: Error getting salon customers:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Error fetching salon customers'
      });
    }
  }

  // Grant customer access to a salon
  async grantAccess(req: Request, res: Response): Promise<void> {
    try {
      const { customerId, salonId, expiresAt, notes, settings } = req.body;

      if (!customerId || !salonId) {
        res.status(400).json({
          success: false,
          error: 'Customer ID and Salon ID are required'
        });
        return;
      }

      console.log('🔐 CustomerSalonAccess: Granting access:', { customerId, salonId });

      const access = await customerSalonAccessService.grantAccess({
        customerId,
        salonId,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        notes,
        settings,
      });

      console.log('✅ CustomerSalonAccess: Access granted:', access.id);

      res.status(201).json({
        success: true,
        data: {
          data: access
        }
      });
    } catch (error: any) {
      console.error('❌ CustomerSalonAccess: Error granting access:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Error granting salon access'
      });
    }
  }

  // Revoke customer access to a salon
  async revokeAccess(req: Request, res: Response): Promise<void> {
    try {
      const { customerId, salonId } = req.body;

      if (!customerId || !salonId) {
        res.status(400).json({
          success: false,
          error: 'Customer ID and Salon ID are required'
        });
        return;
      }

      console.log('🚫 CustomerSalonAccess: Revoking access:', { customerId, salonId });

      await customerSalonAccessService.revokeAccess(customerId, salonId);

      console.log('✅ CustomerSalonAccess: Access revoked');

      res.status(200).json({
        success: true,
        data: {
          message: 'Salon access revoked successfully'
        }
      });
    } catch (error: any) {
      console.error('❌ CustomerSalonAccess: Error revoking access:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Error revoking salon access'
      });
    }
  }

  // Check if customer has access to a specific salon
  async checkAccess(req: Request, res: Response): Promise<void> {
    try {
      const customerId = req.user?.id;
      const { salonId } = req.params;

      if (!customerId) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
        return;
      }

      if (!salonId) {
        res.status(400).json({
          success: false,
          error: 'Salon ID is required'
        });
        return;
      }

      console.log('🔍 CustomerSalonAccess: Checking access:', { customerId, salonId });

      const hasAccess = await customerSalonAccessService.hasAccess(customerId, salonId);

      console.log('✅ CustomerSalonAccess: Access check result:', hasAccess);

      res.status(200).json({
        success: true,
        data: {
          hasAccess,
          customerId,
          salonId,
        }
      });
    } catch (error: any) {
      console.error('❌ CustomerSalonAccess: Error checking access:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Error checking salon access'
      });
    }
  }

  // Get available salons for customer registration (by account)
  async getAvailableSalons(req: Request, res: Response): Promise<void> {
    try {
      const { accountId } = req.query;

      if (!accountId) {
        res.status(400).json({
          success: false,
          error: 'Account ID is required'
        });
        return;
      }

      console.log('🔍 CustomerSalonAccess: Getting available salons for account:', accountId);

      // Import salon service to get salons by account
      const { salonService } = await import('../services/salonService');
      const salons = await salonService.findByAccountId(accountId as string);

      console.log('✅ CustomerSalonAccess: Found', salons.length, 'available salons');

      res.status(200).json({
        success: true,
        data: {
          data: salons,
          count: salons.length,
        }
      });
    } catch (error: any) {
      console.error('❌ CustomerSalonAccess: Error getting available salons:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Error fetching available salons'
      });
    }
  }
}

export const customerSalonAccessController = new CustomerSalonAccessController();

// Export individual methods for route binding
export const {
  getCustomerSalons,
  getSalonCustomers,
  grantAccess,
  revokeAccess,
  checkAccess,
  getAvailableSalons,
} = customerSalonAccessController;

