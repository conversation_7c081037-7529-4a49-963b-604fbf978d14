import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Image,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useToast } from '../../context/ToastContext';
import { useCustomerSalon } from '../../context/CustomerSalonContext';
import {
  DarkTitle,
  DarkBody,
} from '../../components/common/Typography';
import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { Colors, Gradients } from '../../constants/colors';
import { customerServiceService, ServiceCategory, CustomerServiceFilters } from '../../services/customerServiceService';
import { ServiceResponse, formatPrice, formatDuration } from '../../types/service';

const { width, height } = Dimensions.get('window');

interface RouteParams {
  category?: string;
  search?: string;
}

export const ServicesScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { showToast } = useToast();
  const { selectedSalon: customerSalon } = useCustomerSalon();
  const { category, search } = (route.params as RouteParams) || {};

  // State
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [services, setServices] = useState<ServiceResponse[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>(category || 'ALL');
  const [searchQuery, setSearchQuery] = useState<string>(search || '');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'duration' | 'popularity'>('name');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('ASC');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalServices, setTotalServices] = useState(0);

  // Load data
  const loadData = useCallback(async (showLoading = true, reset = true) => {
    if (!customerSalon?.salon?.id) return;

    try {
      if (showLoading) setLoading(true);

      console.log('🔍 ServicesScreen: Loading services for salon:', customerSalon.salon.id);

      // Load categories if not loaded
      if (categories.length === 0) {
        const categoriesData = await customerServiceService.getServiceCategories(customerSalon.salon.id);
        setCategories([{ id: 'ALL', name: 'All', icon: 'apps', color: Colors.primary, serviceCount: 0 }, ...categoriesData]);
      }

      // Prepare filters
      const filters: CustomerServiceFilters = {
        sortBy,
        sortOrder,
        limit: 20,
        offset: reset ? 0 : (page - 1) * 20,
      };

      if (selectedCategory !== 'ALL') {
        filters.category = selectedCategory;
      }

      if (searchQuery.trim()) {
        filters.search = searchQuery.trim();
      }

      // Load services
      const result = await customerServiceService.getServicesBySalon(customerSalon.salon.id, filters);

      if (reset) {
        setServices(result.data);
        setPage(2);
      } else {
        setServices(prev => [...prev, ...result.data]);
        setPage(prev => prev + 1);
      }

      setTotalServices(result.total);
      setHasMore(result.data.length === 20);

      console.log('✅ ServicesScreen: Loaded services:', result.data.length);
    } catch (error: any) {
      console.error('❌ ServicesScreen: Error loading services:', error);
      showToast('Failed to load services', 'error', 3000);
    } finally {
      if (showLoading) setLoading(false);
      setRefreshing(false);
    }
  }, [customerSalon, selectedCategory, searchQuery, sortBy, sortOrder, page, categories.length]);

  // Focus effect to reload data
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Reload when filters change
  useEffect(() => {
    if (!loading) {
      setPage(1);
      loadData(false, true);
    }
  }, [selectedCategory, searchQuery, sortBy, sortOrder]);

  const handleRefresh = () => {
    setRefreshing(true);
    setPage(1);
    loadData(false, true);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadData(false, false);
    }
  };

  const handleServicePress = (service: ServiceResponse) => {
    console.log('📱 Service pressed:', service.name);
    (navigation as any).navigate('ServiceDetails', { serviceId: service.id });
  };

  const handleCategoryPress = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleSortPress = () => {
    // Toggle sort order or change sort type
    if (sortBy === 'name') {
      setSortBy('price');
      setSortOrder('ASC');
    } else if (sortBy === 'price' && sortOrder === 'ASC') {
      setSortOrder('DESC');
    } else if (sortBy === 'price' && sortOrder === 'DESC') {
      setSortBy('duration');
      setSortOrder('ASC');
    } else {
      setSortBy('name');
      setSortOrder('ASC');
    }
  };

  const renderCategory = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={[
        styles.categoryChip,
        selectedCategory === item.id && styles.categoryChipSelected
      ]}
      onPress={() => handleCategoryPress(item.id)}
      activeOpacity={0.7}
    >
      <Icon
        name={item.icon}
        size={16}
        color={selectedCategory === item.id ? Colors.white : item.color}
      />
      <DarkBody
        size="small"
        style={[
          styles.categoryText,
          selectedCategory === item.id && styles.categoryTextSelected
        ]}
      >
        {item.name}
      </DarkBody>
    </TouchableOpacity>
  );

  const renderService = ({ item }: { item: ServiceResponse }) => (
    <TouchableOpacity
      style={styles.serviceCard}
      onPress={() => handleServicePress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.serviceContent}>
        {item.image && (
          <Image source={{ uri: item.image }} style={styles.serviceImage} />
        )}
        <View style={styles.serviceInfo}>
          <DarkTitle level={4} style={styles.serviceName}>
            {item.name}
          </DarkTitle>
          <DarkBody size="small" style={styles.serviceCategory}>
            {item.category}
          </DarkBody>
          {item.description && (
            <DarkBody size="small" style={styles.serviceDescription} numberOfLines={2}>
              {item.description}
            </DarkBody>
          )}
          <View style={styles.serviceDetails}>
            <View style={styles.priceSection}>
              <DarkTitle level={4} style={styles.servicePrice}>
                {formatPrice(item)}
              </DarkTitle>
              <DarkBody size="small" style={styles.serviceDuration}>
                {formatDuration(item)}
              </DarkBody>
            </View>
            {item.isBookable && (
              <TouchableOpacity style={styles.bookButton}>
                <DarkBody size="small" style={styles.bookButtonText}>
                  Book
                </DarkBody>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderFooter = () => {
    if (!hasMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={Colors.primary} />
      </View>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Icon name="search-off" size={width * 0.15} color={Colors.gray400} />
      <DarkTitle level={3} textAlign="center" style={styles.emptyTitle}>
        No services found
      </DarkTitle>
      <DarkBody size="medium" textAlign="center" style={styles.emptyText}>
        {searchQuery ? 
          `No services match "${searchQuery}"` : 
          selectedCategory !== 'ALL' ? 
            `No services in ${selectedCategory} category` :
            'No services available'
        }
      </DarkBody>
    </View>
  );

  if (!customerSalon?.salon) {
    return (
      <ScreenWrapper>
        <View style={styles.noSalonContainer}>
          <Icon name="business" size={width * 0.2} color={Colors.gray400} />
          <DarkTitle level={3} textAlign="center" style={styles.noSalonText}>
            No salon selected
          </DarkTitle>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
          </TouchableOpacity>
          <View style={styles.headerTitle}>
            <DarkTitle level={3} style={styles.title}>
              Services
            </DarkTitle>
            <DarkBody size="small" style={styles.subtitle}>
              {totalServices} services available
            </DarkBody>
          </View>
          <TouchableOpacity style={styles.sortButton} onPress={handleSortPress}>
            <Icon name="sort" size={24} color={Colors.textPrimary} />
          </TouchableOpacity>
        </View>

        {/* Categories */}
        {categories.length > 0 && (
          <View style={styles.categoriesSection}>
            <FlatList
              data={categories}
              renderItem={renderCategory}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesContainer}
            />
          </View>
        )}

        {/* Services List */}
        <FlatList
          data={services}
          renderItem={renderService}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.servicesContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={!loading ? renderEmpty : null}
        />

        {loading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={Colors.primary} />
          </View>
        )}
      </View>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  noSalonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  noSalonText: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    marginLeft: width * 0.03,
  },
  title: {
    color: Colors.textPrimary,
  },
  subtitle: {
    color: Colors.textSecondary,
  },
  sortButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesSection: {
    backgroundColor: Colors.white,
    paddingVertical: height * 0.015,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  categoriesContainer: {
    paddingHorizontal: width * 0.05,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width * 0.03,
    paddingVertical: height * 0.01,
    marginRight: width * 0.02,
    borderRadius: width * 0.05,
    backgroundColor: Colors.gray100,
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  categoryChipSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryText: {
    color: Colors.textPrimary,
    marginLeft: width * 0.015,
  },
  categoryTextSelected: {
    color: Colors.white,
  },
  servicesContainer: {
    padding: width * 0.05,
  },
  serviceCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    marginBottom: height * 0.02,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceContent: {
    flexDirection: 'row',
    padding: width * 0.04,
  },
  serviceImage: {
    width: width * 0.2,
    height: width * 0.2,
    borderRadius: width * 0.02,
    marginRight: width * 0.03,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  serviceCategory: {
    color: Colors.textSecondary,
    marginBottom: height * 0.01,
  },
  serviceDescription: {
    color: Colors.textSecondary,
    marginBottom: height * 0.015,
    lineHeight: 18,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceSection: {
    flex: 1,
  },
  servicePrice: {
    color: Colors.primary,
    fontWeight: '600',
  },
  serviceDuration: {
    color: Colors.textSecondary,
  },
  bookButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.01,
    borderRadius: width * 0.02,
  },
  bookButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  footerLoader: {
    paddingVertical: height * 0.02,
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
    paddingVertical: height * 0.1,
  },
  emptyTitle: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  emptyText: {
    color: Colors.textSecondary,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
