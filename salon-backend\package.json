{"name": "salon-app-backend", "version": "1.0.0", "description": "Backend for salon application with user and role management", "main": "dist/server.js", "private": true, "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "test": "jest", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d data-source.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d data-source.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d data-source.ts", "init:permissions": "ts-node src/scripts/initializePermissions.ts", "db:seed": "npm run init:permissions"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "reflect-metadata": "^0.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.20", "winston": "^3.11.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.11.13", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "nodemon": "^3.0.3", "prettier": "^3.2.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}