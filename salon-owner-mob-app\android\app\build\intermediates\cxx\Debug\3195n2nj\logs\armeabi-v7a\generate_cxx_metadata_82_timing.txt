# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 49ms
  [gap of 15ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 96ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 38ms
  [gap of 22ms]
generate_cxx_metadata completed in 80ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 26ms]
generate_cxx_metadata completed in 42ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 16ms
  [gap of 34ms]
generate_cxx_metadata completed in 65ms

