import { Repository } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { User, UserRole } from '../entities/User';
import { StaffSalonAccess, StaffAccessStatus, StaffPermission } from '../entities/StaffSalonAccess';
import { Salon } from '../entities/Salon';
import bcrypt from 'bcrypt';
import ApiError from '../utils/apiError';

export interface CreateStaffRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  password: string;
  salonId: string;
  position?: string;
  hourlyRate?: number;
  permissions: StaffPermission[];
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;
  startDate?: Date;
  notes?: string;
  grantedBy: string;
}

export interface UpdateStaffRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  position?: string;
  hourlyRate?: number;
  permissions?: StaffPermission[];
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;
  status?: StaffAccessStatus;
  notes?: string;
  lastModifiedBy: string;
}

export interface StaffSearchFilters {
  salonId?: string;
  status?: StaffAccessStatus;
  position?: string;
  search?: string;
  sortBy?: 'firstName' | 'lastName' | 'position' | 'startDate' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

class StaffService {
  private userRepository: Repository<User>;
  private staffAccessRepository: Repository<StaffSalonAccess>;
  private salonRepository: Repository<Salon>;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
    this.staffAccessRepository = AppDataSource.getRepository(StaffSalonAccess);
    this.salonRepository = AppDataSource.getRepository(Salon);
  }

  /**
   * Create a new staff member
   */
  async createStaff(data: CreateStaffRequest): Promise<{ user: User; access: StaffSalonAccess }> {
    try {
      console.log('🔍 StaffService: Creating staff member:', data.email);

      // Check if salon exists and user has permission
      const salon = await this.salonRepository.findOne({
        where: { id: data.salonId, isActive: true }
      });

      if (!salon) {
        throw new ApiError(404, 'Salon not found');
      }

      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: { email: data.email }
      });

      if (existingUser) {
        throw new ApiError(400, 'User with this email already exists');
      }

      // Create user
      const user = this.userRepository.create({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        password: data.password,
        role: UserRole.STAFF,
        accountId: salon.accountId,
        isActive: true,
      });

      const savedUser = await this.userRepository.save(user);

      // Create staff salon access
      const staffAccess = this.staffAccessRepository.create({
        staffId: savedUser.id,
        salonId: data.salonId,
        status: StaffAccessStatus.ACTIVE,
        permissions: data.permissions,
        position: data.position,
        hourlyRate: data.hourlyRate,
        workingHours: data.workingHours,
        startDate: data.startDate || new Date(),
        notes: data.notes,
        grantedBy: data.grantedBy,
        grantedAt: new Date(),
      });

      const savedAccess = await this.staffAccessRepository.save(staffAccess);

      console.log('✅ StaffService: Staff member created successfully:', savedUser.id);
      return { user: savedUser, access: savedAccess };
    } catch (error: any) {
      console.error('❌ StaffService: Error creating staff member:', error);
      throw error;
    }
  }

  /**
   * Get staff members for a salon
   */
  async getStaffBySalon(salonId: string, filters: StaffSearchFilters = {}): Promise<{
    data: Array<StaffSalonAccess & { staff: User }>;
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('🔍 StaffService: Getting staff for salon:', salonId);

      const queryBuilder = this.staffAccessRepository
        .createQueryBuilder('access')
        .leftJoinAndSelect('access.staff', 'staff')
        .leftJoinAndSelect('access.salon', 'salon')
        .where('access.salonId = :salonId', { salonId });

      // Apply filters
      if (filters.status) {
        queryBuilder.andWhere('access.status = :status', { status: filters.status });
      }

      if (filters.position) {
        queryBuilder.andWhere('access.position ILIKE :position', { position: `%${filters.position}%` });
      }

      if (filters.search) {
        queryBuilder.andWhere(
          '(staff.firstName ILIKE :search OR staff.lastName ILIKE :search OR staff.email ILIKE :search OR access.position ILIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt';
      const sortOrder = filters.sortOrder || 'DESC';
      
      if (sortBy === 'firstName' || sortBy === 'lastName') {
        queryBuilder.orderBy(`staff.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy(`access.${sortBy}`, sortOrder);
      }

      // Apply pagination
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;
      
      queryBuilder.limit(limit).offset(offset);

      const [data, total] = await queryBuilder.getManyAndCount();

      const page = Math.floor(offset / limit) + 1;
      const totalPages = Math.ceil(total / limit);

      console.log('✅ StaffService: Retrieved staff members:', data.length);
      return { data, total, page, totalPages };
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff members:', error);
      throw error;
    }
  }

  /**
   * Get staff member by ID
   */
  async getStaffById(staffId: string, salonId?: string): Promise<StaffSalonAccess & { staff: User }> {
    try {
      console.log('🔍 StaffService: Getting staff member:', staffId);

      const queryBuilder = this.staffAccessRepository
        .createQueryBuilder('access')
        .leftJoinAndSelect('access.staff', 'staff')
        .leftJoinAndSelect('access.salon', 'salon')
        .where('access.staffId = :staffId', { staffId });

      if (salonId) {
        queryBuilder.andWhere('access.salonId = :salonId', { salonId });
      }

      const staffAccess = await queryBuilder.getOne();

      if (!staffAccess) {
        throw new ApiError(404, 'Staff member not found');
      }

      console.log('✅ StaffService: Retrieved staff member:', staffAccess.staff.email);
      return staffAccess;
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff member:', error);
      throw error;
    }
  }

  /**
   * Update staff member
   */
  async updateStaff(staffId: string, salonId: string, data: UpdateStaffRequest): Promise<StaffSalonAccess & { staff: User }> {
    try {
      console.log('🔍 StaffService: Updating staff member:', staffId);

      // Get existing staff access
      const staffAccess = await this.getStaffById(staffId, salonId);

      // Update user data if provided
      if (data.firstName || data.lastName || data.email || data.phoneNumber !== undefined) {
        await this.userRepository.update(staffId, {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phoneNumber: data.phoneNumber,
        });
      }

      // Update staff access data
      await this.staffAccessRepository.update(staffAccess.id, {
        position: data.position,
        hourlyRate: data.hourlyRate,
        permissions: data.permissions,
        workingHours: data.workingHours,
        status: data.status,
        notes: data.notes,
        lastModifiedBy: data.lastModifiedBy,
      });

      // Return updated staff access
      const updatedStaffAccess = await this.getStaffById(staffId, salonId);

      console.log('✅ StaffService: Staff member updated successfully');
      return updatedStaffAccess;
    } catch (error: any) {
      console.error('❌ StaffService: Error updating staff member:', error);
      throw error;
    }
  }

  /**
   * Delete/Deactivate staff member
   */
  async deleteStaff(staffId: string, salonId: string, deletedBy: string): Promise<void> {
    try {
      console.log('🔍 StaffService: Deleting staff member:', staffId);

      const staffAccess = await this.getStaffById(staffId, salonId);

      // Update status to revoked instead of hard delete
      await this.staffAccessRepository.update(staffAccess.id, {
        status: StaffAccessStatus.REVOKED,
        endDate: new Date(),
        lastModifiedBy: deletedBy,
      });

      // Optionally deactivate user if they have no other active salon access
      const otherAccess = await this.staffAccessRepository.count({
        where: {
          staffId,
          status: StaffAccessStatus.ACTIVE,
        }
      });

      if (otherAccess === 0) {
        await this.userRepository.update(staffId, { isActive: false });
      }

      console.log('✅ StaffService: Staff member deleted successfully');
    } catch (error: any) {
      console.error('❌ StaffService: Error deleting staff member:', error);
      throw error;
    }
  }

  /**
   * Get salons accessible by staff member
   */
  async getStaffSalons(staffId: string): Promise<Array<StaffSalonAccess & { salon: Salon }>> {
    try {
      console.log('🔍 StaffService: Getting salons for staff:', staffId);

      const staffAccess = await this.staffAccessRepository
        .createQueryBuilder('access')
        .leftJoinAndSelect('access.salon', 'salon')
        .where('access.staffId = :staffId', { staffId })
        .andWhere('access.status = :status', { status: StaffAccessStatus.ACTIVE })
        .andWhere('salon.isActive = :isActive', { isActive: true })
        .getMany();

      console.log('✅ StaffService: Retrieved staff salons:', staffAccess.length);
      return staffAccess;
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff salons:', error);
      throw error;
    }
  }

  /**
   * Check if staff has permission for a salon
   */
  async checkStaffPermission(staffId: string, salonId: string, permission: StaffPermission): Promise<boolean> {
    try {
      const staffAccess = await this.staffAccessRepository.findOne({
        where: {
          staffId,
          salonId,
          status: StaffAccessStatus.ACTIVE,
        }
      });

      return staffAccess?.permissions.includes(permission) || false;
    } catch (error: any) {
      console.error('❌ StaffService: Error checking staff permission:', error);
      return false;
    }
  }
}

export const staffService = new StaffService();
