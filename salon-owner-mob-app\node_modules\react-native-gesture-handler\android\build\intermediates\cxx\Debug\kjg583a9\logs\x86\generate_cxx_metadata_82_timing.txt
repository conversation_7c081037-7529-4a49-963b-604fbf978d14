# C/C++ build system timings
generate_cxx_metadata
  [gap of 44ms]
  create-invalidation-state 62ms
  [gap of 23ms]
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 19ms
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 60ms
  [gap of 23ms]
generate_cxx_metadata completed in 104ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 47ms
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 15ms
generate_cxx_metadata completed in 31ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
  [gap of 16ms]
generate_cxx_metadata completed in 32ms

