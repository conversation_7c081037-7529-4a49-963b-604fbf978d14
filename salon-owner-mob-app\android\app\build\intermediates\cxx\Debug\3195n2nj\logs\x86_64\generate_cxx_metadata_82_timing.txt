# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 56ms
  [gap of 27ms]
generate_cxx_metadata completed in 106ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 47ms
  [gap of 47ms]
generate_cxx_metadata completed in 122ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 10ms]
generate_cxx_metadata completed in 26ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 32ms
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 47ms

