import React, { useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../context/AuthContext';
import { useCustomerSalon } from '../../context/CustomerSalonContext';
import { MainStackParamList } from '../../types';
import { Colors } from '../../constants/colors';
import { DarkTitle } from '../../components/common/Typography';

type LandingScreenNavigationProp = StackNavigationProp<MainStackParamList, 'Landing'>;

export const LandingScreen: React.FC = () => {
  const navigation = useNavigation<LandingScreenNavigationProp>();
  const { user, loading } = useAuth();
  const { accessibleSalons, hasMultipleSalons, needsSalonSelection } = useCustomerSalon();

  useEffect(() => {
    const navigateBasedOnRole = async () => {
      // Wait for auth to finish loading
      if (loading) {
        return;
      }

      // If no user, something went wrong - this shouldn't happen in authenticated flow
      if (!user) {
        console.error('❌ LandingScreen: No user found in authenticated state');
        return;
      }

      console.log('🎯 LandingScreen: Determining navigation for user role:', user.role);

      // Navigate based on user role
      if (user.role === 'ADMIN') {
        console.log('👑 LandingScreen: ADMIN user detected, navigating to salon owner flow');
        navigation.replace('SalonOwner');
      } else if (user.role === 'CUSTOMER') {
        console.log('👤 LandingScreen: CUSTOMER user detected, checking salon access...');

        // Handle customer salon selection logic
        if (accessibleSalons.length === 0) {
          console.log('⚠️ LandingScreen: Customer has no accessible salons, navigating to customer home');
          navigation.replace('Customer');
        } else if (accessibleSalons.length === 1) {
          console.log('✅ LandingScreen: Customer has 1 salon, navigating directly to customer home');
          navigation.replace('Customer');
        } else if (needsSalonSelection) {
          console.log('🏪 LandingScreen: Customer has multiple salons, needs selection');
          // Navigate to salon selection screen first
          navigation.replace('Customer', {
            screen: 'CustomerSalonSelection'
          } as any);
        } else {
          console.log('✅ LandingScreen: Customer has selected salon, navigating to customer home');
          navigation.replace('Customer');
        }
      } else if (user.role === 'STAFF') {
        console.log('👨‍💼 LandingScreen: STAFF user detected, checking salon access...');

        // Handle staff salon selection logic (same as customer)
        if (accessibleSalons.length === 0) {
          console.log('⚠️ LandingScreen: Staff has no accessible salons, navigating to customer home');
          navigation.replace('Customer');
        } else if (accessibleSalons.length === 1) {
          console.log('✅ LandingScreen: Staff has 1 salon, navigating directly to customer home');
          navigation.replace('Customer');
        } else if (needsSalonSelection) {
          console.log('🏪 LandingScreen: Staff has multiple salons, needs selection');
          // Navigate to salon selection screen first
          navigation.replace('Customer', {
            screen: 'CustomerSalonSelection'
          } as any);
        } else {
          console.log('✅ LandingScreen: Staff has selected salon, navigating to customer home');
          navigation.replace('Customer');
        }
      } else {
        console.warn('⚠️ LandingScreen: Unknown user role:', user.role);
        // Default to customer flow for unknown roles
        navigation.replace('Customer');
      }
    };

    navigateBasedOnRole();
  }, [user, loading, navigation]);

  // Show loading screen while determining navigation
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={Colors.primary} />
      <DarkTitle level={3} textAlign="center" style={styles.loadingText}>
        Setting up your experience...
      </DarkTitle>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 20,
    color: Colors.textSecondary,
  },
});
