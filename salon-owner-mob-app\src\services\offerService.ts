import { apiService } from './api';
import { ENDPOINTS } from '../config/api';



export interface OfferFilters {
  status?: 'active' | 'inactive' | 'expired' | 'all';
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_SERVICE';
  customerType?: 'ALL' | 'NEW' | 'EXISTING' | 'VIP' | 'LOYALTY_TIER';
  frequency?: 'ONE_TIME' | 'RECURRING' | 'LIMITED_USES';
  serviceCategory?: string;
  validFrom?: string;
  validUntil?: string;
  sortBy?: 'title' | 'discountValue' | 'validFrom' | 'validUntil' | 'usedCount' | 'priority' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
  limit?: number;
  offset?: number;
}

export interface CreateOfferData {
  title: string;
  description?: string;
  discountType: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_SERVICE';
  discountValue: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  validFrom: string;
  validUntil: string;
  usageLimit?: number;
  code: string;
  
  // Service targeting
  applicableServices: string[];
  serviceCategories: string[];
  
  // Customer segmentation
  customerType: 'ALL' | 'NEW' | 'EXISTING' | 'VIP' | 'LOYALTY_TIER';
  loyaltyTierRequired?: string;
  minVisitsRequired?: number;
  maxUsagePerCustomer?: number;
  
  // Date and time controls
  validDays: string[];
  validTimeSlots: { startTime: string; endTime: string; }[];
  blackoutDates?: string[];
  
  // Duration and frequency
  offerDuration: {
    type: 'DAYS' | 'WEEKS' | 'MONTHS' | 'CUSTOM';
    value: number;
  };
  frequency: 'ONE_TIME' | 'RECURRING' | 'LIMITED_USES';
  cooldownPeriod?: number;
  
  // Advanced settings
  combinableWithOtherOffers: boolean;
  requiresAdvanceBooking?: number;
  autoApply: boolean;
  priority: number;
  
  salonId: string;
  image?: File;
}

export interface UpdateOfferData {
  title?: string;
  description?: string;
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_SERVICE';
  discountValue?: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  validFrom?: string;
  validUntil?: string;
  isActive?: boolean;
  usageLimit?: number;
  code?: string;
  
  // Service targeting
  applicableServices?: string[];
  serviceCategories?: string[];
  
  // Customer segmentation
  customerType?: 'ALL' | 'NEW' | 'EXISTING' | 'VIP' | 'LOYALTY_TIER';
  loyaltyTierRequired?: string;
  minVisitsRequired?: number;
  maxUsagePerCustomer?: number;
  
  // Date and time controls
  validDays?: string[];
  validTimeSlots?: { startTime: string; endTime: string; }[];
  blackoutDates?: string[];
  
  // Duration and frequency
  offerDuration?: {
    type: 'DAYS' | 'WEEKS' | 'MONTHS' | 'CUSTOM';
    value: number;
  };
  frequency?: 'ONE_TIME' | 'RECURRING' | 'LIMITED_USES';
  cooldownPeriod?: number;
  
  // Advanced settings
  combinableWithOtherOffers?: boolean;
  requiresAdvanceBooking?: number;
  autoApply?: boolean;
  priority?: number;
  
  image?: File;
}

export interface BulkOfferOperation {
  offerIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'extend' | 'updatePriority';
  data?: {
    isActive?: boolean;
    validUntil?: string;
    priority?: number;
  };
}

class OfferService {
  // Helper method to prepare offer data with proper types for backend validation
  private prepareOfferData(offerData: CreateOfferData | UpdateOfferData): any {
    const prepared = { ...offerData };

    // Remove image from the data (handled separately)
    delete prepared.image;

    // Ensure arrays are properly formatted
    if (prepared.applicableServices && !Array.isArray(prepared.applicableServices)) {
      prepared.applicableServices = [prepared.applicableServices];
    }
    if (prepared.serviceCategories && !Array.isArray(prepared.serviceCategories)) {
      prepared.serviceCategories = [prepared.serviceCategories];
    }
    if (prepared.validDays && !Array.isArray(prepared.validDays)) {
      prepared.validDays = [prepared.validDays];
    }
    if (prepared.validTimeSlots && !Array.isArray(prepared.validTimeSlots)) {
      prepared.validTimeSlots = [prepared.validTimeSlots];
    }
    if (prepared.blackoutDates && !Array.isArray(prepared.blackoutDates)) {
      prepared.blackoutDates = [prepared.blackoutDates];
    }

    // Ensure numeric fields are numbers
    if (prepared.discountValue !== undefined) {
      prepared.discountValue = Number(prepared.discountValue);
    }
    if (prepared.minOrderAmount !== undefined) {
      prepared.minOrderAmount = Number(prepared.minOrderAmount);
    }
    if (prepared.maxDiscountAmount !== undefined) {
      prepared.maxDiscountAmount = Number(prepared.maxDiscountAmount);
    }
    if (prepared.usageLimit !== undefined) {
      prepared.usageLimit = Number(prepared.usageLimit);
    }
    if (prepared.minVisitsRequired !== undefined) {
      prepared.minVisitsRequired = Number(prepared.minVisitsRequired);
    }
    if (prepared.maxUsagePerCustomer !== undefined) {
      prepared.maxUsagePerCustomer = Number(prepared.maxUsagePerCustomer);
    }
    if (prepared.cooldownPeriod !== undefined) {
      prepared.cooldownPeriod = Number(prepared.cooldownPeriod);
    }
    if (prepared.requiresAdvanceBooking !== undefined) {
      prepared.requiresAdvanceBooking = Number(prepared.requiresAdvanceBooking);
    }
    if (prepared.priority !== undefined) {
      prepared.priority = Number(prepared.priority);
    }

    // Ensure boolean fields are booleans
    if (prepared.combinableWithOtherOffers !== undefined) {
      prepared.combinableWithOtherOffers = Boolean(prepared.combinableWithOtherOffers);
    }
    if (prepared.autoApply !== undefined) {
      prepared.autoApply = Boolean(prepared.autoApply);
    }
    if ('isActive' in prepared && prepared.isActive !== undefined) {
      prepared.isActive = Boolean(prepared.isActive);
    }

    // Ensure date fields are properly formatted
    if (prepared.validFrom && typeof prepared.validFrom === 'string') {
      prepared.validFrom = new Date(prepared.validFrom).toISOString();
    }
    if (prepared.validUntil && typeof prepared.validUntil === 'string') {
      prepared.validUntil = new Date(prepared.validUntil).toISOString();
    }

    // Ensure offerDuration is properly structured
    if (prepared.offerDuration && typeof prepared.offerDuration === 'object') {
      prepared.offerDuration = {
        type: prepared.offerDuration.type,
        value: Number(prepared.offerDuration.value)
      };
    }

    return prepared;
  }

  // Create a new offer
  async createOffer(offerData: CreateOfferData): Promise<any> {
    try {
      console.log('🎯 OfferService: Creating offer:', offerData.title);

      // Prepare the data with proper types for backend validation
      const preparedData = this.prepareOfferData(offerData);

      // If there's an image, use FormData for multipart upload
      if (offerData.image) {
        const formData = new FormData();

        // Add the image file
        formData.append('image', {
          uri: (offerData.image as any).uri,
          type: (offerData.image as any).type,
          name: (offerData.image as any).name,
        } as any);

        // Add all other offer data as individual fields with proper types
        Object.entries(preparedData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              // For arrays containing objects (like validTimeSlots), stringify the entire array
              if (value.length > 0 && typeof value[0] === 'object') {
                formData.append(key, JSON.stringify(value));
              } else {
                // For simple arrays, add each item with array notation
                value.forEach((item, index) => {
                  formData.append(`${key}[${index}]`, item.toString());
                });
              }
            } else if (typeof value === 'object') {
              // For objects, stringify the entire object
              formData.append(key, JSON.stringify(value));
            } else {
              formData.append(key, value.toString());
            }
          }
        });

        const response = await apiService.postMultipart(ENDPOINTS.OFFERS.BASE, formData);
        console.log('✅ OfferService: Offer created successfully');
        return response.data;
      } else {
        // If no image, use regular JSON POST with prepared data
        const response = await apiService.post(ENDPOINTS.OFFERS.BASE, preparedData);
        console.log('✅ OfferService: Offer created successfully');
        return response.data;
      }
    } catch (error: any) {
      console.error('❌ OfferService: Error creating offer:', error);
      throw error;
    }
  }

  // Get all offers with filters
  async getOffers(filters: OfferFilters = {}): Promise<any> {
    try {
      console.log('🔍 OfferService: Fetching offers with filters:', filters);

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const endpoint = `${ENDPOINTS.OFFERS.BASE}?${queryParams}`;
      const response = await apiService.get(endpoint);
      console.log('✅ OfferService: Fetched offers:', response.data?.total || 0);
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error fetching offers:', error);
      throw error;
    }
  }

  // Get offers by salon
  async getOffersBySalon(salonId: string, filters: OfferFilters = {}): Promise<any> {
    try {
      console.log('🔍 OfferService: Fetching offers for salon:', salonId);

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const endpoint = `${ENDPOINTS.OFFERS.BY_SALON(salonId)}?${queryParams}`;
      const response = await apiService.get(endpoint);
      console.log('✅ OfferService: Fetched salon offers:', response.data?.total || 0);
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error fetching salon offers:', error);
      throw error;
    }
  }

  // Get offer by ID
  async getOfferById(id: string): Promise<any> {
    try {
      console.log('🔍 OfferService: Fetching offer:', id);

      const response = await apiService.get(ENDPOINTS.OFFERS.BY_ID(id));
      console.log('✅ OfferService: Fetched offer:', response.data?.data?.title || 'Unknown');
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error fetching offer:', error);
      throw error;
    }
  }

  // Update offer
  async updateOffer(id: string, updateData: UpdateOfferData): Promise<any> {
    try {
      console.log('🔄 OfferService: Updating offer:', id);

      // Prepare the data with proper types for backend validation
      const preparedData = this.prepareOfferData(updateData);

      // If there's an image, use FormData for multipart upload
      if (updateData.image) {
        const formData = new FormData();

        // Add the image file
        formData.append('image', {
          uri: (updateData.image as any).uri,
          type: (updateData.image as any).type,
          name: (updateData.image as any).name,
        } as any);

        // Add all other update data as individual fields with proper types
        Object.entries(preparedData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              // For arrays containing objects (like validTimeSlots), stringify the entire array
              if (value.length > 0 && typeof value[0] === 'object') {
                formData.append(key, JSON.stringify(value));
              } else {
                // For simple arrays, add each item with array notation
                value.forEach((item, index) => {
                  formData.append(`${key}[${index}]`, item.toString());
                });
              }
            } else if (typeof value === 'object') {
              // For objects, stringify the entire object
              formData.append(key, JSON.stringify(value));
            } else {
              formData.append(key, value.toString());
            }
          }
        });

        const response = await apiService.putMultipart(ENDPOINTS.OFFERS.BY_ID(id), formData);
        console.log('✅ OfferService: Offer updated successfully');
        return response.data;
      } else {
        // If no image, use regular JSON PUT with prepared data
        const response = await apiService.put(ENDPOINTS.OFFERS.BY_ID(id), preparedData);
        console.log('✅ OfferService: Offer updated successfully');
        return response.data;
      }
    } catch (error: any) {
      console.error('❌ OfferService: Error updating offer:', error);
      throw error;
    }
  }

  // Delete offer
  async deleteOffer(id: string): Promise<any> {
    try {
      console.log('🗑️ OfferService: Deleting offer:', id);

      const response = await apiService.delete(ENDPOINTS.OFFERS.BY_ID(id));
      console.log('✅ OfferService: Offer deleted successfully');
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error deleting offer:', error);
      throw error;
    }
  }

  // Bulk operations
  async bulkOperation(operation: BulkOfferOperation): Promise<any> {
    try {
      console.log('🔄 OfferService: Performing bulk operation:', operation.operation);

      const response = await apiService.post(ENDPOINTS.OFFERS.BULK, operation);
      console.log('✅ OfferService: Bulk operation completed successfully');
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error performing bulk operation:', error);
      throw error;
    }
  }

  // Get offer statistics
  async getOfferStats(salonId?: string): Promise<any> {
    try {
      console.log('📊 OfferService: Fetching offer statistics');

      const queryParams = new URLSearchParams();
      if (salonId) {
        queryParams.append('salonId', salonId);
      }

      const endpoint = `${ENDPOINTS.OFFERS.STATS}?${queryParams}`;
      const response = await apiService.get(endpoint);
      console.log('✅ OfferService: Statistics fetched successfully');
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error fetching statistics:', error);
      throw error;
    }
  }

  // Publish offer (send notifications to applicable customers)
  async publishOffer(offerId: string): Promise<any> {
    try {
      console.log('📢 OfferService: Publishing offer:', offerId);

      const response = await apiService.post(ENDPOINTS.OFFERS.PUBLISH(offerId));
      console.log('✅ OfferService: Offer published successfully');
      return response.data;
    } catch (error: any) {
      console.error('❌ OfferService: Error publishing offer:', error);
      throw error;
    }
  }
}

export const offerService = new OfferService();
