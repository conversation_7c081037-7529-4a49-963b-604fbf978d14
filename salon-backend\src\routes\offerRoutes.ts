import { Router } from 'express';
import multer from 'multer';
import {
  createOffer,
  getOffers,
  getOffersBySalon,
  getOffer,
  updateOffer,
  deleteOffer,
  bulkOperation,
  getOfferStats,
  publishOffer
} from '../controllers/offerController';

import {  protect } from '../middlewares/authMiddleware';
import { validateOfferData, validateOfferUpdate } from '../middleware/offerValidation';
import { parseFormDataArrays } from '../middleware/parseFormData';

const router = Router();

// Configure multer for image uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (_req, file, cb) => {
    // Allow only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});
// Apply authentication to all routes
router.use(protect);

/**
 * @swagger
 * components:
 *   schemas:
 *     Offer:
 *       type: object
 *       required:
 *         - title
 *         - discountType
 *         - discountValue
 *         - validFrom
 *         - validUntil
 *         - code
 *         - salonId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the offer
 *         title:
 *           type: string
 *           maxLength: 100
 *           description: Offer title
 *         description:
 *           type: string
 *           description: Offer description
 *         discountType:
 *           type: string
 *           enum: [PERCENTAGE, FIXED_AMOUNT, BUY_ONE_GET_ONE, FREE_SERVICE]
 *           description: Type of discount
 *         discountValue:
 *           type: number
 *           description: Discount value
 *         minOrderAmount:
 *           type: number
 *           description: Minimum order amount required
 *         maxDiscountAmount:
 *           type: number
 *           description: Maximum discount amount
 *         validFrom:
 *           type: string
 *           format: date-time
 *           description: Offer valid from date
 *         validUntil:
 *           type: string
 *           format: date-time
 *           description: Offer valid until date
 *         isActive:
 *           type: boolean
 *           description: Whether the offer is active
 *         usageLimit:
 *           type: integer
 *           description: Maximum number of times offer can be used
 *         usedCount:
 *           type: integer
 *           description: Number of times offer has been used
 *         code:
 *           type: string
 *           maxLength: 50
 *           description: Unique offer code
 *         applicableServices:
 *           type: array
 *           items:
 *             type: string
 *           description: Service IDs or ["all"]
 *         serviceCategories:
 *           type: array
 *           items:
 *             type: string
 *           description: Service categories
 *         customerType:
 *           type: string
 *           enum: [ALL, NEW, EXISTING, VIP, LOYALTY_TIER]
 *           description: Target customer type
 *         validDays:
 *           type: array
 *           items:
 *             type: string
 *           description: Valid days of week or ["ALL"]
 *         validTimeSlots:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               startTime:
 *                 type: string
 *               endTime:
 *                 type: string
 *         frequency:
 *           type: string
 *           enum: [ONE_TIME, RECURRING, LIMITED_USES]
 *           description: Offer frequency
 *         autoApply:
 *           type: boolean
 *           description: Whether to auto-apply the offer
 *         priority:
 *           type: integer
 *           description: Offer priority for stacking
 *         salonId:
 *           type: string
 *           format: uuid
 *           description: Salon ID
 */

/**
 * @swagger
 * /api/offers:
 *   post:
 *     summary: Create a new offer
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               discountType:
 *                 type: string
 *                 enum: [PERCENTAGE, FIXED_AMOUNT, BUY_ONE_GET_ONE, FREE_SERVICE]
 *               discountValue:
 *                 type: number
 *               validFrom:
 *                 type: string
 *                 format: date-time
 *               validUntil:
 *                 type: string
 *                 format: date-time
 *               code:
 *                 type: string
 *               salonId:
 *                 type: string
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: Offer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Offer'
 */
router.post('/', upload.single('image'), parseFormDataArrays, validateOfferData, createOffer);

/**
 * @swagger
 * /api/offers:
 *   get:
 *     summary: Get all offers with filters
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, expired, all]
 *         description: Filter by offer status
 *       - in: query
 *         name: discountType
 *         schema:
 *           type: string
 *           enum: [PERCENTAGE, FIXED_AMOUNT, BUY_ONE_GET_ONE, FREE_SERVICE]
 *         description: Filter by discount type
 *       - in: query
 *         name: customerType
 *         schema:
 *           type: string
 *           enum: [ALL, NEW, EXISTING, VIP, LOYALTY_TIER]
 *         description: Filter by customer type
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in title, description, or code
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [title, discountValue, validFrom, validUntil, usedCount, priority, createdAt]
 *         description: Sort by field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: Offers retrieved successfully
 */
router.get('/', getOffers);

/**
 * @swagger
 * /api/offers/salon/{salonId}:
 *   get:
 *     summary: Get offers by salon
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: salonId
 *         required: true
 *         schema:
 *           type: string
 *         description: Salon ID
 *     responses:
 *       200:
 *         description: Salon offers retrieved successfully
 */
router.get('/salon/:salonId', getOffersBySalon);

/**
 * @swagger
 * /api/offers/stats:
 *   get:
 *     summary: Get offer statistics
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: salonId
 *         schema:
 *           type: string
 *         description: Salon ID for salon-specific stats
 *     responses:
 *       200:
 *         description: Offer statistics retrieved successfully
 */
router.get('/stats', getOfferStats);

/**
 * @swagger
 * /api/offers/{id}:
 *   get:
 *     summary: Get offer by ID
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Offer ID
 *     responses:
 *       200:
 *         description: Offer retrieved successfully
 *       404:
 *         description: Offer not found
 */
router.get('/:id', getOffer);

/**
 * @swagger
 * /api/offers/{id}:
 *   put:
 *     summary: Update offer
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Offer ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               discountType:
 *                 type: string
 *               discountValue:
 *                 type: number
 *               isActive:
 *                 type: boolean
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Offer updated successfully
 *       404:
 *         description: Offer not found
 */
router.put('/:id', upload.single('image'), parseFormDataArrays, validateOfferUpdate, updateOffer);

/**
 * @swagger
 * /api/offers/{id}:
 *   delete:
 *     summary: Delete offer
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Offer ID
 *     responses:
 *       200:
 *         description: Offer deleted successfully
 *       404:
 *         description: Offer not found
 */
router.delete('/:id', deleteOffer);

/**
 * @swagger
 * /api/offers/bulk:
 *   post:
 *     summary: Perform bulk operations on offers
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               offerIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               operation:
 *                 type: string
 *                 enum: [activate, deactivate, delete, extend, updatePriority]
 *               data:
 *                 type: object
 *                 properties:
 *                   isActive:
 *                     type: boolean
 *                   validUntil:
 *                     type: string
 *                     format: date-time
 *                   priority:
 *                     type: integer
 *     responses:
 *       200:
 *         description: Bulk operation completed successfully
 */
router.post('/bulk', bulkOperation);

/**
 * @swagger
 * /api/offers/{id}/publish:
 *   post:
 *     summary: Publish offer and send notifications to applicable customers
 *     tags: [Offers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Offer ID
 *     responses:
 *       200:
 *         description: Offer published successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *       400:
 *         description: Bad request (inactive or expired offer)
 *       404:
 *         description: Offer not found
 *       500:
 *         description: Internal server error
 */
router.post('/:id/publish', publishOffer);

export default router;
