import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import { AvailableSalon, ApiResponse } from '../types';

class CustomerSalonService {
  /**
   * Get available salons for customer registration
   */
  async getAvailableSalons(accountId: string): Promise<AvailableSalon[]> {
    try {
      console.log('🔍 CustomerSalonService: Getting available salons for account:', accountId);

      const response = await apiService.get<ApiResponse<AvailableSalon[]>>(
        `${ENDPOINTS.CUSTOMER_SALON_ACCESS.AVAILABLE_SALONS}?accountId=${accountId}`
      );

      console.log('✅ CustomerSalonService: Retrieved available salons:', response.data);
      
      if (!response.data) {
        throw new Error('No data returned from API');
      }

      // Extract the actual salon data from the nested response structure
      const salons = response.data.data || [];
      console.log('✅ CustomerSalonService: Found', salons.length, 'available salons');
      
      return salons;
    } catch (error: any) {
      console.error('❌ CustomerSalonService: Error getting available salons:', error);
      throw error;
    }
  }

  /**
   * Get customer's accessible salons
   */
  async getCustomerSalons(): Promise<any[]> {
    try {
      console.log('🔍 CustomerSalonService: Getting customer salons');

      const response = await apiService.get<ApiResponse<any[]>>(
        ENDPOINTS.CUSTOMER_SALON_ACCESS.MY_SALONS
      );

      console.log('✅ CustomerSalonService: Retrieved customer salons:', response.data);
      
      if (!response.data) {
        throw new Error('No data returned from API');
      }

      const salons = response.data.data || [];
      return salons;
    } catch (error: any) {
      console.error('❌ CustomerSalonService: Error getting customer salons:', error);
      throw error;
    }
  }

  /**
   * Check if customer has access to a specific salon
   */
  async checkSalonAccess(salonId: string): Promise<boolean> {
    try {
      console.log('🔍 CustomerSalonService: Checking access for salon:', salonId);

      const response = await apiService.get<ApiResponse<{ hasAccess: boolean }>>(
        ENDPOINTS.CUSTOMER_SALON_ACCESS.CHECK(salonId)
      );

      console.log('✅ CustomerSalonService: Access check result:', response.data);
      
      if (!response.data) {
        throw new Error('No data returned from API');
      }

      return response.data.data?.hasAccess || false;
    } catch (error: any) {
      console.error('❌ CustomerSalonService: Error checking salon access:', error);
      throw error;
    }
  }
}

export const customerSalonService = new CustomerSalonService();
