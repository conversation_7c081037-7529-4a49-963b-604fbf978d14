import { DataSource } from 'typeorm';
import { User } from '../entities/User';
import { Role } from '../entities/Role';
import { License } from '../entities/License';
import { Account } from '../entities/Account';
import { Salon } from '../entities/Salon';
import { Service } from '../entities/Service';
import { Offer } from '../entities/Offer';
import { UserDeviceToken } from '../entities/UserDeviceToken';
import { Notification } from '../entities/Notification';
import { CustomerSalonAccess } from '../entities/CustomerSalonAccess';
import { StaffSalonAccess } from '../entities/StaffSalonAccess';
import { Permission } from '../entities/Permission';
import { StaffPermission } from '../entities/StaffPermission';
import logger from '../utils/logger';

export const AppDataSource = new DataSource({
  type: 'postgres',
  url: process.env.DATABASE_URL,
  synchronize: process.env.NODE_ENV === 'development',
  logging: process.env.NODE_ENV === 'development',
  entities: [User, Role, License, Account, Salon, Service, Offer, UserDeviceToken, Notification, CustomerSalonAccess, StaffSalonAccess, Permission, StaffPermission],
  migrations: ['src/migrations/*.ts'],
  subscribers: ['src/subscribers/*.ts'],
});

export const initializeDatabase = async () => {
  try {
    await AppDataSource.initialize();
    logger.info('TypeORM Data Source has been initialized!');

    // Import services after AppDataSource is initialized to avoid circular dependency
    const { roleService } = await import('../services/roleService');
    const { licenseService } = await import('../services/licenseService');
    const { userService } = await import('../services/userService');

    // Create default roles
    await roleService.createDefaultRoles();
    logger.info('Default roles created/verified');

    // Create default licenses
    await licenseService.createDefaultLicenses();
    logger.info('Default licenses created/verified');

    // Create default product admin user
    await userService.createDefaultProductAdmin();
    logger.info('Default product admin user created/verified');
  } catch (error) {
    logger.error('Error during Data Source initialization:', error);
    throw error;
  }
};