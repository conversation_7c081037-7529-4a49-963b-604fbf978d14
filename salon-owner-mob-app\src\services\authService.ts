import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User, 
  ApiResponse 
} from '../types';

class AuthService {
  // Login user
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>(
      ENDPOINTS.AUTH.LOGIN,
      credentials
    );
    return response.data!;
  }

  // Register user
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    console.log('🌐 AuthService: Making API call to register with data:', {
      ...userData,
      password: '[HIDDEN]', // Don't log the actual password
      salonIds: userData.salonIds ? `[${userData.salonIds.length} salons]` : 'none'
    });

    const response = await apiService.post<AuthResponse>(
      ENDPOINTS.AUTH.REGISTER,
      userData
    );

    console.log('✅ AuthService: Registration API response received:', {
      success: response.success,
      userId: response.data?.data?.id,
      userAccountId: response.data?.data?.accountId,
      salonCount: userData.salonIds?.length || 0
    });

    return response.data!;
  }

  // Register with image
  async registerWithImage(
    userData: Omit<RegisterRequest, 'profileImage'>,
    imageUri?: string
  ): Promise<AuthResponse> {
    console.log('📸 AuthService: Registering with image, userData:', {
      ...userData,
      password: '[HIDDEN]', // Don't log the actual password
      salonIds: userData.salonIds ? `[${userData.salonIds.length} salons]` : 'none'
    });

    if (!imageUri) {
      console.log('📸 AuthService: No image provided, using regular register');
      return this.register(userData);
    }

    console.log('📸 AuthService: Creating FormData with image:', imageUri);
    const formData = new FormData();

    // Add user data
    Object.entries(userData).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'salonIds' && Array.isArray(value)) {
          // Handle array fields specially for FormData
          value.forEach((salonId, index) => {
            formData.append(`salonIds[${index}]`, salonId);
          });
        } else {
          formData.append(key, value);
        }
      }
    });

    // Add image file
    formData.append('profileImage', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'profile.jpg',
    } as any);

    const response = await apiService.postMultipart<AuthResponse>(
      ENDPOINTS.AUTH.REGISTER,
      formData
    );
    return response.data!;
  }

  // Get current user
  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>(ENDPOINTS.AUTH.ME);
    return response.data!;
  }

  // Logout user
  async logout(): Promise<void> {
    await apiService.get(ENDPOINTS.AUTH.LOGOUT);
  }

  // Update password
  async updatePassword(currentPassword: string, newPassword: string): Promise<void> {
    await apiService.put(ENDPOINTS.AUTH.UPDATE_PASSWORD, {
      currentPassword,
      newPassword,
    });
  }

  // Forgot password
  async forgotPassword(email: string): Promise<void> {
    await apiService.post(ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
  }

  // Reset password
  async resetPassword(token: string, newPassword: string): Promise<void> {
    await apiService.put(`${ENDPOINTS.AUTH.RESET_PASSWORD}/${token}`, {
      newPassword,
    });
  }
}

export const authService = new AuthService();
