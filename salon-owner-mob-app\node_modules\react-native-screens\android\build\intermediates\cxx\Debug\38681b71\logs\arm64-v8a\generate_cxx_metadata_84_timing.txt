# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 39ms
  [gap of 11ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 84ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 39ms
  [gap of 28ms]
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 17ms
  [gap of 15ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 67ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 16ms
  [gap of 16ms]
generate_cxx_metadata completed in 46ms

