import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import { ServiceResponse } from '../types/service';

export interface CustomerServiceFilters {
  category?: string;
  priceMin?: number;
  priceMax?: number;
  duration?: number;
  isBookable?: boolean;
  search?: string;
  sortBy?: 'name' | 'price' | 'duration' | 'popularity';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface ServiceCategory {
  id: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  serviceCount: number;
}

export interface PopularService extends ServiceResponse {
  bookingCount: number;
  rating: number;
  reviewCount: number;
}

class CustomerServiceService {
  /**
   * Get services for a specific salon (customer view)
   */
  async getServicesBySalon(salonId: string, filters: CustomerServiceFilters = {}): Promise<{
    data: ServiceResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('🔍 CustomerServiceService: Getting services for salon:', salonId, filters);

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const endpoint = `${ENDPOINTS.SERVICES.CUSTOMER_SERVICES(salonId)}?${queryParams}`;
      const response = await apiService.get(endpoint);
      
      console.log('✅ CustomerServiceService: Retrieved services:', response.data?.total || 0);
      return response.data?.data || { data: [], total: 0, page: 1, totalPages: 0 };
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting services:', error);
      throw error;
    }
  }

  /**
   * Get service categories for a salon
   */
  async getServiceCategories(salonId: string): Promise<ServiceCategory[]> {
    try {
      console.log('🔍 CustomerServiceService: Getting service categories for salon:', salonId);

      const response = await apiService.get(ENDPOINTS.SERVICES.CUSTOMER_CATEGORIES(salonId));
      
      console.log('✅ CustomerServiceService: Retrieved categories:', response.data?.length || 0);
      return response.data?.data || [];
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting categories:', error);
      throw error;
    }
  }

  /**
   * Get popular services for a salon
   */
  async getPopularServices(salonId: string, limit: number = 10): Promise<PopularService[]> {
    try {
      console.log('🔍 CustomerServiceService: Getting popular services for salon:', salonId);

      const response = await apiService.get(`${ENDPOINTS.SERVICES.CUSTOMER_POPULAR(salonId)}?limit=${limit}`);
      
      console.log('✅ CustomerServiceService: Retrieved popular services:', response.data?.length || 0);
      return response.data?.data || [];
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting popular services:', error);
      throw error;
    }
  }

  /**
   * Get service by ID (customer view)
   */
  async getServiceById(serviceId: string): Promise<ServiceResponse> {
    try {
      console.log('🔍 CustomerServiceService: Getting service by ID:', serviceId);

      const response = await apiService.get(ENDPOINTS.SERVICES.BY_ID(serviceId));
      
      console.log('✅ CustomerServiceService: Retrieved service:', response.data?.name);
      return response.data?.data;
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting service:', error);
      throw error;
    }
  }

  /**
   * Search services across categories
   */
  async searchServices(salonId: string, query: string, filters: CustomerServiceFilters = {}): Promise<{
    data: ServiceResponse[];
    total: number;
  }> {
    try {
      console.log('🔍 CustomerServiceService: Searching services:', query);

      const searchFilters = { ...filters, search: query };
      const result = await this.getServicesBySalon(salonId, searchFilters);
      
      console.log('✅ CustomerServiceService: Search results:', result.total);
      return {
        data: result.data,
        total: result.total,
      };
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error searching services:', error);
      throw error;
    }
  }

  /**
   * Get services by category
   */
  async getServicesByCategory(salonId: string, category: string, filters: CustomerServiceFilters = {}): Promise<{
    data: ServiceResponse[];
    total: number;
  }> {
    try {
      console.log('🔍 CustomerServiceService: Getting services by category:', category);

      const categoryFilters = { ...filters, category };
      const result = await this.getServicesBySalon(salonId, categoryFilters);
      
      console.log('✅ CustomerServiceService: Category services:', result.total);
      return {
        data: result.data,
        total: result.total,
      };
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting services by category:', error);
      throw error;
    }
  }

  /**
   * Get recommended services based on customer preferences
   */
  async getRecommendedServices(salonId: string, limit: number = 5): Promise<ServiceResponse[]> {
    try {
      console.log('🔍 CustomerServiceService: Getting recommended services for salon:', salonId);

      // For now, return popular services as recommendations
      // This can be enhanced with ML-based recommendations later
      const popularServices = await this.getPopularServices(salonId, limit);
      
      console.log('✅ CustomerServiceService: Retrieved recommendations:', popularServices.length);
      return popularServices;
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting recommendations:', error);
      throw error;
    }
  }
}

export const customerServiceService = new CustomerServiceService();
