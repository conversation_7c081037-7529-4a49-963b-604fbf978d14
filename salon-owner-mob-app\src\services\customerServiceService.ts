import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import { ServiceResponse } from '../types/service';

export interface CustomerServiceFilters {
  category?: string;
  priceMin?: number;
  priceMax?: number;
  duration?: number;
  isBookable?: boolean;
  search?: string;
  sortBy?: 'name' | 'price' | 'duration' | 'popularity';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface ServiceCategory {
  id: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  serviceCount: number;
}

export interface PopularService extends ServiceResponse {
  bookingCount: number;
  rating: number;
  reviewCount: number;
}

class CustomerServiceService {
  /**
   * Get services for a specific salon (customer view)
   */
  async getServicesBySalon(salonId: string, filters: CustomerServiceFilters = {}): Promise<{
    data: ServiceResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('🔍 CustomerServiceService: Getting services for salon:', salonId, filters);

      const queryParams = new URLSearchParams();

      // Map customer filters to backend filters
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
      if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);
      if (filters.limit) queryParams.append('limit', filters.limit.toString());
      if (filters.offset) queryParams.append('offset', filters.offset.toString());

      // Only show active services for customers
      queryParams.append('status', 'ACTIVE');
      queryParams.append('isActive', 'true');

      const endpoint = `${ENDPOINTS.SERVICES.BY_SALON(salonId)}?${queryParams}`;
      const response = await apiService.get(endpoint);

      console.log('✅ CustomerServiceService: Retrieved services:', response.data?.data?.length || 0);

      // Handle the response structure
      const services = response.data?.data || [];
      const total = response.data?.total || services.length;

      return {
        data: services,
        total: total,
        page: Math.floor((filters.offset || 0) / (filters.limit || 20)) + 1,
        totalPages: Math.ceil(total / (filters.limit || 20))
      };
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting services:', error);

      // Return empty result instead of throwing
      return { data: [], total: 0, page: 1, totalPages: 0 };
    }
  }

  /**
   * Get service categories for a salon
   */
  async getServiceCategories(salonId: string): Promise<ServiceCategory[]> {
    try {
      console.log('🔍 CustomerServiceService: Getting service categories for salon:', salonId);

      // Get all services for the salon and extract unique categories
      const servicesResponse = await this.getServicesBySalon(salonId, { limit: 1000 });
      const services = servicesResponse.data;

      // Extract unique categories
      const categoryMap = new Map<string, ServiceCategory>();

      services.forEach(service => {
        if (service.category && !categoryMap.has(service.category)) {
          categoryMap.set(service.category, {
            id: service.category,
            name: this.formatCategoryName(service.category),
            icon: this.getCategoryIcon(service.category),
            color: this.getCategoryColor(service.category),
            serviceCount: 0
          });
        }
      });

      // Count services per category
      services.forEach(service => {
        if (service.category && categoryMap.has(service.category)) {
          const category = categoryMap.get(service.category)!;
          category.serviceCount++;
        }
      });

      const categories = Array.from(categoryMap.values());
      console.log('✅ CustomerServiceService: Retrieved categories:', categories.length);
      return categories;
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting categories:', error);
      return [];
    }
  }

  /**
   * Get popular services for a salon
   */
  async getPopularServices(salonId: string, limit: number = 10): Promise<PopularService[]> {
    try {
      console.log('🔍 CustomerServiceService: Getting popular services for salon:', salonId);

      // Get all services and simulate popularity (for now, just return first N services)
      const servicesResponse = await this.getServicesBySalon(salonId, {
        limit,
        sortBy: 'name',
        sortOrder: 'ASC'
      });

      // Convert to PopularService format
      const popularServices: PopularService[] = servicesResponse.data.map(service => ({
        ...service,
        bookingCount: Math.floor(Math.random() * 50) + 10, // Simulated
        rating: 4.0 + Math.random() * 1.0, // Simulated 4.0-5.0 rating
        reviewCount: Math.floor(Math.random() * 20) + 5, // Simulated
      }));

      console.log('✅ CustomerServiceService: Retrieved popular services:', popularServices.length);
      return popularServices;
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting popular services:', error);
      return [];
    }
  }

  /**
   * Helper method to format category names
   */
  private formatCategoryName(category: string): string {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Helper method to get category icons
   */
  private getCategoryIcon(category: string): string {
    const iconMap: { [key: string]: string } = {
      'HAIR_CUT': 'content-cut',
      'HAIR_COLOR': 'palette',
      'HAIR_STYLING': 'auto-awesome',
      'HAIR_TREATMENT': 'spa',
      'NAIL_CARE': 'back-hand',
      'NAIL_ART': 'brush',
      'FACIAL': 'face',
      'SKIN_CARE': 'face-retouching-natural',
      'MASSAGE': 'healing',
      'WAXING': 'remove',
      'EYEBROW': 'visibility',
      'EYELASH': 'remove-red-eye',
      'MAKEUP': 'face-retouching-natural',
      'BRIDAL': 'favorite',
      'SPECIAL_OCCASION': 'celebration',
      'PACKAGE': 'card-giftcard',
      'OTHER': 'more-horiz'
    };
    return iconMap[category] || 'spa';
  }

  /**
   * Helper method to get category colors
   */
  private getCategoryColor(category: string): string {
    const colorMap: { [key: string]: string } = {
      'HAIR_CUT': '#FF6B6B',
      'HAIR_COLOR': '#4ECDC4',
      'HAIR_STYLING': '#45B7D1',
      'HAIR_TREATMENT': '#96CEB4',
      'NAIL_CARE': '#FFEAA7',
      'NAIL_ART': '#DDA0DD',
      'FACIAL': '#98D8C8',
      'SKIN_CARE': '#F7DC6F',
      'MASSAGE': '#BB8FCE',
      'WAXING': '#F8C471',
      'EYEBROW': '#85C1E9',
      'EYELASH': '#F1948A',
      'MAKEUP': '#D7BDE2',
      'BRIDAL': '#F9E79F',
      'SPECIAL_OCCASION': '#AED6F1',
      'PACKAGE': '#A9DFBF',
      'OTHER': '#D5DBDB'
    };
    return colorMap[category] || '#b363e0';
  }

  /**
   * Get service by ID (customer view)
   */
  async getServiceById(serviceId: string): Promise<ServiceResponse> {
    try {
      console.log('🔍 CustomerServiceService: Getting service by ID:', serviceId);

      const response = await apiService.get(ENDPOINTS.SERVICES.BY_ID(serviceId));

      console.log('✅ CustomerServiceService: Retrieved service:', response.data?.data?.name);
      return response.data?.data;
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting service:', error);
      throw error;
    }
  }

  /**
   * Search services across categories
   */
  async searchServices(salonId: string, query: string, filters: CustomerServiceFilters = {}): Promise<{
    data: ServiceResponse[];
    total: number;
  }> {
    try {
      console.log('🔍 CustomerServiceService: Searching services:', query);

      const searchFilters = { ...filters, search: query };
      const result = await this.getServicesBySalon(salonId, searchFilters);
      
      console.log('✅ CustomerServiceService: Search results:', result.total);
      return {
        data: result.data,
        total: result.total,
      };
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error searching services:', error);
      throw error;
    }
  }

  /**
   * Get services by category
   */
  async getServicesByCategory(salonId: string, category: string, filters: CustomerServiceFilters = {}): Promise<{
    data: ServiceResponse[];
    total: number;
  }> {
    try {
      console.log('🔍 CustomerServiceService: Getting services by category:', category);

      const categoryFilters = { ...filters, category };
      const result = await this.getServicesBySalon(salonId, categoryFilters);
      
      console.log('✅ CustomerServiceService: Category services:', result.total);
      return {
        data: result.data,
        total: result.total,
      };
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting services by category:', error);
      throw error;
    }
  }

  /**
   * Get recommended services based on customer preferences
   */
  async getRecommendedServices(salonId: string, limit: number = 5): Promise<ServiceResponse[]> {
    try {
      console.log('🔍 CustomerServiceService: Getting recommended services for salon:', salonId);

      // For now, return popular services as recommendations
      // This can be enhanced with ML-based recommendations later
      const popularServices = await this.getPopularServices(salonId, limit);
      
      console.log('✅ CustomerServiceService: Retrieved recommendations:', popularServices.length);
      return popularServices;
    } catch (error: any) {
      console.error('❌ CustomerServiceService: Error getting recommendations:', error);
      throw error;
    }
  }
}

export const customerServiceService = new CustomerServiceService();
