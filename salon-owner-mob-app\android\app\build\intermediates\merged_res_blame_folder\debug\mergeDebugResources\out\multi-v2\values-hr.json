{"logs": [{"outputFile": "com.saloon_app-mergeDebugResources-54:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cf9bc6b4f1abcf764423b202ca0f1044\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,73,76,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3155,3233,3311,3396,3493,4312,4408,4538,6893,6960,7028,7198,7416,7546,7654,7714,7780,7836,7907,7967,8021,8147,8204,8266,8320,8395,8764,8849,8927,9022,9107,9188,9325,9409,9495,9628,9719,9797,9853,9908,9974,10048,10126,10197,10279,10351,10428,10508,10582,10689,10782,10855,10947,11043,11117,11193,11289,11341,11423,11490,11577,11664,11726,11790,11853,11923,12029,12145,12242,12356,12416,12475,12971,13054,13131", "endLines": "6,35,36,37,38,39,47,48,49,69,70,71,73,76,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,145,146,147", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3228,3306,3391,3488,3581,4403,4533,4617,6955,7023,7119,7261,7474,7649,7709,7775,7831,7902,7962,8016,8142,8199,8261,8315,8390,8524,8844,8922,9017,9102,9183,9320,9404,9490,9623,9714,9792,9848,9903,9969,10043,10121,10192,10274,10346,10423,10503,10577,10684,10777,10850,10942,11038,11112,11188,11284,11336,11418,11485,11572,11659,11721,11785,11848,11918,12024,12140,12237,12351,12411,12470,12550,13049,13126,13201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5718", "endColumns": "131", "endOffsets": "5845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "34,50,72,74,75,77,91,92,93,140,141,142,143,148,149,150,151,152,153,154,155,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3081,4622,7124,7266,7335,7479,8529,8600,8681,12555,12639,12728,12800,13206,13289,13365,13445,13527,13606,13684,13760,13951,14024,14103,14181", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3150,4701,7193,7330,7411,7541,8595,8676,8759,12634,12723,12795,12881,13284,13360,13440,13522,13601,13679,13755,13845,14019,14098,14176,14257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3586,3684,3791,3888,3987,4091,4195,13850", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3679,3786,3883,3982,4086,4190,4307,13946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,12886", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,12966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4706,4812,4969,5099,5209,5366,5496,5611,5850,6000,6107,6264,6392,6539,6682,6750,6812", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "4807,4964,5094,5204,5361,5491,5606,5713,5995,6102,6259,6387,6534,6677,6745,6807,6888"}}]}]}