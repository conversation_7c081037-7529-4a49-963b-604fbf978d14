import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SalonResponse } from '../services/salonService';

interface SalonContextType {
  selectedSalon: SalonResponse | null;
  setSelectedSalon: (salon: SalonResponse | null) => void;
  clearSelectedSalon: () => void;
  isLoading: boolean;
}

const SalonContext = createContext<SalonContextType | undefined>(undefined);

const SELECTED_SALON_KEY = '@selected_salon';

interface SalonProviderProps {
  children: ReactNode;
}

export const SalonProvider: React.FC<SalonProviderProps> = ({ children }) => {
  const [selectedSalon, setSelectedSalonState] = useState<SalonResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load selected salon from AsyncStorage on app start
  useEffect(() => {
    const loadSelectedSalon = async () => {
      try {
        console.log('🔍 SalonContext: Loading selected salon from AsyncStorage...');
        const storedSalon = await AsyncStorage.getItem(SELECTED_SALON_KEY);
        console.log('🔍 SalonContext: Stored salon data:', storedSalon ? 'Found' : 'Not found');

        if (storedSalon) {
          const salon = JSON.parse(storedSalon) as SalonResponse;
          setSelectedSalonState(salon);
          console.log('✅ SalonContext: Loaded selected salon:', salon.name);
        } else {
          console.log('ℹ️ SalonContext: No salon found in storage');
        }
      } catch (error) {
        console.error('❌ SalonContext: Error loading selected salon:', error);
      } finally {
        setIsLoading(false);
        console.log('✅ SalonContext: Loading complete');
      }
    };

    loadSelectedSalon();
  }, []);

  const setSelectedSalon = async (salon: SalonResponse | null) => {
    try {
      setSelectedSalonState(salon);
      
      if (salon) {
        await AsyncStorage.setItem(SELECTED_SALON_KEY, JSON.stringify(salon));
        console.log('✅ SalonContext: Saved selected salon:', salon.name);
      } else {
        await AsyncStorage.removeItem(SELECTED_SALON_KEY);
        console.log('✅ SalonContext: Cleared selected salon');
      }
    } catch (error) {
      console.error('❌ SalonContext: Error saving selected salon:', error);
    }
  };

  const clearSelectedSalon = async () => {
    try {
      setSelectedSalonState(null);
      await AsyncStorage.removeItem(SELECTED_SALON_KEY);
      console.log('✅ SalonContext: Cleared selected salon');
    } catch (error) {
      console.error('❌ SalonContext: Error clearing selected salon:', error);
    }
  };

  const value: SalonContextType = {
    selectedSalon,
    setSelectedSalon,
    clearSelectedSalon,
    isLoading,
  };

  return (
    <SalonContext.Provider value={value}>
      {children}
    </SalonContext.Provider>
  );
};

export const useSalon = (): SalonContextType => {
  const context = useContext(SalonContext);
  if (context === undefined) {
    throw new Error('useSalon must be used within a SalonProvider');
  }
  return context;
};
