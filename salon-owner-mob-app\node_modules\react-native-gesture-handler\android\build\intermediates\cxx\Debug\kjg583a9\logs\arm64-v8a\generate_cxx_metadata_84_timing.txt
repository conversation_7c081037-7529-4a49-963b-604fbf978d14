# C/C++ build system timings
generate_cxx_metadata
  [gap of 48ms]
  create-invalidation-state 59ms
  [gap of 26ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 157ms

# C/C++ build system timings
generate_cxx_metadata 22ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 31ms
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 82ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 41ms
  [gap of 11ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 113ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 39ms
  [gap of 13ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 112ms

# C/C++ build system timings
generate_cxx_metadata 29ms

