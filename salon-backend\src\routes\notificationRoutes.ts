import express from 'express';
import { body, param } from 'express-validator';


import {
  registerDeviceToken,
  removeDeviceToken,
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getNotificationCount,
  sendTestNotification,
} from '../controllers/notificationController';
import { protect } from '../middlewares/authMiddleware';

const router = express.Router();

// All notification routes require authentication
router.use(protect);

/**
 * @swagger
 * /api/v1/notifications/device-token:
 *   post:
 *     summary: Register device token for push notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - platform
 *             properties:
 *               token:
 *                 type: string
 *                 description: FCM device token
 *               platform:
 *                 type: string
 *                 enum: [android, ios, web]
 *                 description: Device platform
 *               deviceInfo:
 *                 type: object
 *                 properties:
 *                   deviceName:
 *                     type: string
 *                   appVersion:
 *                     type: string
 *                   osVersion:
 *                     type: string
 *                   metadata:
 *                     type: object
 *     responses:
 *       200:
 *         description: Device token registered successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/device-token',
  [
    body('token')
      .notEmpty()
      .withMessage('Device token is required'),
    body('platform')
      .isIn(['android', 'ios', 'web'])
      .withMessage('Platform must be android, ios, or web'),
    body('deviceInfo.deviceName')
      .optional()
      .isString()
      .withMessage('Device name must be a string'),
    body('deviceInfo.appVersion')
      .optional()
      .isString()
      .withMessage('App version must be a string'),
    body('deviceInfo.osVersion')
      .optional()
      .isString()
      .withMessage('OS version must be a string'),
  ],
  registerDeviceToken
);

/**
 * @swagger
 * /api/v1/notifications/device-token:
 *   delete:
 *     summary: Remove device token
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: FCM device token to remove
 *     responses:
 *       200:
 *         description: Device token removed successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 */
router.delete(
  '/device-token',
  [
    body('token')
      .notEmpty()
      .withMessage('Device token is required'),
  ],
  removeDeviceToken
);

/**
 * @swagger
 * /api/v1/notifications:
 *   get:
 *     summary: Get user notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of notifications per page
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', getUserNotifications);

/**
 * @swagger
 * /api/v1/notifications/count:
 *   get:
 *     summary: Get notification count
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Notification count retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/count', getNotificationCount);

/**
 * @swagger
 * /api/v1/notifications/{id}/read:
 *   patch:
 *     summary: Mark notification as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification marked as read
 *       404:
 *         description: Notification not found
 *       401:
 *         description: Unauthorized
 */
router.patch(
  '/:id/read',
  [
    param('id')
      .isUUID()
      .withMessage('Invalid notification ID'),
  ],

  markNotificationAsRead
);

/**
 * @swagger
 * /api/v1/notifications/read-all:
 *   patch:
 *     summary: Mark all notifications as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All notifications marked as read
 *       401:
 *         description: Unauthorized
 */
router.patch('/read-all', markAllNotificationsAsRead);

/**
 * @swagger
 * /api/v1/notifications/test:
 *   post:
 *     summary: Send test notification (development only)
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data
 *     responses:
 *       200:
 *         description: Test notification sent successfully
 *       401:
 *         description: Unauthorized
 */
router.post('/test', sendTestNotification);

export default router;
