import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { DarkTitle, DarkBody } from '../../components/common/Typography';
import { Input } from '../../components/common/Input';
import { Button } from '../../components/common/Button';
import { Colors } from '../../constants/colors';
import { staffService } from '../../services/staffService';
import { permissionService } from '../../services/permissionService';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import {
  UpdateStaffRequest,
  StaffMember,
  StaffPermission,
  StaffPermissionInfo,
  StaffPosition,
  PositionOption,
  StaffAccessStatus,
  getDefaultPermissions,
  getDefaultWorkingHours,
  getPositionOptions,
  getPositionsByCategory,
  validateStaffForm,
  getPermissionLabel,
  getPermissionDescription,
} from '../../types/staff';

const { width, height } = Dimensions.get('window');

interface EditStaffScreenProps {
  route: {
    params: {
      staffId: string;
    };
  };
}

export const EditStaffScreen: React.FC<EditStaffScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();
  const { staffId } = route.params as { staffId: string };

  // Form state
  const [formData, setFormData] = useState<Partial<UpdateStaffRequest>>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    position: undefined,
    hourlyRate: undefined,
    permissions: [],
    workingHours: getDefaultWorkingHours() as any,
    notes: '',
    salonId: selectedSalon?.id || '',
  });

  // UI state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [availablePermissions, setAvailablePermissions] = useState<StaffPermissionInfo[]>([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [showPositionPicker, setShowPositionPicker] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [originalStaff, setOriginalStaff] = useState<StaffMember | null>(null);

  // Load staff data and permissions
  useEffect(() => {
    loadStaffData();
    loadPermissions();
  }, []);

  const loadStaffData = async () => {
    if (!selectedSalon || !staffId) return;

    try {
      setLoading(true);
      const staffData = await staffService.getStaffById(staffId, selectedSalon.id);
      setOriginalStaff(staffData);

      // Populate form with existing data
      setFormData({
        firstName: staffData.staff.firstName,
        lastName: staffData.staff.lastName,
        email: staffData.staff.email,
        phoneNumber: staffData.staff.phoneNumber || '',
        position: staffData.position,
        hourlyRate: staffData.hourlyRate,
        permissions: staffData.permissions?.map(p => p.code) || [],
        workingHours: staffData.workingHours || getDefaultWorkingHours(),
        notes: staffData.notes || '',
        salonId: selectedSalon.id,
      });

      // Set profile image if exists
      if (staffData.staff.profileImage) {
        setProfileImage(staffData.staff.profileImage);
      }

      console.log('✅ EditStaffScreen: Staff data loaded');
    } catch (error: any) {
      console.error('❌ EditStaffScreen: Error loading staff data:', error);
      showToast('Failed to load staff data', 'error');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const loadPermissions = async () => {
    try {
      const permissions = await permissionService.getPermissions();
      setAvailablePermissions(permissions);
      console.log('✅ EditStaffScreen: Permissions loaded:', permissions.length);
    } catch (error: any) {
      console.error('❌ EditStaffScreen: Error loading permissions:', error);
      showToast('Failed to load permissions', 'error');
    }
  };

  // Form handlers
  const updateFormData = (field: keyof UpdateStaffRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors([]); // Clear errors when user makes changes
  };

  const togglePermission = (permissionCode: string) => {
    const currentPermissions = formData.permissions || [];
    const hasPermission = currentPermissions.includes(permissionCode);

    if (hasPermission) {
      updateFormData('permissions', currentPermissions.filter(p => p !== permissionCode));
    } else {
      updateFormData('permissions', [...currentPermissions, permissionCode]);
    }
  };

  // Image picker function
  const selectProfileImage = () => {
    const options = {
      mediaType: 'photo' as MediaType,
      includeBase64: false,
      maxHeight: 1024,
      maxWidth: 1024,
      quality: 0.8 as any,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.didCancel || response.errorMessage) {
        return;
      }

      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        setProfileImage(asset.uri || null);
        updateFormData('profileImage', asset.uri);
      }
    });
  };

  // Save staff changes
  const handleSave = async () => {
    if (!selectedSalon || !staffId) return;

    // Validate form
    const validationErrors = validateStaffForm(formData as any);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      showToast('Please fix the errors below', 'error');
      return;
    }

    try {
      setSaving(true);

      // Prepare update data
      const updateData: UpdateStaffRequest = {
        ...formData,
        salonId: selectedSalon.id,
      };

      console.log('🔍 EditStaffScreen: Updating staff:', updateData);

      await staffService.updateStaff(staffId, updateData, profileImage || undefined);
      
      showToast('Staff member updated successfully', 'success');
      navigation.goBack();
    } catch (error: any) {
      console.error('❌ EditStaffScreen: Error updating staff:', error);
      showToast(error.message || 'Failed to update staff member', 'error');
    } finally {
      setSaving(false);
    }
  };

  // Delete confirmation
  const handleDelete = () => {
    if (!originalStaff) return;

    Alert.alert(
      'Delete Staff Member',
      `Are you sure you want to remove ${originalStaff.staff.firstName} ${originalStaff.staff.lastName} from your salon? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: confirmDelete,
        },
      ]
    );
  };

  const confirmDelete = async () => {
    if (!selectedSalon || !staffId) return;

    try {
      setSaving(true);
      await staffService.deleteStaff(staffId, selectedSalon.id);
      showToast('Staff member removed successfully', 'success');
      navigation.goBack();
    } catch (error: any) {
      console.error('❌ EditStaffScreen: Error deleting staff:', error);
      showToast('Failed to remove staff member', 'error');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody size="medium" style={styles.loadingText}>
            Loading staff data...
          </DarkBody>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={3} style={styles.headerTitle}>
          Edit Staff Member
        </DarkTitle>
        <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
          <Icon name="delete" size={24} color={Colors.error} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Profile Image Section */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Profile Photo
          </DarkTitle>
          
          <View style={styles.profileImageSection}>
            <TouchableOpacity
              style={styles.profileImageContainer}
              onPress={selectProfileImage}
            >
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.profileImage} />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <Icon name="add-a-photo" size={width * 0.08} color={Colors.gray400} />
                  <DarkBody size="small" style={styles.profileImageText}>
                    Tap to change photo
                  </DarkBody>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Personal Information */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Personal Information
          </DarkTitle>
          
          <Input
            label="First Name"
            value={formData.firstName}
            onChangeText={(value) => updateFormData('firstName', value)}
            placeholder="Enter first name"
          />

          <Input
            label="Last Name"
            value={formData.lastName}
            onChangeText={(value) => updateFormData('lastName', value)}
            placeholder="Enter last name"
          />

          <Input
            label="Email"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Input
            label="Phone Number (Optional)"
            value={formData.phoneNumber}
            onChangeText={(value) => updateFormData('phoneNumber', value)}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />
        </View>

        {/* Job Information */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Job Information
          </DarkTitle>

          {/* Position Selector */}
          <TouchableOpacity
            style={styles.positionSelector}
            onPress={() => setShowPositionPicker(true)}
          >
            <DarkBody size="small" style={styles.inputLabel}>
              Position/Title
            </DarkBody>
            <View style={styles.positionSelectorContent}>
              <DarkBody size="medium" style={[
                styles.positionSelectorText,
                !formData.position && styles.positionSelectorPlaceholder
              ]}>
                {formData.position ?
                  getPositionOptions().find(p => p.value === formData.position)?.label || formData.position :
                  'Select position'
                }
              </DarkBody>
              <Icon name="keyboard-arrow-down" size={24} color={Colors.textSecondary} />
            </View>
          </TouchableOpacity>

          <Input
            label="Hourly Rate (Optional)"
            value={formData.hourlyRate?.toString() || ''}
            onChangeText={(value) => updateFormData('hourlyRate', value ? parseFloat(value) : undefined)}
            placeholder="Enter hourly rate"
            keyboardType="numeric"
          />

          <Input
            label="Notes (Optional)"
            value={formData.notes}
            onChangeText={(value) => updateFormData('notes', value)}
            placeholder="Additional notes about this staff member"
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Permissions */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Permissions
          </DarkTitle>
          <DarkBody size="small" style={styles.sectionDescription}>
            Select what this staff member can access and manage
          </DarkBody>

          {availablePermissions.map((permission) => (
            <TouchableOpacity
              key={permission.code}
              style={styles.permissionItem}
              onPress={() => togglePermission(permission.code)}
            >
              <View style={styles.permissionContent}>
                <View style={styles.permissionInfo}>
                  <DarkBody size="medium" style={styles.permissionName}>
                    {getPermissionLabel(permission.code)}
                  </DarkBody>
                  <DarkBody size="small" style={styles.permissionDescription}>
                    {getPermissionDescription(permission.code)}
                  </DarkBody>
                </View>
                <View style={[
                  styles.checkbox,
                  (formData.permissions || []).includes(permission.code) && styles.checkboxChecked
                ]}>
                  {(formData.permissions || []).includes(permission.code) && (
                    <Icon name="check" size={16} color={Colors.white} />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Save Button */}
        <View style={styles.buttonContainer}>
          <Button
            title={saving ? 'Saving...' : 'Save Changes'}
            onPress={handleSave}
            disabled={saving}
            loading={saving}
          />
        </View>
      </ScrollView>

      {/* Position Picker Modal */}
      {showPositionPicker && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <DarkTitle level={3} style={styles.modalTitle}>
                Select Position
              </DarkTitle>
              <TouchableOpacity onPress={() => setShowPositionPicker(false)}>
                <Icon name="close" size={24} color={Colors.textPrimary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              {Object.entries(getPositionsByCategory()).map(([category, positions]) => (
                <View key={category} style={styles.positionCategory}>
                  <DarkBody size="medium" style={styles.positionCategoryTitle}>
                    {category}
                  </DarkBody>
                  {positions.map((position) => (
                    <TouchableOpacity
                      key={position.value}
                      style={[
                        styles.positionOption,
                        formData.position === position.value && styles.positionOptionSelected
                      ]}
                      onPress={() => {
                        updateFormData('position', position.value);
                        setShowPositionPicker(false);
                      }}
                    >
                      <DarkBody size="medium" style={[
                        styles.positionOptionText,
                        formData.position === position.value && styles.positionOptionTextSelected
                      ]}>
                        {position.label}
                      </DarkBody>
                      {formData.position === position.value && (
                        <Icon name="check" size={20} color={Colors.primary} />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  backButton: {
    padding: width * 0.02,
  },
  headerTitle: {
    color: Colors.textPrimary,
    flex: 1,
    textAlign: 'center',
  },
  deleteButton: {
    padding: width * 0.02,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  section: {
    backgroundColor: Colors.white,
    marginHorizontal: width * 0.05,
    marginVertical: height * 0.01,
    borderRadius: width * 0.03,
    padding: width * 0.05,
  },
  sectionTitle: {
    color: Colors.textPrimary,
    marginBottom: height * 0.02,
  },
  profileImageSection: {
    alignItems: 'center',
    marginBottom: height * 0.02,
  },
  profileImageContainer: {
    width: width * 0.25,
    height: width * 0.25,
    borderRadius: width * 0.125,
    backgroundColor: Colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.gray300,
    borderStyle: 'dashed',
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: width * 0.125,
  },
  profileImagePlaceholder: {
    alignItems: 'center',
  },
  profileImageText: {
    color: Colors.textSecondary,
    marginTop: height * 0.005,
    textAlign: 'center',
  },
  buttonContainer: {
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.03,
  },
  inputLabel: {
    color: Colors.textPrimary,
    marginBottom: height * 0.01,
    fontWeight: '500',
  },
  sectionDescription: {
    color: Colors.textSecondary,
    marginBottom: height * 0.02,
  },
  positionSelector: {
    marginBottom: height * 0.02,
  },
  positionSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: width * 0.02,
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.015,
    backgroundColor: Colors.white,
  },
  positionSelectorText: {
    color: Colors.textPrimary,
    flex: 1,
  },
  positionSelectorPlaceholder: {
    color: Colors.textSecondary,
  },
  permissionItem: {
    marginBottom: height * 0.015,
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  permissionInfo: {
    flex: 1,
    marginRight: width * 0.03,
  },
  permissionName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  permissionDescription: {
    color: Colors.textSecondary,
  },
  checkbox: {
    width: width * 0.06,
    height: width * 0.06,
    borderRadius: width * 0.01,
    borderWidth: 2,
    borderColor: Colors.gray300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.04,
    width: width * 0.9,
    maxHeight: height * 0.7,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: width * 0.05,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  modalTitle: {
    color: Colors.textPrimary,
  },
  modalBody: {
    maxHeight: height * 0.5,
    padding: width * 0.05,
  },
  positionCategory: {
    marginBottom: height * 0.025,
  },
  positionCategoryTitle: {
    color: Colors.textPrimary,
    fontWeight: '600',
    marginBottom: height * 0.01,
    paddingBottom: height * 0.005,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  positionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: height * 0.015,
    paddingHorizontal: width * 0.03,
    borderRadius: width * 0.02,
    marginBottom: height * 0.005,
  },
  positionOptionSelected: {
    backgroundColor: Colors.primary + '20',
  },
  positionOptionText: {
    color: Colors.textPrimary,
    flex: 1,
  },
  positionOptionTextSelected: {
    color: Colors.primary,
    fontWeight: '600',
  },
});
