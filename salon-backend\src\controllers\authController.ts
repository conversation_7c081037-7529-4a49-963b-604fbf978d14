import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { userService } from '../services/userService';
import { UserRole } from '../entities/User';
import ApiError from '../utils/apiError';
import { setCache, getCache, deleteCache } from '../config/redis';
import { azureBlobStorage } from '../config/azure';
import {
  sendOkResponse,
  sendAuthResponse,
} from '../utils/responseHelper';
import {
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  UpdatePasswordRequest,
} from '../types/user';

// @desc    Register user
// @route   POST /api/v1/auth/register
// @access  Public
export const register = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { firstName, lastName, email, password, phoneNumber, accountId, salonIds }: RegisterRequest = req.body;

    // Check if user already exists
    const existingUser = await userService.findByEmail(email);
    if (existingUser) {
      return next(new ApiError(400, 'User already exists'));
    }

    let profileImageUrl: string | undefined;

    // Handle optional image upload
    if (req.file) {
      try {
        // Generate a unique blob name
        const blobName = `profile-${uuidv4()}${path.extname(req.file.originalname)}`;

        // Upload to Azure Blob Storage
        profileImageUrl = await azureBlobStorage.uploadBlob(
          blobName,
          req.file.buffer,
          req.file.mimetype
        );
      } catch (uploadError) {
        console.error('Error uploading profile image during registration:', uploadError);
        // Continue with registration even if image upload fails
        profileImageUrl = undefined;
      }
    }

    // Create user (password will be hashed automatically by the entity)
    const user = await userService.create({
      firstName,
      lastName,
      email,
      password,
      phoneNumber,
      role: UserRole.CUSTOMER, // Default role should be CUSTOMER, not ADMIN
      profileImage: profileImageUrl,
      accountId, // ✅ Include account ID if provided
    });

    // Grant salon access if salonIds provided (for customer registration)
    if (salonIds && Array.isArray(salonIds) && salonIds.length > 0) {
      const { customerSalonAccessService } = await import('../services/customerSalonAccessService');
      try {
        await customerSalonAccessService.grantMultipleSalonAccess(user.id, salonIds);
        console.log(`✅ Granted access to ${salonIds.length} salons for customer ${user.id}`);
      } catch (salonAccessError) {
        console.error('❌ Error granting salon access during registration:', salonAccessError);
        // Don't fail registration if salon access fails
      }
    }

    // Generate token
    const token = jwt.sign(
      { id: user.id },
      process.env.JWT_SECRET as string,
      {
        expiresIn: process.env.JWT_EXPIRATION as any,
      }
    );

    return sendAuthResponse(
      res,
      201,
      {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        profileImage: user.profileImage,
        accountId: user.accountId,
      },
      token
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Login user
// @route   POST /api/v1/auth/login
// @access  Public
export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, password }: LoginRequest = req.body;

    // Validate email & password
    if (!email || !password) {
      return next(new ApiError(400, 'Please provide an email and password'));
    }

    // Check for user
    const user = await userService.findByEmail(email);
    if (!user) {
      return next(new ApiError(401, 'Invalid credentials'));
    }

    // Check if user is active
    if (!user.isActive) {
      return next(new ApiError(401, 'Your account has been deactivated'));
    }

    // Check if password matches
    const isMatch = await userService.comparePassword(user, password);
    if (!isMatch) {
      return next(new ApiError(401, 'Invalid credentials'));
    }

    // Generate token
    const token = jwt.sign(
      { id: user.id },
      process.env.JWT_SECRET as string,
      {
        expiresIn: process.env.JWT_EXPIRATION as any,
      }
    );

    // Cache user data
    const redisKey = `user:${user.id}`;
    await setCache(
      redisKey,
      JSON.stringify({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
      }),
      3600
    );

    // For customers, get their accessible salons
    interface AccessibleSalon {
      id: string;
      name: string;
      [key: string]: any;
    }
    let accessibleSalons: AccessibleSalon[] = [];
    if (user.role === UserRole.CUSTOMER) {
      try {
        const { customerSalonAccessService } = await import('../services/customerSalonAccessService');
        const customerSalons = await customerSalonAccessService.getCustomerSalons(user.id);
        accessibleSalons = customerSalons.map((salon: any) => ({
          id: salon.id,
          name: salon.name ?? '',
          ...salon
        }));
        console.log(`✅ Found ${accessibleSalons.length} accessible salons for customer ${user.id}`);
      } catch (error) {
        console.error('❌ Error fetching customer salons during login:', error);
        // Don't fail login if salon access fetch fails
      }
    }

    return sendAuthResponse(
      res,
      200,
      {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        profileImage: user.profileImage,
        token,
        accessibleSalons, // Include accessible salons for customers
      },
      token
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Logout user / clear cache
// @route   GET /api/v1/auth/logout
// @access  Private
export const logout = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (req.user) {
      const redisKey = `user:${req.user.id}`;
      await deleteCache(redisKey);
    }

    return sendOkResponse(res, {}, 'Logged out successfully');
  } catch (error) {
    next(error);
  }
};

// @desc    Get current logged in user
// @route   GET /api/v1/auth/me
// @access  Private
export const getMe = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const redisKey = `user:${req.user?.id}`;
    const cachedUser = await getCache(redisKey);

    if (cachedUser) {
      return sendOkResponse(res, JSON.parse(cachedUser));
    }

    const user = await userService.findById(req.user?.id!);

    if (!user) {
      return next(new ApiError(404, 'User not found'));
    }

    await setCache(
      redisKey,
      JSON.stringify({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        profileImage: user.profileImage,
      }),
      3600
    );

    return sendOkResponse(res, {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      role: user.role,
      profileImage: user.profileImage,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update password
// @route   PUT /api/v1/auth/updatepassword
// @access  Private
export const updatePassword = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { currentPassword, newPassword }: UpdatePasswordRequest = req.body;

    const user = await userService.findById(req.user?.id!);

    if (!user) {
      return next(new ApiError(404, 'User not found'));
    }

    const isMatch = await userService.comparePassword(user, currentPassword);
    if (!isMatch) {
      return next(new ApiError(401, 'Current password is incorrect'));
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    await userService.update(user.id, { password: hashedPassword });

    const redisKey = `user:${user.id}`;
    await deleteCache(redisKey);

    const token = jwt.sign(
      { id: user.id },
      process.env.JWT_SECRET as string,
      {
        expiresIn: process.env.JWT_EXPIRATION as any,
      }
    );

    return sendOkResponse(res, { token }, 'Password updated successfully');
  } catch (error) {
    next(error);
  }
};

// @desc    Forgot password
// @route   POST /api/v1/auth/forgotpassword
// @access  Public
export const forgotPassword = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email }: ForgotPasswordRequest = req.body;

    const user = await userService.findByEmail(email);
    if (!user) {
      return next(new ApiError(404, 'No user with that email'));
    }

    const resetToken = crypto.randomBytes(20).toString('hex');
    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    await userService.update(user.id, {
      resetPasswordToken,
      resetPasswordExpire: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    });

    const resetUrl = `${req.protocol}://${req.get(
      'host'
    )}/api/v1/auth/resetpassword/${resetToken}`;

    return sendOkResponse(res, {
      resetToken,
      resetUrl,
    }, 'Password reset token generated. In a production environment, this would be sent via email.');
  } catch (error) {
    next(error);
  }
};

// @desc    Reset password
// @route   PUT /api/v1/auth/resetpassword/:resettoken
// @access  Public
export const resetPassword = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { token } = req.params;
    const { newPassword }: ResetPasswordRequest = req.body;

    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    const user = await userService.findByResetToken(resetPasswordToken);

    if (!user) {
      return next(new ApiError(400, 'Invalid or expired token'));
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    await userService.update(user.id, {
      password: hashedPassword,
      resetPasswordToken: undefined,
      resetPasswordExpire: undefined,
    });

    const redisKey = `user:${user.id}`;
    await deleteCache(redisKey);

    const jwtToken = jwt.sign(
      { id: user.id },
      process.env.JWT_SECRET as string,
      {
        expiresIn: process.env.JWT_EXPIRATION as any,
      }
    );

    return sendOkResponse(res, { token: jwtToken }, 'Password reset successfully');
  } catch (error) {
    next(error);
  }
};