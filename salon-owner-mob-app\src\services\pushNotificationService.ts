import { Platform, AppState } from 'react-native';
import { apiService } from './api';
import { firebaseMessaging } from '../config/firebase';
import notifee, { AndroidImportance, AndroidStyle } from '@notifee/react-native';


export interface DeviceInfo {
  deviceName?: string;
  appVersion?: string;
  osVersion?: string;
  metadata?: Record<string, any>;
}

export interface NotificationData {
  id: string;
  type: string;
  title: string;
  body: string;
  imageUrl?: string;
  data?: Record<string, any>;
  status: string;
  createdAt: string;
  readAt?: string;
  salon?: {
    id: string;
    name: string;
    logo?: string;
  };
  offer?: {
    id: string;
    title: string;
    discountValue: number;
    discountType: string;
    code: string;
  };
}

export interface NotificationResponse {
  data: NotificationData[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface NotificationCount {
  unread: number;
  total: number;
}

class PushNotificationService {
  private isInitialized = false;
  private currentToken: string | null = null;
  private messageHandlers: ((message: any) => void)[] = [];
  private notificationOpenHandlers: ((message: any) => void)[] = [];
  private initializationPromise: Promise<void> | null = null;
  private localNotificationsInitialized = false;


  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        console.log('🔔 Push Notification Service already initialized');
        return;
      }

      // Prevent multiple simultaneous initialization attempts
      if (this.initializationPromise) {
        console.log('🔔 Push Notification Service initialization in progress, waiting...');
        return this.initializationPromise;
      }

      this.initializationPromise = this.performInitialization();
      await this.initializationPromise;
      this.initializationPromise = null;
    } catch (error) {
      this.initializationPromise = null;
      console.error('❌ Error initializing Push Notification Service:', error);
      throw error;
    }
  }

  
  private async performInitialization(): Promise<void> {
    try {
      console.log('🔔 Starting Push Notification Service initialization...');

      // Initialize local notifications first
      await this.initializeLocalNotifications();

      // Set up Notifee event handlers
      this.setupNotifeeEventHandlers();

      // Initialize Firebase Messaging with proper error handling
      await firebaseMessaging.initialize();

      // Get initial token
      await this.refreshToken();

      // Set up message handlers
      this.setupMessageHandlers();

      // Handle initial notification (app opened from notification)
      await this.handleInitialNotification();

      this.isInitialized = true;
      console.log('✅ Push Notification Service initialized successfully');
    } catch (error) {
      console.error('❌ Error in Push Notification Service initialization:', error);

      // Don't throw error if it's just a permission issue
      // Allow the app to continue functioning
      if (this.isPermissionError(error)) {
        console.warn('⚠️ Push notifications disabled due to permission issues');
        return;
      }

      throw error;
    }
  }

  private isPermissionError(error: any): boolean {
    const errorMessage = error?.message || error?.toString() || '';
    return errorMessage.includes('permission') ||
           errorMessage.includes('not granted') ||
           errorMessage.includes('not attached to an Activity');
  }

  private async initializeLocalNotifications(): Promise<void> {
    if (this.localNotificationsInitialized) {
      return;
    }

    try {
      console.log('🔔 Initializing Notifee local push notifications...');

      // Request permissions for notifications
      await notifee.requestPermission();

      // Create notification channels for Android
      if (Platform.OS === 'android') {
        // Salon Offers Channel
        await notifee.createChannel({
          id: 'salon_offers',
          name: 'Salon Offers',
          description: 'Notifications about new salon offers and promotions',
          importance: AndroidImportance.HIGH,
          sound: 'default',
          vibration: true,
        });

        // Salon Bookings Channel
        await notifee.createChannel({
          id: 'salon_bookings',
          name: 'Salon Bookings',
          description: 'Notifications about booking confirmations and reminders',
          importance: AndroidImportance.HIGH,
          sound: 'default',
          vibration: true,
        });

        // General Notifications Channel
        await notifee.createChannel({
          id: 'salon_general',
          name: 'General Notifications',
          description: 'General app notifications',
          importance: AndroidImportance.DEFAULT,
          sound: 'default',
          vibration: true,
        });

        console.log('✅ Notifee notification channels created');
      }

      this.localNotificationsInitialized = true;
      console.log('✅ Notifee local push notifications initialized');
    } catch (error) {
      console.error('❌ Error initializing Notifee local push notifications:', error);
    }
  }

  private setupNotifeeEventHandlers(): void {
    try {
      console.log('🔔 Setting up Notifee event handlers...');

      // Handle notification press events
      notifee.onForegroundEvent(({ type, detail }) => {
        console.log('📱 Notifee foreground event:', type, detail);

        switch (type) {
          case 1: // PRESS
            console.log('📱 Notification pressed:', detail.notification);
            this.handleNotificationPress(detail.notification);
            break;
          case 2: // ACTION_PRESS
            console.log('📱 Notification action pressed:', detail);
            this.handleNotificationActionPress(detail);
            break;
        }
      });

      // Handle background events
      notifee.onBackgroundEvent(async ({ type, detail }) => {
        console.log('📱 Notifee background event:', type, detail);

        switch (type) {
          case 1: // PRESS
            console.log('📱 Background notification pressed:', detail.notification);
            this.handleNotificationPress(detail.notification);
            break;
          case 2: // ACTION_PRESS
            console.log('📱 Background notification action pressed:', detail);
            this.handleNotificationActionPress(detail);
            break;
        }
      });

      console.log('✅ Notifee event handlers set up successfully');
    } catch (error) {
      console.error('❌ Error setting up Notifee event handlers:', error);
    }
  }

  private handleNotificationPress(notification: any): void {
    try {
      console.log('📱 Handling notification press:', notification);

      // Notify all registered handlers
      this.notificationOpenHandlers.forEach(handler => {
        try {
          handler(notification);
        } catch (error) {
          console.error('❌ Error in notification open handler:', error);
        }
      });
    } catch (error) {
      console.error('❌ Error handling notification press:', error);
    }
  }

  private handleNotificationActionPress(detail: any): void {
    try {
      console.log('📱 Handling notification action press:', detail);

      // Handle specific actions
      if (detail.pressAction?.id === 'view') {
        this.handleNotificationPress(detail.notification);
      }
    } catch (error) {
      console.error('❌ Error handling notification action press:', error);
    }
  }

  private setupMessageHandlers(): void {
    // Handle foreground messages
    firebaseMessaging.onMessage((message) => {
      console.log('📱 Foreground message received:', message);
      this.handleForegroundMessage(message);
    });

    // Handle notification opened app
    firebaseMessaging.onNotificationOpenedApp((message) => {
      console.log('📱 Notification opened app:', message);
      this.handleNotificationOpened(message);
    });

    // Handle token refresh
    firebaseMessaging.onTokenRefresh((token) => {
      console.log('🔄 FCM Token refreshed:', token);
      this.currentToken = token;
      this.registerTokenWithBackend(token);
    });
  }

  private async handleInitialNotification(): Promise<void> {
    try {
      const initialNotification = await firebaseMessaging.getInitialNotification();
      if (initialNotification) {
        console.log('📱 Initial notification:', initialNotification);
        this.handleNotificationOpened(initialNotification);
      }
    } catch (error) {
      console.error('❌ Error handling initial notification:', error);
      // Don't throw error as this is not critical for app functionality
    }
  }

  private handleForegroundMessage(message: any): void {
    console.log('📱 Handling foreground message:', message);

    // Show local notification when app is in foreground
    if (message.notification) {
      this.displayLocalNotification(message);
    }

    // Notify handlers
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('❌ Error in message handler:', error);
      }
    });
  }

  private async displayLocalNotification(message: any): Promise<void> {
    try {
      console.log('🔔 Displaying Notifee local notification:', message.notification?.title);

      // Ensure local notifications are initialized
      if (!this.localNotificationsInitialized) {
        await this.initializeLocalNotifications();
      }

      // Display notification using Notifee
      await notifee.displayNotification({
        title: message.notification?.title || 'New Notification',
        body: message.notification?.body || 'You have a new notification',
        data: message.data || {},
        android: {
          channelId: message.data?.channelId || 'salon_offers',
          smallIcon: 'ic_notification',
          largeIcon: 'ic_launcher',
          color: '#b363e0',
          importance: AndroidImportance.HIGH,
          sound: 'default',
          vibrationPattern: [300, 500],
          autoCancel: true,
          ongoing: false,
          style: {
            type: AndroidStyle.BIGTEXT,
            text: message.notification?.body || 'You have a new notification',
          },
          actions: [
            {
              title: 'View',
              pressAction: {
                id: 'view',
                launchActivity: 'default',
              },
            },
          ],
        },
        ios: {
          sound: 'default',
          categoryId: 'salon_offers',
        },
      });

      console.log('✅ Notifee local notification displayed successfully');
    } catch (error) {
      console.error('❌ Error displaying Notifee local notification:', error);

      // Fallback to console log for debugging
      console.log('📱 Notification fallback - Title:', message.notification?.title);
      console.log('📱 Notification fallback - Body:', message.notification?.body);
    }
  }

  private handleNotificationOpened(message: any): void {
    // Handle navigation based on notification data
    if (message.data) {
      console.log('📱 Notification data:', message.data);
      
      // Notify handlers
      this.notificationOpenHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('❌ Error in notification open handler:', error);
        }
      });
    }
  }

  async refreshToken(): Promise<string | null> {
    try {
      const token = await firebaseMessaging.getToken();
      if (token) {
        this.currentToken = token;
        await this.registerTokenWithBackend(token);
      }
      return token;
    } catch (error) {
      console.error('❌ Error refreshing token:', error);

      // If it's a permission or Activity context error, don't fail completely
      if (this.isPermissionError(error)) {
        console.warn('⚠️ Token refresh failed due to permission issues, continuing without token');
        return null;
      }

      return null;
    }
  }

  // Safe initialization method that won't crash the app
  async initializeSafely(): Promise<boolean> {
    try {
      await this.initialize();
      return true;
    } catch (error) {
      console.error('❌ Safe initialization failed:', error);
      return false;
    }
  }

  private async registerTokenWithBackend(token: string): Promise<void> {
    try {
      const deviceInfo: DeviceInfo = {
        deviceName: Platform.OS === 'ios' ? 'iPhone' : 'Android Device',
        appVersion: '1.0.0', // You can get this from package.json or native modules
        osVersion: Platform.Version.toString(),
        metadata: {
          platform: Platform.OS,
          timestamp: new Date().toISOString(),
        },
      };

      console.log('📤 Registering device token with backend');

      await apiService.post('/notifications/device-token', {
        token,
        platform: Platform.OS === 'ios' ? 'ios' : 'android',
        deviceInfo,
      });

      console.log('✅ Device token registered with backend');
    } catch (error) {
      console.error('❌ Error registering token with backend:', error);
      // Don't throw error to avoid breaking the app
    }
  }

  async unregisterToken(): Promise<void> {
    try {
      if (this.currentToken) {
        console.log('📤 Unregistering device token from backend');

        await apiService.delete('/notifications/device-token', {
          token: this.currentToken,
        });

        await firebaseMessaging.deleteToken();
        this.currentToken = null;

        console.log('✅ Device token unregistered');
      }
    } catch (error) {
      console.error('❌ Error unregistering token:', error);
    }
  }

  // Get user notifications
  async getNotifications(page: number = 1, limit: number = 20): Promise<NotificationResponse> {
    try {
      console.log('🔍 Fetching notifications, page:', page);

      const response = await apiService.get('/notifications', {
        params: { page, limit },
      });

      console.log('✅ Notifications fetched:', response.data.total);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching notifications:', error);
      throw error;
    }
  }

  // Get notification count
  async getNotificationCount(): Promise<NotificationCount> {
    try {
      const response = await apiService.get('/notifications/count');
      return response.data.data;
    } catch (error) {
      console.error('❌ Error fetching notification count:', error);
      return { unread: 0, total: 0 };
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      console.log('📖 Marking notification as read:', notificationId);

      await apiService.put(`/notifications/${notificationId}/read`);

      console.log('✅ Notification marked as read');
    } catch (error) {
      console.error('❌ Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllAsRead(): Promise<void> {
    try {
      console.log('📖 Marking all notifications as read');

      await apiService.put('/notifications/read-all');

      console.log('✅ All notifications marked as read');
    } catch (error) {
      console.error('❌ Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Send test notification
  async sendTestNotification(title?: string, body?: string): Promise<void> {
    try {
      console.log('🧪 Sending test notification');

      await apiService.post('/notifications/test', {
        title: title || 'Test Notification',
        body: body || 'This is a test notification',
        data: { type: 'test', timestamp: new Date().toISOString() },
      });

      console.log('✅ Test notification sent');
    } catch (error) {
      console.error('❌ Error sending test notification:', error);
      throw error;
    }
  }

  // Subscribe to topic
  async subscribeToTopic(topic: string): Promise<void> {
    try {
      await firebaseMessaging.subscribeToTopic(topic);
    } catch (error) {
      console.error(`❌ Error subscribing to topic ${topic}:`, error);
    }
  }

  // Unsubscribe from topic
  async unsubscribeFromTopic(topic: string): Promise<void> {
    try {
      await firebaseMessaging.unsubscribeFromTopic(topic);
    } catch (error) {
      console.error(`❌ Error unsubscribing from topic ${topic}:`, error);
    }
  }

  // Add message handler
  onMessage(handler: (message: any) => void): () => void {
    this.messageHandlers.push(handler);
    
    // Return unsubscribe function
    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) {
        this.messageHandlers.splice(index, 1);
      }
    };
  }

  // Add notification opened handler
  onNotificationOpened(handler: (message: any) => void): () => void {
    this.notificationOpenHandlers.push(handler);
    
    // Return unsubscribe function
    return () => {
      const index = this.notificationOpenHandlers.indexOf(handler);
      if (index > -1) {
        this.notificationOpenHandlers.splice(index, 1);
      }
    };
  }

  // Check if notifications are enabled
  async areNotificationsEnabled(): Promise<boolean> {
    return firebaseMessaging.areNotificationsEnabled();
  }

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    try {
      console.log('🔔 Requesting notification permissions with Notifee...');

      // Use Notifee for both platforms
      const settings = await notifee.requestPermission();

      console.log('📱 Notifee permission result:', settings);

      // Check if permissions are granted
      const hasPermission = settings.authorizationStatus >= 1; // AUTHORIZED or PROVISIONAL

      console.log('📱 Permission granted:', hasPermission);
      return hasPermission;
    } catch (error) {
      console.error('❌ Error requesting permissions with Notifee:', error);

      // Fallback to Firebase for iOS
      if (Platform.OS === 'ios') {
        try {
          const hasPermission = await firebaseMessaging.requestPermissionsManually();
          console.log('📱 iOS Firebase permission fallback result:', hasPermission);
          return hasPermission;
        } catch (fallbackError) {
          console.error('❌ Error with Firebase permission fallback:', fallbackError);
          return false;
        }
      }

      return false;
    }
  }

  // Open app settings
  async openSettings(): Promise<void> {
    return firebaseMessaging.openSettings();
  }

  getCurrentToken(): string | null {
    return this.currentToken;
  }

  isServiceInitialized(): boolean {
    return this.isInitialized;
  }
}

export const pushNotificationService = new PushNotificationService();
