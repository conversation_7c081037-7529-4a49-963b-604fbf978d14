# C/C++ build system timings
generate_cxx_metadata
  [gap of 130ms]
  create-invalidation-state 237ms
  [gap of 95ms]
  write-metadata-json-to-file 79ms
generate_cxx_metadata completed in 541ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 82ms]
  create-invalidation-state 222ms
  [gap of 146ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 483ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 108ms]
  create-invalidation-state 180ms
  [gap of 55ms]
  write-metadata-json-to-file 47ms
generate_cxx_metadata completed in 390ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 47ms]
  create-invalidation-state 248ms
  generate-prefab-packages
    exec-prefab 1018ms
    [gap of 31ms]
  generate-prefab-packages completed in 1049ms
  execute-generate-process
    exec-configure 624ms
    [gap of 219ms]
  execute-generate-process completed in 843ms
  [gap of 101ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 2336ms

