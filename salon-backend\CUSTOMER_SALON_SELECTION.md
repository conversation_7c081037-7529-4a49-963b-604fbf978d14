# Customer Salon Selection Feature

## Overview
This feature allows customers to select one or more salons during registration and provides appropriate navigation flow based on their salon access.

## Customer Flow

### Registration Flow
1. **Customer Registration**: During signup, customers can select multiple salons from an account
2. **Salon Access**: Selected salons are automatically granted to the customer
3. **Login Response**: Login includes accessible salons information

### Login Flow
1. **Single Salon Access**: Customer goes directly to customer home page
2. **Multiple Salon Access**: Customer sees salon selection screen first, then goes to customer home page
3. **No Salon Access**: Customer goes to customer home page (with limited functionality)

## Backend Implementation

### 1. Database Schema

#### CustomerSalonAccess Entity
```sql
CREATE TABLE "customer_salon_access" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "customerId" uuid NOT NULL,
  "salonId" uuid NOT NULL,
  "status" varchar(20) NOT NULL DEFAULT 'ACTIVE',
  "grantedAt" timestamp,
  "expiresAt" timestamp,
  "settings" json,
  "notes" text,
  "createdAt" timestamp DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" timestamp DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT "FK_customer_salon_access_customerId" 
    FOREIGN KEY ("customerId") REFERENCES "users"("id") ON DELETE CASCADE,
  CONSTRAINT "FK_customer_salon_access_salonId" 
    FOREIGN KEY ("salonId") REFERENCES "salons"("id") ON DELETE CASCADE,
  CONSTRAINT "UQ_customer_salon_access_customer_salon" 
    UNIQUE ("customerId", "salonId")
);
```

### 2. API Endpoints

#### Customer Salon Access Routes (`/api/v1/customer-salon-access`)

- `GET /my-salons` - Get customer's accessible salons
- `GET /salon/:salonId/customers` - Get salon's customers (Admin/Staff only)
- `POST /grant` - Grant customer access to a salon (Admin only)
- `POST /revoke` - Revoke customer access to a salon (Admin only)
- `GET /check/:salonId` - Check if customer has access to a specific salon
- `GET /available-salons?accountId=:accountId` - Get available salons for registration

#### Updated Registration Endpoint
```typescript
POST /api/v1/auth/register
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+**********",
  "accountId": "account-uuid",
  "salonIds": ["salon-uuid-1", "salon-uuid-2"] // New field
}
```

#### Updated Login Response
```typescript
{
  "success": true,
  "data": {
    "token": "jwt-token",
    "user": {
      "id": "user-uuid",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "CUSTOMER",
      // ... other user fields
    },
    "accessibleSalons": [ // New field for customers
      {
        "id": "access-uuid",
        "customerId": "user-uuid",
        "salonId": "salon-uuid",
        "status": "ACTIVE",
        "salon": {
          "id": "salon-uuid",
          "name": "Salon Name",
          "description": "Salon Description",
          "logo": "logo-url",
          "address": "Salon Address",
          "city": "City"
        }
      }
    ]
  }
}
```

### 3. Services

#### CustomerSalonAccessService
- `grantAccess()` - Grant customer access to a salon
- `getCustomerSalons()` - Get customer's accessible salons
- `getSalonCustomers()` - Get salon's customers
- `revokeAccess()` - Revoke customer access
- `grantMultipleSalonAccess()` - Grant access to multiple salons
- `hasAccess()` - Check if customer has access to a salon

## Frontend Implementation

### 1. Context Management

#### CustomerSalonContext
- Manages customer's accessible salons
- Handles salon selection state
- Provides salon selection methods
- Integrates with login response

### 2. Navigation Flow

#### Landing Screen Logic
```typescript
if (user.role === 'CUSTOMER') {
  if (accessibleSalons.length === 0) {
    // No salon access - go to customer home
    navigation.replace('Customer');
  } else if (accessibleSalons.length === 1) {
    // Single salon - go directly to customer home
    navigation.replace('Customer');
  } else if (needsSalonSelection) {
    // Multiple salons - show selection screen
    navigation.replace('Customer', { 
      screen: 'CustomerSalonSelection' 
    });
  } else {
    // Salon already selected - go to customer home
    navigation.replace('Customer');
  }
}
```

### 3. Screens

#### CustomerSalonSelectionScreen
- Displays available salons for customer
- Allows salon selection
- Navigates to customer home after selection
- Handles empty state and loading

#### CustomerRegistrationScreen
- Includes salon selection during registration
- Loads available salons based on account ID
- Allows multiple salon selection
- Validates salon selection before registration

## Usage Examples

### 1. Customer Registration with Salon Selection
```typescript
const registrationData = {
  firstName: "John",
  lastName: "Doe",
  email: "<EMAIL>",
  password: "password123",
  accountId: "account-uuid",
  salonIds: ["salon-1", "salon-2"] // Customer selects multiple salons
};

const response = await apiService.post('/auth/register', registrationData);
```

### 2. Customer Login Flow
```typescript
// Login response includes accessible salons
const loginResponse = await apiService.post('/auth/login', credentials);
const { user, accessibleSalons } = loginResponse.data.data;

// Navigation logic based on salon count
if (accessibleSalons.length === 1) {
  // Auto-select single salon and go to home
  selectSalon(accessibleSalons[0]);
  navigation.navigate('CustomerHome');
} else if (accessibleSalons.length > 1) {
  // Show salon selection screen
  navigation.navigate('CustomerSalonSelection');
}
```

### 3. Salon Selection
```typescript
const { selectSalon } = useCustomerSalon();

const handleSalonSelect = (salon) => {
  selectSalon(salon); // Updates context and persists selection
  navigation.navigate('CustomerHome');
};
```

## Testing

### Backend Testing
1. **Registration**: Test customer registration with salon selection
2. **Login**: Test login response includes accessible salons
3. **Access Control**: Test salon access validation
4. **Admin Functions**: Test granting/revoking salon access

### Frontend Testing
1. **Single Salon**: Test direct navigation to customer home
2. **Multiple Salons**: Test salon selection screen flow
3. **No Salons**: Test empty state handling
4. **Persistence**: Test salon selection persistence across app restarts

## Security Considerations

1. **Access Validation**: Always validate customer has access to salon before allowing operations
2. **Role-Based Access**: Only admins can grant/revoke salon access
3. **Data Isolation**: Customers can only see their accessible salons
4. **Expiry Handling**: Support for time-based access expiry

## Future Enhancements

1. **Salon Switching**: Allow customers to switch between salons in-app
2. **Access Requests**: Allow customers to request access to new salons
3. **Notifications**: Notify customers when salon access is granted/revoked
4. **Analytics**: Track salon selection patterns and usage
