import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCustomerSalonAccess1703000000002 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create customer_salon_access table
    await queryRunner.query(`
      CREATE TABLE "customer_salon_access" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "customerId" uuid NOT NULL,
        "salonId" uuid NOT NULL,
        "status" varchar(20) NOT NULL DEFAULT 'ACTIVE' CHECK ("status" IN ('ACTIVE', 'INACTIVE', 'SUSPENDED')),
        "grantedAt" timestamp,
        "expiresAt" timestamp,
        "settings" json,
        "notes" text,
        "createdAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        
        -- Foreign key constraints
        CONSTRAINT "FK_customer_salon_access_customerId" 
          FOREIGN KEY ("customerId") REFERENCES "users"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_customer_salon_access_salonId" 
          FOREIGN KEY ("salonId") REFERENCES "salons"("id") ON DELETE CASCADE,
          
        -- Unique constraint to prevent duplicate access records
        CONSTRAINT "UQ_customer_salon_access_customer_salon" 
          UNIQUE ("customerId", "salonId")
      );
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_customer_salon_access_customerId" 
      ON "customer_salon_access" ("customerId");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_customer_salon_access_salonId" 
      ON "customer_salon_access" ("salonId");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_customer_salon_access_status" 
      ON "customer_salon_access" ("status");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_customer_salon_access_customer_status" 
      ON "customer_salon_access" ("customerId", "status");
    `);

    // Create trigger to update updatedAt timestamp
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_customer_salon_access_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW."updatedAt" = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await queryRunner.query(`
      CREATE TRIGGER update_customer_salon_access_updated_at
        BEFORE UPDATE ON "customer_salon_access"
        FOR EACH ROW
        EXECUTE FUNCTION update_customer_salon_access_updated_at();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger and function
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS update_customer_salon_access_updated_at ON "customer_salon_access";
    `);

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS update_customer_salon_access_updated_at();
    `);

    // Drop indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_customer_salon_access_customer_status";
    `);

    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_customer_salon_access_status";
    `);

    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_customer_salon_access_salonId";
    `);

    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_customer_salon_access_customerId";
    `);

    // Drop table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "customer_salon_access";
    `);
  }
}
