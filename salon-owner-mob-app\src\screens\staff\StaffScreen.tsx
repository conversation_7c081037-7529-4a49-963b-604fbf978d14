import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { DarkTitle, DarkBody } from '../../components/common/Typography';
import { Colors } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import { staffService } from '../../services/staffService';
import {
  StaffMember,
  StaffAccessStatus,
  formatStaffName,
  formatPosition,
  getStatusColor,
  getStatusLabel,
  getTodayWorkingHours,
} from '../../types/staff';
import { ScreenWrapper } from '../../components/common/ScreenWrapper';

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: height * 0.015,
  },
  headerTitle: {
    color: Colors.textPrimary,
  },
  addButton: {
    backgroundColor: Colors.primary,
    width: width * 0.12,
    height: width * 0.12,
    borderRadius: width * 0.06,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    marginTop: height * 0.01,
  },
  filterChip: {
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.01,
    marginRight: width * 0.02,
    borderRadius: width * 0.05,
    backgroundColor: Colors.gray100,
  },
  filterChipActive: {
    backgroundColor: Colors.primary,
  },
  filterChipText: {
    color: Colors.textSecondary,
  },
  filterChipTextActive: {
    color: Colors.white,
  },
  staffCard: {
    backgroundColor: Colors.white,
    marginHorizontal: width * 0.05,
    marginVertical: height * 0.01,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  staffCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: width * 0.03,
  },
  avatar: {
    width: width * 0.12,
    height: width * 0.12,
    borderRadius: width * 0.06,
  },
  avatarPlaceholder: {
    width: width * 0.12,
    height: width * 0.12,
    borderRadius: width * 0.06,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: Colors.white,
    fontWeight: 'bold',
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: width * 0.03,
    height: width * 0.03,
    borderRadius: width * 0.015,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  staffInfo: {
    flex: 1,
  },
  staffName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  staffPosition: {
    color: Colors.primary,
    marginBottom: height * 0.005,
  },
  staffEmail: {
    color: Colors.textSecondary,
    marginBottom: height * 0.005,
  },
  workingHours: {
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  staffActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: width * 0.02,
    marginLeft: width * 0.02,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: height * 0.015,
    paddingTop: height * 0.015,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  statusChip: {
    paddingHorizontal: width * 0.03,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.02,
  },
  statusText: {
    color: Colors.white,
    fontWeight: '600',
  },
  permissionCount: {
    color: Colors.textSecondary,
  },
  listContainer: {
    paddingVertical: height * 0.01,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  emptyTitle: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  emptyText: {
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: height * 0.03,
  },
  emptyButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.015,
    borderRadius: width * 0.02,
  },
  emptyButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  noSalonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  noSalonText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
});

export const StaffScreen: React.FC = () => {
  const navigation = useNavigation();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();

  // State
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<StaffAccessStatus | 'ALL'>('ALL');

  // Load staff data
  const loadStaff = useCallback(async (showLoading = true) => {
    if (!selectedSalon) return;

    try {
      if (showLoading) setLoading(true);

      console.log('🔍 StaffScreen: Loading staff for salon:', selectedSalon.id);

      const filters = {
        status: selectedStatus !== 'ALL' ? selectedStatus : undefined,
        search: searchQuery || undefined,
        sortBy: 'firstName' as const,
        sortOrder: 'ASC' as const,
        limit: 100,
      };

      const result = await staffService.getStaffBySalon(selectedSalon.id, filters);
      setStaff(result.data);

      console.log('✅ StaffScreen: Loaded staff members:', result.data.length);
    } catch (error: any) {
      console.error('❌ StaffScreen: Error loading staff:', error);
      showToast('Failed to load staff members', 'error');
    } finally {
      if (showLoading) setLoading(false);
      setRefreshing(false);
    }
  }, [selectedSalon, selectedStatus, searchQuery, showToast]);

  // Focus effect to reload data
  useFocusEffect(
    useCallback(() => {
      loadStaff();
    }, [loadStaff])
  );

  // Refresh handler
  const handleRefresh = () => {
    setRefreshing(true);
    loadStaff(false);
  };

  // Navigation handlers
  const handleAddStaff = () => {
    navigation.navigate('AddStaff' as never);
  };

  const handleStaffPress = (staffMember: StaffMember) => {
    navigation.navigate('StaffDetails' as never, { staffId: staffMember.staffId } as never);
  };

  const handleDeleteStaff = (staffMember: StaffMember) => {
    Alert.alert(
      'Delete Staff Member',
      `Are you sure you want to remove ${formatStaffName(staffMember.staff)} from your salon?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteStaff(staffMember),
        },
      ]
    );
  };

  const deleteStaff = async (staffMember: StaffMember) => {
    if (!selectedSalon) return;

    try {
      await staffService.deleteStaff(staffMember.staffId, selectedSalon.id);
      showToast('Staff member removed successfully', 'success');
      loadStaff();
    } catch (error: any) {
      console.error('❌ StaffScreen: Error deleting staff:', error);
      showToast('Failed to remove staff member', 'error');
    }
  };

  // Filter handlers
  const statusFilters = [
    { label: 'All', value: 'ALL' },
    { label: 'Active', value: StaffAccessStatus.ACTIVE },
    { label: 'Suspended', value: StaffAccessStatus.SUSPENDED },
    { label: 'Revoked', value: StaffAccessStatus.REVOKED },
  ];

  // Render staff card
  const renderStaffCard = ({ item }: { item: StaffMember }) => (
    <TouchableOpacity
      style={styles.staffCard}
      onPress={() => handleStaffPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.staffCardContent}>
        {/* Avatar */}
        <View style={styles.avatarContainer}>
          {item.staff.profileImage ? (
            <Image source={{ uri: item.staff.profileImage }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <DarkBody size="large" style={styles.avatarText}>
                {item.staff.firstName.charAt(0)}{item.staff.lastName.charAt(0)}
              </DarkBody>
            </View>
          )}
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]} />
        </View>

        {/* Staff Info */}
        <View style={styles.staffInfo}>
          <DarkTitle level={4} style={styles.staffName}>
            {formatStaffName(item.staff)}
          </DarkTitle>
          <DarkBody size="small" style={styles.staffPosition}>
            {formatPosition(item.position)}
          </DarkBody>
          <DarkBody size="small" style={styles.staffEmail}>
            {item.staff.email}
          </DarkBody>
          <DarkBody size="small" style={styles.workingHours}>
            Today: {getTodayWorkingHours(item.workingHours)}
          </DarkBody>
        </View>

        {/* Actions */}
        <View style={styles.staffActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleStaffPress(item)}
          >
            <Icon name="edit" size={20} color={Colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteStaff(item)}
          >
            <Icon name="delete" size={20} color={Colors.error} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Status */}
      <View style={styles.statusContainer}>
        <View style={[styles.statusChip, { backgroundColor: getStatusColor(item.status) }]}>
          <DarkBody size="small" style={styles.statusText}>
            {getStatusLabel(item.status)}
          </DarkBody>
        </View>
        <DarkBody size="small" style={styles.permissionCount}>
          {item.permissions.length} permissions
        </DarkBody>
      </View>
    </TouchableOpacity>
  );

  // Show loading or no salon selected
  if (!selectedSalon) {
    return (
      <ScreenWrapper>
        <View style={styles.noSalonContainer}>
          <Icon name="business" size={width * 0.2} color={Colors.gray400} />
          <DarkTitle level={3} textAlign="center" style={styles.noSalonText}>
            No salon selected
          </DarkTitle>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <DarkTitle level={2} style={styles.headerTitle}>
            Staff Management
          </DarkTitle>
          <TouchableOpacity style={styles.addButton} onPress={handleAddStaff}>
            <Icon name="add" size={24} color={Colors.white} />
          </TouchableOpacity>
        </View>

        {/* Status Filters */}
        <View style={styles.filtersContainer}>
          <FlatList
            horizontal
            data={statusFilters}
            keyExtractor={(item) => item.value}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  selectedStatus === item.value && styles.filterChipActive,
                ]}
                onPress={() => setSelectedStatus(item.value as any)}
              >
                <DarkBody
                  size="small"
                  style={[
                    styles.filterChipText,
                    selectedStatus === item.value && styles.filterChipTextActive,
                  ]}
                >
                  {item.label}
                </DarkBody>
              </TouchableOpacity>
            )}
          />
        </View>
      </View>

      {/* Staff List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody size="medium" style={styles.loadingText}>
            Loading staff members...
          </DarkBody>
        </View>
      ) : staff.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="people" size={width * 0.15} color={Colors.gray400} />
          <DarkTitle level={3} textAlign="center" style={styles.emptyTitle}>
            No staff members found
          </DarkTitle>
          <DarkBody size="medium" textAlign="center" style={styles.emptyText}>
            {selectedStatus !== 'ALL'
              ? `No ${selectedStatus.toLowerCase()} staff members found`
              : 'Add your first staff member to get started'}
          </DarkBody>
          {selectedStatus === 'ALL' && (
            <TouchableOpacity style={styles.emptyButton} onPress={handleAddStaff}>
              <DarkBody size="medium" style={styles.emptyButtonText}>
                Add Staff Member
              </DarkBody>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={staff}
          keyExtractor={(item) => item.id}
          renderItem={renderStaffCard}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </ScreenWrapper>
  );
};
