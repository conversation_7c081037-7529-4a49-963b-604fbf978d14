import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { apiService } from '../../services/api';

interface Salon {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  address?: string;
  city?: string;
  phone?: string;
  email?: string;
}

const CustomerRegistrationScreen: React.FC = () => {
  const navigation = useNavigation();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    accountId: '', // This should be provided or selected
  });
  const [availableSalons, setAvailableSalons] = useState<Salon[]>([]);
  const [selectedSalonIds, setSelectedSalonIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingSalons, setIsLoadingSalons] = useState(false);

  // Load available salons for the account
  const loadAvailableSalons = async () => {
    if (!formData.accountId) {
      return;
    }

    try {
      setIsLoadingSalons(true);
      console.log('🔍 Loading available salons for account:', formData.accountId);

      const response = await apiService.get(`/customer-salon-access/available-salons?accountId=${formData.accountId}`);
      
      if (response) {
        const salons = response.data || [];
        setAvailableSalons(salons);
        console.log('✅ Found', salons.length, 'available salons');
      } else {
        console.error('❌ Failed to load available salons:', response.data.error);
        setAvailableSalons([]);
      }
    } catch (error) {
      console.error('❌ Error loading available salons:', error);
      setAvailableSalons([]);
      Alert.alert('Error', 'Failed to load available salons');
    } finally {
      setIsLoadingSalons(false);
    }
  };

  // Load salons when account ID changes
  useEffect(() => {
    if (formData.accountId) {
      loadAvailableSalons();
    } else {
      setAvailableSalons([]);
      setSelectedSalonIds([]);
    }
  }, [formData.accountId]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const toggleSalonSelection = (salonId: string) => {
    setSelectedSalonIds(prev => {
      if (prev.includes(salonId)) {
        return prev.filter(id => id !== salonId);
      } else {
        return [...prev, salonId];
      }
    });
  };

  const validateForm = (): boolean => {
    if (!formData.firstName.trim()) {
      Alert.alert('Error', 'First name is required');
      return false;
    }
    if (!formData.lastName.trim()) {
      Alert.alert('Error', 'Last name is required');
      return false;
    }
    if (!formData.email.trim()) {
      Alert.alert('Error', 'Email is required');
      return false;
    }
    if (!formData.password) {
      Alert.alert('Error', 'Password is required');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    if (!formData.accountId) {
      Alert.alert('Error', 'Account ID is required');
      return false;
    }
    if (selectedSalonIds.length === 0) {
      Alert.alert('Error', 'Please select at least one salon');
      return false;
    }
    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      console.log('📝 Registering customer with salon access...');

      const registrationData = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
        phoneNumber: formData.phoneNumber.trim() || undefined,
        accountId: formData.accountId,
        salonIds: selectedSalonIds, // Include selected salon IDs
      };

      const response = await apiService.post('/auth/register', registrationData);

      if (response.data.success) {
        console.log('✅ Customer registration successful');
        Alert.alert(
          'Success',
          'Registration successful! You can now login with your credentials.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Login' as never),
            },
          ]
        );
      } else {
        console.error('❌ Registration failed:', response.data.error);
        Alert.alert('Error', response.data.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('❌ Registration error:', error);
      Alert.alert(
        'Error',
        error.response?.data?.error || 'Registration failed. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderSalonItem = ({ item }: { item: Salon }) => {
    const isSelected = selectedSalonIds.includes(item.id);
    
    return (
      <TouchableOpacity
        style={[styles.salonItem, isSelected && styles.salonItemSelected]}
        onPress={() => toggleSalonSelection(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.salonInfo}>
          <Text style={styles.salonName}>{item.name}</Text>
          {item.description && (
            <Text style={styles.salonDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
          {item.address && (
            <Text style={styles.salonAddress}>📍 {item.address}</Text>
          )}
        </View>
        <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
          {isSelected && <Text style={styles.checkmark}>✓</Text>}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Customer Registration</Text>
        <Text style={styles.subtitle}>Create your account and select salons</Text>
      </View>

      <View style={styles.form}>
        <TextInput
          style={styles.input}
          placeholder="First Name"
          value={formData.firstName}
          onChangeText={(value) => handleInputChange('firstName', value)}
          autoCapitalize="words"
        />

        <TextInput
          style={styles.input}
          placeholder="Last Name"
          value={formData.lastName}
          onChangeText={(value) => handleInputChange('lastName', value)}
          autoCapitalize="words"
        />

        <TextInput
          style={styles.input}
          placeholder="Email"
          value={formData.email}
          onChangeText={(value) => handleInputChange('email', value)}
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <TextInput
          style={styles.input}
          placeholder="Phone Number (Optional)"
          value={formData.phoneNumber}
          onChangeText={(value) => handleInputChange('phoneNumber', value)}
          keyboardType="phone-pad"
        />

        <TextInput
          style={styles.input}
          placeholder="Account ID"
          value={formData.accountId}
          onChangeText={(value) => handleInputChange('accountId', value)}
        />

        <TextInput
          style={styles.input}
          placeholder="Password"
          value={formData.password}
          onChangeText={(value) => handleInputChange('password', value)}
          secureTextEntry
        />

        <TextInput
          style={styles.input}
          placeholder="Confirm Password"
          value={formData.confirmPassword}
          onChangeText={(value) => handleInputChange('confirmPassword', value)}
          secureTextEntry
        />

        {/* Salon Selection */}
        <View style={styles.salonSection}>
          <Text style={styles.salonSectionTitle}>Select Salons</Text>
          <Text style={styles.salonSectionSubtitle}>
            Choose one or more salons you want to access
          </Text>

          {isLoadingSalons ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#b363e0" />
              <Text style={styles.loadingText}>Loading salons...</Text>
            </View>
          ) : availableSalons.length > 0 ? (
            <FlatList
              data={availableSalons}
              renderItem={renderSalonItem}
              keyExtractor={(item) => item.id}
              style={styles.salonList}
              scrollEnabled={false}
            />
          ) : formData.accountId ? (
            <Text style={styles.noSalonsText}>
              No salons available for this account
            </Text>
          ) : (
            <Text style={styles.noSalonsText}>
              Enter an Account ID to see available salons
            </Text>
          )}
        </View>

        <TouchableOpacity
          style={[styles.registerButton, isLoading && styles.registerButtonDisabled]}
          onPress={handleRegister}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.registerButtonText}>Register</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.loginLink}
          onPress={() => navigation.navigate('Login' as never)}
        >
          <Text style={styles.loginLinkText}>
            Already have an account? Login
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    paddingBottom: 40,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 32,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    color: '#666',
  },
  form: {
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  salonSection: {
    marginTop: 16,
    marginBottom: 24,
  },
  salonSectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#333',
    marginBottom: 8,
  },
  salonSectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#666',
    marginBottom: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#666',
  },
  salonList: {
    maxHeight: 300,
  },
  salonItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  salonItemSelected: {
    borderColor: '#b363e0',
    backgroundColor: '#f8f4ff',
  },
  salonInfo: {
    flex: 1,
    marginRight: 12,
  },
  salonName: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#333',
    marginBottom: 4,
  },
  salonDescription: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#666',
    marginBottom: 4,
  },
  salonAddress: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#888',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#b363e0',
    borderColor: '#b363e0',
  },
  checkmark: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins-Bold',
  },
  noSalonsText: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#666',
    textAlign: 'center',
    paddingVertical: 20,
  },
  registerButton: {
    backgroundColor: '#b363e0',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  registerButtonDisabled: {
    opacity: 0.6,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
  },
  loginLink: {
    alignItems: 'center',
  },
  loginLinkText: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#b363e0',
  },
});

export default CustomerRegistrationScreen;
