@echo off
"C:\\Program Files\\Microsoft\\jdk-17.0.15.6-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  armeabi-v7a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging15661578948604764603\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7eeb3e74c63d5948104a854a18ed214d\\transformed\\hermes-android-0.79.2-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b60134eb4741b931e894271dbfdd4cc\\transformed\\fbjni-0.7.0\\prefab"
