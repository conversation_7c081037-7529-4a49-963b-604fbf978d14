import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitializePermissions1704000000000 implements MigrationInterface {
  name = 'InitializePermissions1704000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert default permissions
    await queryRunner.query(`
      INSERT INTO permissions (id, code, name, description, category, resource, action, "isActive", "sortOrder", "createdAt", "updatedAt") VALUES
      -- Services Permissions
      (gen_random_uuid(), 'VIEW_SERVICES', 'View Services', 'View salon services and pricing information', 'SERVICES', 'services', 'read', true, 1, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_SERVICES', 'Manage Services', 'Create, edit, and delete salon services', 'SERVICES', 'services', 'write', true, 2, NOW(), NOW()),
      
      -- Offers Permissions
      (gen_random_uuid(), 'VIEW_OFFERS', 'View Offers', 'View salon offers and promotions', 'OFFERS', 'offers', 'read', true, 3, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_OFFERS', 'Manage Offers', 'Create, edit, and delete salon offers', 'OFFERS', 'offers', 'write', true, 4, NOW(), NOW()),
      
      -- Bookings Permissions
      (gen_random_uuid(), 'VIEW_BOOKINGS', 'View Bookings', 'View customer bookings and appointments', 'BOOKINGS', 'bookings', 'read', true, 5, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_BOOKINGS', 'Manage Bookings', 'Create, edit, and cancel customer bookings', 'BOOKINGS', 'bookings', 'write', true, 6, NOW(), NOW()),
      
      -- Customers Permissions
      (gen_random_uuid(), 'VIEW_CUSTOMERS', 'View Customers', 'View customer information and history', 'CUSTOMERS', 'customers', 'read', true, 7, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_CUSTOMERS', 'Manage Customers', 'Edit customer information and preferences', 'CUSTOMERS', 'customers', 'write', true, 8, NOW(), NOW()),
      
      -- Reports Permissions
      (gen_random_uuid(), 'VIEW_REPORTS', 'View Reports', 'View salon analytics and reports', 'REPORTS', 'reports', 'read', true, 9, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_REPORTS', 'Manage Reports', 'Create and configure custom reports', 'REPORTS', 'reports', 'write', true, 10, NOW(), NOW()),
      
      -- Staff Permissions
      (gen_random_uuid(), 'VIEW_STAFF', 'View Staff', 'View other staff members and their information', 'STAFF', 'staff', 'read', true, 11, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_STAFF', 'Manage Staff', 'Manage other staff members (supervisor role)', 'STAFF', 'staff', 'write', true, 12, NOW(), NOW()),
      
      -- Salon Permissions
      (gen_random_uuid(), 'VIEW_SALON_SETTINGS', 'View Salon Settings', 'View salon configuration and settings', 'SALON', 'salon', 'read', true, 13, NOW(), NOW()),
      (gen_random_uuid(), 'MANAGE_SALON_SETTINGS', 'Manage Salon Settings', 'Edit salon configuration and settings', 'SALON', 'salon', 'write', true, 14, NOW(), NOW())
      ON CONFLICT (code) DO NOTHING;
    `);

    console.log('✅ Default permissions initialized successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove default permissions
    await queryRunner.query(`
      DELETE FROM permissions WHERE code IN (
        'VIEW_SERVICES', 'MANAGE_SERVICES',
        'VIEW_OFFERS', 'MANAGE_OFFERS', 
        'VIEW_BOOKINGS', 'MANAGE_BOOKINGS',
        'VIEW_CUSTOMERS', 'MANAGE_CUSTOMERS',
        'VIEW_REPORTS', 'MANAGE_REPORTS',
        'VIEW_STAFF', 'MANAGE_STAFF',
        'VIEW_SALON_SETTINGS', 'MANAGE_SALON_SETTINGS'
      );
    `);

    console.log('✅ Default permissions removed successfully');
  }
}
