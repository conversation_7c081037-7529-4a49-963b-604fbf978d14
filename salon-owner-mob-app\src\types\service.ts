// Service Types (matching backend)
export enum ServiceStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export enum ServiceCategory {
  HAIR_CUT = 'HAIR_CUT',
  HAIR_COLOR = 'HAIR_COLOR',
  HAIR_STYLING = 'HAIR_STYLING',
  HAIR_TREATMENT = 'HAIR_TREATMENT',
  NAIL_CARE = 'NAIL_CARE',
  NAIL_ART = 'NAIL_ART',
  FACIAL = 'FACIAL',
  SKIN_CARE = 'SKIN_CARE',
  MASSAGE = 'MASSAGE',
  WAXING = 'WAXING',
  EYEBROW = 'EYEBROW',
  EYELASH = 'EYELASH',
  MAKEUP = 'MAKEUP',
  BRIDAL = 'BRIDAL',
  SPECIAL_OCCASION = 'SPECIAL_OCCASION',
  PACKAGE = 'PACKAGE',
  OTHER = 'OTHER',
}

export enum DurationType {
  MINUTES = 'MINUTES',
  HOURS = 'HOURS',
}

export enum PricingType {
  FIXED = 'FIXED',
  RANGE = 'RANGE',
  CONSULTATION = 'CONSULTATION',
}

export interface ServiceResponse {
  id: string;
  name: string;
  description?: string;
  category: ServiceCategory;
  status: ServiceStatus;
  pricingType: PricingType;
  price?: number;
  priceMin?: number;
  priceMax?: number;
  duration: number;
  durationType: DurationType;
  bufferTime?: number;
  image?: string;
  images?: string[];
  tags?: string[];
  requirements?: string[];
  preparations?: string[];
  aftercare?: string[];
  isBookable: boolean;
  requiresConsultation: boolean;
  sortOrder: number;
  settings?: Record<string, any>;
  isActive: boolean;
  salonId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateServiceRequest {
  name: string;
  description?: string;
  category: ServiceCategory;
  pricingType?: PricingType;
  price?: number;
  priceMin?: number;
  priceMax?: number;
  duration?: number;
  durationType?: DurationType;
  bufferTime?: number;
  tags?: string[];
  requirements?: string[];
  preparations?: string[];
  aftercare?: string[];
  isBookable?: boolean;
  requiresConsultation?: boolean;
  sortOrder?: number;
  settings?: Record<string, any>;
  salonId: string;
}

export interface UpdateServiceRequest {
  name?: string;
  description?: string;
  category?: ServiceCategory;
  status?: ServiceStatus;
  pricingType?: PricingType;
  price?: number;
  priceMin?: number;
  priceMax?: number;
  duration?: number;
  durationType?: DurationType;
  bufferTime?: number;
  tags?: string[];
  requirements?: string[];
  preparations?: string[];
  aftercare?: string[];
  isBookable?: boolean;
  requiresConsultation?: boolean;
  sortOrder?: number;
  settings?: Record<string, any>;
  isActive?: boolean;
  removeImage?: boolean;
}

export interface ServiceSearchFilters {
  category?: ServiceCategory;
  status?: ServiceStatus;
  pricingType?: PricingType;
  priceMin?: number;
  priceMax?: number;
  durationMin?: number;
  durationMax?: number;
  isBookable?: boolean;
  requiresConsultation?: boolean;
  tags?: string[];
  search?: string;
  sortBy?: 'name' | 'price' | 'duration' | 'category' | 'createdAt' | 'sortOrder';
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

export interface BulkServiceOperation {
  serviceIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'updateCategory' | 'updatePricing';
  data?: {
    status?: ServiceStatus;
    category?: ServiceCategory;
    pricingType?: PricingType;
    price?: number;
    priceMin?: number;
    priceMax?: number;
  };
}

// Service category configurations for UI
export const SERVICE_CATEGORY_CONFIG: Record<ServiceCategory, {
  label: string;
  icon: string;
  color: string;
  defaultDuration: number;
  commonTags: string[];
}> = {
  [ServiceCategory.HAIR_CUT]: {
    label: 'Hair Cut',
    icon: 'content-cut',
    color: '#FF6B6B',
    defaultDuration: 45,
    commonTags: ['trim', 'layers', 'bangs', 'styling'],
  },
  [ServiceCategory.HAIR_COLOR]: {
    label: 'Hair Color',
    icon: 'palette',
    color: '#4ECDC4',
    defaultDuration: 120,
    commonTags: ['highlights', 'lowlights', 'full-color', 'touch-up'],
  },
  [ServiceCategory.HAIR_STYLING]: {
    label: 'Hair Styling',
    icon: 'auto-fix-high',
    color: '#45B7D1',
    defaultDuration: 60,
    commonTags: ['blowout', 'curls', 'updo', 'straightening'],
  },
  [ServiceCategory.HAIR_TREATMENT]: {
    label: 'Hair Treatment',
    icon: 'healing',
    color: '#96CEB4',
    defaultDuration: 90,
    commonTags: ['deep-conditioning', 'keratin', 'protein', 'moisturizing'],
  },
  [ServiceCategory.NAIL_CARE]: {
    label: 'Nail Care',
    icon: 'pan-tool',
    color: '#FFEAA7',
    defaultDuration: 45,
    commonTags: ['manicure', 'pedicure', 'gel', 'polish'],
  },
  [ServiceCategory.NAIL_ART]: {
    label: 'Nail Art',
    icon: 'brush',
    color: '#DDA0DD',
    defaultDuration: 60,
    commonTags: ['design', 'patterns', 'gems', 'custom'],
  },
  [ServiceCategory.FACIAL]: {
    label: 'Facial',
    icon: 'face',
    color: '#FFB6C1',
    defaultDuration: 75,
    commonTags: ['cleansing', 'hydrating', 'anti-aging', 'acne'],
  },
  [ServiceCategory.SKIN_CARE]: {
    label: 'Skin Care',
    icon: 'spa',
    color: '#98D8C8',
    defaultDuration: 60,
    commonTags: ['exfoliation', 'moisturizing', 'treatment', 'consultation'],
  },
  [ServiceCategory.MASSAGE]: {
    label: 'Massage',
    icon: 'self-improvement',
    color: '#F7DC6F',
    defaultDuration: 60,
    commonTags: ['relaxation', 'therapeutic', 'deep-tissue', 'hot-stone'],
  },
  [ServiceCategory.WAXING]: {
    label: 'Waxing',
    icon: 'remove',
    color: '#F1948A',
    defaultDuration: 30,
    commonTags: ['legs', 'arms', 'bikini', 'eyebrows'],
  },
  [ServiceCategory.EYEBROW]: {
    label: 'Eyebrow',
    icon: 'visibility',
    color: '#85C1E9',
    defaultDuration: 30,
    commonTags: ['shaping', 'tinting', 'threading', 'microblading'],
  },
  [ServiceCategory.EYELASH]: {
    label: 'Eyelash',
    icon: 'remove-red-eye',
    color: '#D7BDE2',
    defaultDuration: 90,
    commonTags: ['extensions', 'lift', 'tint', 'perm'],
  },
  [ServiceCategory.MAKEUP]: {
    label: 'Makeup',
    icon: 'face-retouching-natural',
    color: '#F8C471',
    defaultDuration: 45,
    commonTags: ['application', 'lesson', 'special-event', 'trial'],
  },
  [ServiceCategory.BRIDAL]: {
    label: 'Bridal',
    icon: 'favorite',
    color: '#F5B7B1',
    defaultDuration: 180,
    commonTags: ['wedding', 'trial', 'package', 'party'],
  },
  [ServiceCategory.SPECIAL_OCCASION]: {
    label: 'Special Occasion',
    icon: 'event',
    color: '#AED6F1',
    defaultDuration: 120,
    commonTags: ['prom', 'party', 'photoshoot', 'event'],
  },
  [ServiceCategory.PACKAGE]: {
    label: 'Package',
    icon: 'card-giftcard',
    color: '#A9DFBF',
    defaultDuration: 180,
    commonTags: ['combo', 'deal', 'multiple-services', 'discount'],
  },
  [ServiceCategory.OTHER]: {
    label: 'Other',
    icon: 'more-horiz',
    color: '#D5DBDB',
    defaultDuration: 60,
    commonTags: ['custom', 'consultation', 'special'],
  },
};

// Utility functions
export const formatPrice = (service: ServiceResponse): string => {
  if (service.pricingType === PricingType.CONSULTATION) {
    return 'Consultation';
  } else if (service.pricingType === PricingType.RANGE) {
    
    
    const min =  service.priceMin  ? Number(service.priceMin) : 0;
    const max =  service.priceMax ? Number(service.priceMax) : 0;
    return `$${min.toFixed(0)} - $${max.toFixed(0)}`;
  } else {
    // FIXED pricing
    const price =  service.price  ? Number(service.price) : 0;
    return `$${price.toFixed(0)}`;
  }
};

export const formatDuration = (service: ServiceResponse): string => {
  const duration = typeof service.duration === 'number' ? service.duration : 0;
  const bufferTime = typeof service.bufferTime === 'number' ? service.bufferTime : 0;
  const total = duration + bufferTime;

  if (service.durationType === DurationType.HOURS) {
    const hours = duration;
    const totalHours = total;
    return bufferTime > 0
      ? `${hours}h (${totalHours}h total)`
      : `${hours}h`;
  } else {
    // MINUTES
    const minutes = duration;
    const totalMinutes = total;
    return bufferTime > 0
      ? `${minutes}m (${totalMinutes}m total)`
      : `${minutes}m`;
  }
};

export const getCategoryConfig = (category: ServiceCategory) => {
  return SERVICE_CATEGORY_CONFIG[category];
};

export const getServiceStatusColor = (status: ServiceStatus): string => {
  switch (status) {
    case ServiceStatus.ACTIVE:
      return '#4CAF50';
    case ServiceStatus.INACTIVE:
      return '#FF9800';
    case ServiceStatus.SUSPENDED:
      return '#F44336';
    default:
      return '#9E9E9E';
  }
};

export const getServiceStatusLabel = (status: ServiceStatus): string => {
  switch (status) {
    case ServiceStatus.ACTIVE:
      return 'Active';
    case ServiceStatus.INACTIVE:
      return 'Inactive';
    case ServiceStatus.SUSPENDED:
      return 'Suspended';
    default:
      return 'Unknown';
  }
};

export const validateServiceForm = (service: Partial<CreateServiceRequest>): string[] => {
  const errors: string[] = [];

  if (!service.name || service.name.trim().length < 2) {
    errors.push('Service name must be at least 2 characters long');
  }

  if (!service.category) {
    errors.push('Service category is required');
  }

  if (service.pricingType === PricingType.FIXED && (!service.price || service.price <= 0)) {
    errors.push('Valid price is required for fixed pricing');
  }

  if (service.pricingType === PricingType.RANGE) {
    if (!service.priceMin || service.priceMin <= 0) {
      errors.push('Valid minimum price is required for range pricing');
    }
    if (!service.priceMax || service.priceMax <= 0) {
      errors.push('Valid maximum price is required for range pricing');
    }
    if (service.priceMin && service.priceMax && service.priceMin >= service.priceMax) {
      errors.push('Maximum price must be greater than minimum price');
    }
  }

  if (service.duration && service.duration <= 0) {
    errors.push('Duration must be greater than 0');
  }

  if (service.bufferTime && service.bufferTime < 0) {
    errors.push('Buffer time cannot be negative');
  }

  return errors;
};
