import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { DarkTitle, DarkBody } from '../../components/common/Typography';

import { Colors } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import { staffService } from '../../services/staffService';
import { salonService } from '../../services/salonService';
import {
  CreateStaffRequest,
  StaffPermission,
  StaffPermissionInfo,
  StaffPosition,
  PositionOption,
  getDefaultPermissions,
  getDefaultWorkingHours,
  getPositionOptions,
  getPositionsByCategory,
  validateStaffForm,
  getPermissionLabel,
  getPermissionDescription,
} from '../../types/staff';
import { Input } from '../../components/common';

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  backButton: {
    padding: width * 0.02,
  },
  headerTitle: {
    color: Colors.textPrimary,
  },
  headerSpacer: {
    width: width * 0.08,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.gray50,
  },
  errorContainer: {
    backgroundColor: Colors.error + '20',
    margin: width * 0.05,
    padding: width * 0.04,
    borderRadius: width * 0.02,
    borderLeftWidth: 4,
    borderLeftColor: Colors.error,
  },
  errorText: {
    color: Colors.error,
    marginBottom: height * 0.005,
  },
  section: {
    backgroundColor: Colors.white,
    margin: width * 0.05,
    marginBottom: height * 0.02,
    padding: width * 0.05,
    borderRadius: width * 0.03,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    color: Colors.textPrimary,
    marginBottom: height * 0.01,
  },
  sectionDescription: {
    color: Colors.textSecondary,
    marginBottom: height * 0.02,
  },
  permissionItem: {
    marginBottom: height * 0.015,
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  permissionInfo: {
    flex: 1,
    marginRight: width * 0.03,
  },
  permissionLabel: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  permissionDescription: {
    color: Colors.textSecondary,
  },
  checkbox: {
    width: width * 0.06,
    height: width * 0.06,
    borderRadius: width * 0.01,
    borderWidth: 2,
    borderColor: Colors.gray300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  submitContainer: {
    padding: width * 0.05,
    paddingBottom: height * 0.05,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    paddingVertical: height * 0.02,
    borderRadius: width * 0.03,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: Colors.gray400,
  },
  submitButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  noSalonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  noSalonText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  positionSelector: {
    marginBottom: height * 0.02,
  },
  inputLabel: {
    color: Colors.textPrimary,
    marginBottom: height * 0.01,
    fontWeight: '500',
  },
  positionSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: width * 0.02,
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.015,
    backgroundColor: Colors.white,
  },
  positionSelectorText: {
    color: Colors.textPrimary,
    flex: 1,
  },
  positionSelectorPlaceholder: {
    color: Colors.textSecondary,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.04,
    width: width * 0.9,
    maxHeight: height * 0.7,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: width * 0.05,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  modalTitle: {
    color: Colors.textPrimary,
  },
  modalBody: {
    maxHeight: height * 0.5,
    padding: width * 0.05,
  },
  positionCategory: {
    marginBottom: height * 0.025,
  },
  positionCategoryTitle: {
    color: Colors.textPrimary,
    fontWeight: '600',
    marginBottom: height * 0.01,
    paddingBottom: height * 0.005,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  positionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: height * 0.015,
    paddingHorizontal: width * 0.03,
    borderRadius: width * 0.02,
    marginBottom: height * 0.005,
  },
  positionOptionSelected: {
    backgroundColor: Colors.primary + '20',
  },
  positionOptionText: {
    color: Colors.textPrimary,
    flex: 1,
  },
  positionOptionTextSelected: {
    color: Colors.primary,
    fontWeight: '600',
  },
});

export const AddStaffScreen: React.FC = () => {
  const navigation = useNavigation();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();

  // Form state
  const [formData, setFormData] = useState<Partial<CreateStaffRequest>>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    password: '',
    position: undefined,
    hourlyRate: undefined,
    permissions: getDefaultPermissions(),
    workingHours: getDefaultWorkingHours() as any,
    notes: '',
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [availablePermissions, setAvailablePermissions] = useState<StaffPermissionInfo[]>([]);
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [selectedSalonIds, setSelectedSalonIds] = useState<string[]>(selectedSalon ? [selectedSalon.id] : []);
  const [showPositionPicker, setShowPositionPicker] = useState(false);

  // Load available permissions
  useEffect(() => {
    loadPermissions();
  }, []);

  const loadPermissions = async () => {
    try {
      const permissions = await staffService.getAvailablePermissions();
      setAvailablePermissions(permissions);
      console.log('Permissions loaded:', permissions);
      
    } catch (error) {
      console.error('Error loading permissions:', error);
      // Use fallback permissions
      const fallbackPermissions: StaffPermissionInfo[] = Object.values(StaffPermission).map(permission => ({
        id: permission,
        code: permission,
        name: getPermissionLabel(permission),
        label: getPermissionLabel(permission),
        description: getPermissionDescription(permission),
        category: 'general',
        categoryLabel: 'General',
        value: permission,
      }));
      setAvailablePermissions(fallbackPermissions);
    }
  };

  // Form handlers
  const updateFormData = (field: keyof CreateStaffRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const togglePermission = (permissionCode: string) => {
    const currentPermissions = formData.permissions || [];
    const hasPermission = currentPermissions.includes(permissionCode);

    if (hasPermission) {
      updateFormData('permissions', currentPermissions.filter(p => p !== permissionCode));
    } else {
      updateFormData('permissions', [...currentPermissions, permissionCode]);
    }
  };

  // Validation and submission
  const handleSubmit = async () => {
    if (!selectedSalon) {
      showToast('No salon selected', 'error');
      return;
    }

    // Validate form
    const validationErrors = validateStaffForm({
      ...formData,
      salonIds: selectedSalonIds.length > 0 ? selectedSalonIds : [selectedSalon.id],
    });

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      showToast('Please fix the errors below', 'error');
      return;
    }

    try {
      setLoading(true);

      const staffData: CreateStaffRequest = {
        firstName: formData.firstName!,
        lastName: formData.lastName!,
        email: formData.email!,
        phoneNumber: formData.phoneNumber,
        password: formData.password!,
        salonIds: selectedSalonIds.length > 0 ? selectedSalonIds : [selectedSalon.id],
        position: formData.position,
        hourlyRate: formData.hourlyRate,
        permissions: formData.permissions!,
        workingHours: formData.workingHours,
        startDate: new Date(),
        notes: formData.notes,
      };

      await staffService.createStaff(staffData);
      
      showToast('Staff member added successfully', 'success');
      navigation.goBack();
    } catch (error: any) {
      console.error('Error creating staff:', error);
      showToast(error.message || 'Failed to add staff member', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Show no salon selected
  if (!selectedSalon) {
    return (
      <ScreenWrapper>
        <View style={styles.noSalonContainer}>
          <Icon name="business" size={width * 0.2} color={Colors.gray400} />
          <DarkTitle level={3} textAlign="center" style={styles.noSalonText}>
            No salon selected
          </DarkTitle>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={2} style={styles.headerTitle}>
          Add Staff Member
        </DarkTitle>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Error Messages */}
        {errors.length > 0 && (
          <View style={styles.errorContainer}>
            {errors.map((error, index) => (
              <DarkBody key={index} size="small" style={styles.errorText}>
                • {error}
              </DarkBody>
            ))}
          </View>
        )}

        {/* Personal Information */}
        <View style={styles.section}>
          <DarkTitle level={3} style={styles.sectionTitle}>
            Personal Information
          </DarkTitle>

          <Input
            label="First Name"
            value={formData.firstName}
            onChangeText={(value) => updateFormData('firstName', value)}
            placeholder="Enter first name"
            
          />

          <Input
            label="Last Name"
            value={formData.lastName}
            onChangeText={(value) => updateFormData('lastName', value)}
            placeholder="Enter last name"
            
          />

          <Input
            label="Email"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
            
          />

          <Input
            label="Phone Number"
            value={formData.phoneNumber}
            onChangeText={(value) => updateFormData('phoneNumber', value)}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />

          <Input
            label="Password"
            value={formData.password}
            onChangeText={(value) => updateFormData('password', value)}
            placeholder="Enter password"
            secureTextEntry={!showPassword}
            rightIcon={
              <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                <Icon 
                  name={showPassword ? 'visibility-off' : 'visibility'} 
                  size={20} 
                  color={Colors.textSecondary} 
                />
              </TouchableOpacity>
            }
            
          />
        </View>

        {/* Job Information */}
        <View style={styles.section}>
          <DarkTitle level={3} style={styles.sectionTitle}>
            Job Information
          </DarkTitle>

          <TouchableOpacity
            style={styles.positionSelector}
            onPress={() => setShowPositionPicker(true)}
          >
            <DarkBody size="small" style={styles.inputLabel}>
              Position/Title
            </DarkBody>
            <View style={styles.positionSelectorContent}>
              <DarkBody size="medium" style={[
                styles.positionSelectorText,
                !formData.position && styles.positionSelectorPlaceholder
              ]}>
                {formData.position ?
                  getPositionOptions().find(p => p.value === formData.position)?.label || formData.position :
                  'Select position'
                }
              </DarkBody>
              <Icon name="keyboard-arrow-down" size={24} color={Colors.textSecondary} />
            </View>
          </TouchableOpacity>

          <Input
            label="Hourly Rate ($)"
            value={formData.hourlyRate?.toString()}
            onChangeText={(value) => updateFormData('hourlyRate', value ? parseFloat(value) : undefined)}
            placeholder="Enter hourly rate"
            keyboardType="numeric"
          />

          <Input
            label="Notes"
            value={formData.notes}
            onChangeText={(value) => updateFormData('notes', value)}
            placeholder="Additional notes about this staff member"
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Permissions */}
        <View style={styles.section}>
          <DarkTitle level={3} style={styles.sectionTitle}>
            Permissions
          </DarkTitle>
          <DarkBody size="small" style={styles.sectionDescription}>
            Select what this staff member can do in your salon
          </DarkBody>

          {availablePermissions.map((permission) => {
            const permissionCode = permission.code || permission.value || '';
            const permissionLabel = permission.name || permission.label || '';
            return (
              <TouchableOpacity
                key={permissionCode}
                style={styles.permissionItem}
                onPress={() => togglePermission(permissionCode)}
              >
                <View style={styles.permissionContent}>
                  <View style={styles.permissionInfo}>
                    <DarkBody size="medium" style={styles.permissionLabel}>
                      {permissionLabel}
                    </DarkBody>
                    <DarkBody size="small" style={styles.permissionDescription}>
                      {permission.description}
                    </DarkBody>
                  </View>
                  <View style={[
                    styles.checkbox,
                    formData.permissions?.includes(permissionCode) && styles.checkboxChecked
                  ]}>
                    {formData.permissions?.includes(permissionCode) && (
                      <Icon name="check" size={16} color={Colors.white} />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color={Colors.white} />
            ) : (
              <DarkBody size="medium" style={styles.submitButtonText}>
                Add Staff Member
              </DarkBody>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Position Picker Modal */}
      {showPositionPicker && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <DarkTitle level={3} style={styles.modalTitle}>
                Select Position
              </DarkTitle>
              <TouchableOpacity onPress={() => setShowPositionPicker(false)}>
                <Icon name="close" size={24} color={Colors.textPrimary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              {Object.entries(getPositionsByCategory()).map(([category, positions]) => (
                <View key={category} style={styles.positionCategory}>
                  <DarkBody size="medium" style={styles.positionCategoryTitle}>
                    {category}
                  </DarkBody>
                  {positions.map((position) => (
                    <TouchableOpacity
                      key={position.value}
                      style={[
                        styles.positionOption,
                        formData.position === position.value && styles.positionOptionSelected
                      ]}
                      onPress={() => {
                        updateFormData('position', position.value);
                        setShowPositionPicker(false);
                      }}
                    >
                      <DarkBody size="medium" style={[
                        styles.positionOptionText,
                        formData.position === position.value && styles.positionOptionTextSelected
                      ]}>
                        {position.label}
                      </DarkBody>
                      {formData.position === position.value && (
                        <Icon name="check" size={20} color={Colors.primary} />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </ScreenWrapper>
  );
};
