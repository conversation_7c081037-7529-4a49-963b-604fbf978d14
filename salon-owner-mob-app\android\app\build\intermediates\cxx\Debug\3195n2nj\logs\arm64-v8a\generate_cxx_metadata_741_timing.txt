# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 368ms
  generate-prefab-packages
    [gap of 27ms]
    exec-prefab 2339ms
    [gap of 52ms]
  generate-prefab-packages completed in 2418ms
  execute-generate-process
    exec-configure 1476ms
    [gap of 530ms]
  execute-generate-process completed in 2006ms
  remove-unexpected-so-files 10ms
  [gap of 85ms]
generate_cxx_metadata completed in 4902ms

