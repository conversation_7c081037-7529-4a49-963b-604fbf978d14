# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 54ms
  [gap of 19ms]
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata 27ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 44ms
  [gap of 18ms]
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 77ms]
  create-invalidation-state 94ms
  [gap of 20ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 202ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 35ms
  [gap of 10ms]
generate_cxx_metadata completed in 75ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 16ms
generate_cxx_metadata completed in 32ms

