import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image,
  ActivityIndicator,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useToast } from '../../context/ToastContext';
import {
  DarkTitle,
  DarkBody,
  WhiteTitle,
} from '../../components/common/Typography';
import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { Colors, Gradients } from '../../constants/colors';
import { customerServiceService } from '../../services/customerServiceService';
import { customerOfferService, CustomerOffer } from '../../services/customerOfferService';
import { ServiceResponse } from '../../types/service';

const { width, height } = Dimensions.get('window');

interface RouteParams {
  serviceId: string;
}

export const ServiceDetailsScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { showToast } = useToast();
  const { serviceId } = route.params as RouteParams;

  // State
  const [loading, setLoading] = useState(true);
  const [service, setService] = useState<ServiceResponse | null>(null);
  const [relatedOffers, setRelatedOffers] = useState<CustomerOffer[]>([]);

  // Load service details
  useEffect(() => {
    loadServiceDetails();
  }, [serviceId]);

  const loadServiceDetails = async () => {
    try {
      setLoading(true);
      console.log('🔍 ServiceDetailsScreen: Loading service details:', serviceId);

      const serviceData = await customerServiceService.getServiceById(serviceId);
      setService(serviceData);

      // Load related offers if service has category
      if (serviceData.category) {
        const offers = await customerOfferService.getOffersByCategory(
          serviceData.salonId,
          serviceData.category
        );
        setRelatedOffers(offers);
      }

      console.log('✅ ServiceDetailsScreen: Service details loaded');
    } catch (error: any) {
      console.error('❌ ServiceDetailsScreen: Error loading service details:', error);
      showToast('Failed to load service details', 'error', 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = () => {
    console.log('📱 Book now pressed for service:', service?.name);
    // Navigate to booking screen
    // navigation.navigate('BookService', { serviceId: service?.id });
    showToast('Booking feature coming soon!', 'info', 2000);
  };

  const handleOfferPress = (offer: CustomerOffer) => {
    console.log('📱 Offer pressed:', offer.title);
    // Navigate to offer details
    // navigation.navigate('OfferDetails', { offerId: offer.id });
  };

  const renderOffer = (offer: CustomerOffer, index: number) => (
    <TouchableOpacity
      key={offer.id}
      style={styles.offerCard}
      onPress={() => handleOfferPress(offer)}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={Gradients.primaryButton}
        style={styles.offerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.offerContent}>
          <WhiteTitle level={4}>{offer.title}</WhiteTitle>
          <WhiteTitle level={3} style={styles.offerDiscount}>
            {customerOfferService.formatDiscount(offer)}
          </WhiteTitle>
          <DarkBody size="small" style={styles.offerValid}>
            Valid until {customerOfferService.formatValidPeriod(offer)}
          </DarkBody>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody size="medium" style={styles.loadingText}>
            Loading service details...
          </DarkBody>
        </View>
      </ScreenWrapper>
    );
  }

  if (!service) {
    return (
      <ScreenWrapper>
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={width * 0.15} color={Colors.gray400} />
          <DarkTitle level={3} textAlign="center" style={styles.errorTitle}>
            Service not found
          </DarkTitle>
          <DarkBody size="medium" textAlign="center" style={styles.errorText}>
            The service you're looking for doesn't exist or has been removed.
          </DarkBody>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <DarkBody size="medium" style={styles.backButtonText}>
              Go Back
            </DarkBody>
          </TouchableOpacity>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backIcon}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.favoriteIcon}>
            <Icon name="favorite-border" size={24} color={Colors.white} />
          </TouchableOpacity>
        </View>

        {/* Service Image */}
        {service.image && (
          <Image source={{ uri: service.image }} style={styles.serviceImage} />
        )}

        {/* Service Info */}
        <View style={styles.serviceInfo}>
          <View style={styles.serviceHeader}>
            <View style={styles.serviceTitleSection}>
              <DarkTitle level={2} style={styles.serviceName}>
                {service.name}
              </DarkTitle>
              <DarkBody size="medium" style={styles.serviceCategory}>
                {service.category}
              </DarkBody>
            </View>
            <View style={styles.servicePriceSection}>
              <DarkTitle level={2} style={styles.servicePrice}>
                {service.formattedPrice}
              </DarkTitle>
              <DarkBody size="small" style={styles.serviceDuration}>
                {service.formattedDuration}
              </DarkBody>
            </View>
          </View>

          {/* Service Description */}
          {service.description && (
            <View style={styles.descriptionSection}>
              <DarkTitle level={3} style={styles.sectionTitle}>
                Description
              </DarkTitle>
              <DarkBody size="medium" style={styles.description}>
                {service.description}
              </DarkBody>
            </View>
          )}

          {/* Service Details */}
          <View style={styles.detailsSection}>
            <DarkTitle level={3} style={styles.sectionTitle}>
              Service Details
            </DarkTitle>
            <View style={styles.detailItem}>
              <Icon name="schedule" size={20} color={Colors.primary} />
              <DarkBody size="medium" style={styles.detailText}>
                Duration: {service.formattedDuration}
              </DarkBody>
            </View>
            <View style={styles.detailItem}>
              <Icon name="attach-money" size={20} color={Colors.primary} />
              <DarkBody size="medium" style={styles.detailText}>
                Price: {service.formattedPrice}
              </DarkBody>
            </View>
            {service.isBookable && (
              <View style={styles.detailItem}>
                <Icon name="event-available" size={20} color={Colors.primary} />
                <DarkBody size="medium" style={styles.detailText}>
                  Available for booking
                </DarkBody>
              </View>
            )}
          </View>

          {/* Related Offers */}
          {relatedOffers.length > 0 && (
            <View style={styles.offersSection}>
              <DarkTitle level={3} style={styles.sectionTitle}>
                Special Offers
              </DarkTitle>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.offersContainer}
              >
                {relatedOffers.map((offer, index) => renderOffer(offer, index))}
              </ScrollView>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Book Now Button */}
      <View style={styles.bookingSection}>
        <TouchableOpacity
          style={styles.bookButton}
          onPress={handleBookNow}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={Gradients.primaryButton}
            style={styles.bookButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <WhiteTitle level={3}>Book Now</WhiteTitle>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  contentContainer: {
    paddingBottom: height * 0.12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  errorTitle: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  errorText: {
    color: Colors.textSecondary,
    marginBottom: height * 0.03,
  },
  backButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.015,
    borderRadius: width * 0.02,
  },
  backButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  header: {
    position: 'absolute',
    top: height * 0.05,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    zIndex: 10,
  },
  backIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceImage: {
    width: '100%',
    height: height * 0.35,
  },
  serviceInfo: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: width * 0.06,
    borderTopRightRadius: width * 0.06,
    marginTop: -width * 0.06,
    paddingHorizontal: width * 0.05,
    paddingTop: width * 0.06,
    paddingBottom: width * 0.05,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: height * 0.03,
  },
  serviceTitleSection: {
    flex: 1,
  },
  serviceName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  serviceCategory: {
    color: Colors.textSecondary,
  },
  servicePriceSection: {
    alignItems: 'flex-end',
  },
  servicePrice: {
    color: Colors.primary,
    fontWeight: '700',
  },
  serviceDuration: {
    color: Colors.textSecondary,
  },
  descriptionSection: {
    marginBottom: height * 0.03,
  },
  sectionTitle: {
    color: Colors.textPrimary,
    marginBottom: height * 0.015,
  },
  description: {
    color: Colors.textSecondary,
    lineHeight: 22,
  },
  detailsSection: {
    marginBottom: height * 0.03,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.015,
  },
  detailText: {
    color: Colors.textPrimary,
    marginLeft: width * 0.03,
  },
  offersSection: {
    marginBottom: height * 0.02,
  },
  offersContainer: {
    paddingRight: width * 0.05,
  },
  offerCard: {
    marginRight: width * 0.04,
    borderRadius: width * 0.03,
    overflow: 'hidden',
    width: width * 0.6,
  },
  offerGradient: {
    padding: width * 0.04,
  },
  offerContent: {
    alignItems: 'flex-start',
  },
  offerDiscount: {
    marginVertical: height * 0.01,
  },
  offerValid: {
    color: Colors.white,
    opacity: 0.9,
  },
  bookingSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.white,
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  bookButton: {
    borderRadius: width * 0.03,
    overflow: 'hidden',
  },
  bookButtonGradient: {
    paddingVertical: height * 0.02,
    alignItems: 'center',
  },
});
