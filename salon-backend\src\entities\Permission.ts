import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum PermissionCategory {
  SERVICES = 'SERVICES',
  OFFERS = 'OFFERS',
  BOOKINGS = 'BOOKINGS',
  CUSTOMERS = 'CUSTOMERS',
  REPORTS = 'REPORTS',
  STAFF = 'STAFF',
  SALON = 'SALON',
}

@Entity('permissions')
@Index(['code'])
@Index(['category'])
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Unique permission code',
  })
  code!: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Human-readable permission name',
  })
  name!: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Detailed description of what this permission allows',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: PermissionCategory,
    comment: 'Category this permission belongs to',
  })
  category!: PermissionCategory;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Resource this permission applies to',
  })
  resource?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Action this permission allows (create, read, update, delete)',
  })
  action?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this permission is active and can be assigned',
  })
  isActive!: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order for UI',
  })
  sortOrder!: number;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Virtual properties
  get displayName(): string {
    return this.name || this.code.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }

  get fullDescription(): string {
    if (this.description) {
      return this.description;
    }
    
    // Generate description from code if none provided
    const action = this.action || 'access';
    const resource = this.resource || this.category.toLowerCase();
    return `Allows user to ${action} ${resource}`;
  }
}

// Default permissions data
export const DEFAULT_PERMISSIONS = [
  // Services
  {
    code: 'VIEW_SERVICES',
    name: 'View Services',
    description: 'View salon services and pricing information',
    category: PermissionCategory.SERVICES,
    resource: 'services',
    action: 'read',
    sortOrder: 1,
  },
  {
    code: 'MANAGE_SERVICES',
    name: 'Manage Services',
    description: 'Create, edit, and delete salon services',
    category: PermissionCategory.SERVICES,
    resource: 'services',
    action: 'write',
    sortOrder: 2,
  },
  
  // Offers
  {
    code: 'VIEW_OFFERS',
    name: 'View Offers',
    description: 'View salon offers and promotions',
    category: PermissionCategory.OFFERS,
    resource: 'offers',
    action: 'read',
    sortOrder: 3,
  },
  {
    code: 'MANAGE_OFFERS',
    name: 'Manage Offers',
    description: 'Create, edit, and delete salon offers',
    category: PermissionCategory.OFFERS,
    resource: 'offers',
    action: 'write',
    sortOrder: 4,
  },
  
  // Bookings
  {
    code: 'VIEW_BOOKINGS',
    name: 'View Bookings',
    description: 'View customer bookings and appointments',
    category: PermissionCategory.BOOKINGS,
    resource: 'bookings',
    action: 'read',
    sortOrder: 5,
  },
  {
    code: 'MANAGE_BOOKINGS',
    name: 'Manage Bookings',
    description: 'Create, edit, and cancel customer bookings',
    category: PermissionCategory.BOOKINGS,
    resource: 'bookings',
    action: 'write',
    sortOrder: 6,
  },
  
  // Customers
  {
    code: 'VIEW_CUSTOMERS',
    name: 'View Customers',
    description: 'View customer information and history',
    category: PermissionCategory.CUSTOMERS,
    resource: 'customers',
    action: 'read',
    sortOrder: 7,
  },
  {
    code: 'MANAGE_CUSTOMERS',
    name: 'Manage Customers',
    description: 'Edit customer information and preferences',
    category: PermissionCategory.CUSTOMERS,
    resource: 'customers',
    action: 'write',
    sortOrder: 8,
  },
  
  // Reports
  {
    code: 'VIEW_REPORTS',
    name: 'View Reports',
    description: 'View salon analytics and reports',
    category: PermissionCategory.REPORTS,
    resource: 'reports',
    action: 'read',
    sortOrder: 9,
  },
  {
    code: 'MANAGE_REPORTS',
    name: 'Manage Reports',
    description: 'Create and configure custom reports',
    category: PermissionCategory.REPORTS,
    resource: 'reports',
    action: 'write',
    sortOrder: 10,
  },
  
  // Staff
  {
    code: 'VIEW_STAFF',
    name: 'View Staff',
    description: 'View other staff members and their information',
    category: PermissionCategory.STAFF,
    resource: 'staff',
    action: 'read',
    sortOrder: 11,
  },
  {
    code: 'MANAGE_STAFF',
    name: 'Manage Staff',
    description: 'Manage other staff members (supervisor role)',
    category: PermissionCategory.STAFF,
    resource: 'staff',
    action: 'write',
    sortOrder: 12,
  },
  
  // Salon
  {
    code: 'VIEW_SALON_SETTINGS',
    name: 'View Salon Settings',
    description: 'View salon configuration and settings',
    category: PermissionCategory.SALON,
    resource: 'salon',
    action: 'read',
    sortOrder: 13,
  },
  {
    code: 'MANAGE_SALON_SETTINGS',
    name: 'Manage Salon Settings',
    description: 'Edit salon configuration and settings',
    category: PermissionCategory.SALON,
    resource: 'salon',
    action: 'write',
    sortOrder: 14,
  },
];
