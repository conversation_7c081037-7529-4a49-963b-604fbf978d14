import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { staffService, CreateStaffRequest, UpdateStaffRequest, StaffSearchFilters } from '../services/staffService';
import { StaffAccessStatus, StaffPosition } from '../entities/StaffSalonAccess';
import { permissionService } from '../services/permissionService';
import { azureBlobStorage } from '../config/azure';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import ApiError from '../utils/apiError';
import { IUser } from '@/types/user';


interface AuthenticatedRequest extends Request {
  user?: IUser;
}

// Helper function to validate and convert position string to enum
const validatePosition = (position: string | undefined): StaffPosition | undefined => {
  if (!position) return undefined;

  // Check if the position is a valid enum value
  if (Object.values(StaffPosition).includes(position as StaffPosition)) {
    return position as StaffPosition;
  }

  return undefined;
};

class StaffController {
  /**
   * Create a new staff member
   */
  async createStaff(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.error('❌ StaffController: Validation errors:', errors.array());
        console.error('❌ StaffController: Request body:', req.body);
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
        return;
      }

      // Parse FormData fields (when multipart) or use direct values (when JSON)
      const parseFormField = (field: any) => {
        if (typeof field === 'string') {
          try {
            return JSON.parse(field);
          } catch {
            return field;
          }
        }
        return field;
      };

      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        position,
        hourlyRate,
        notes,
      } = req.body;

      // Parse complex fields that might be JSON strings in FormData
      const salonIds = parseFormField(req.body.salonIds);
      const permissions = parseFormField(req.body.permissions);
      const workingHours = parseFormField(req.body.workingHours);
      const startDate = req.body.startDate;

      // Handle optional profile image upload
      let profileImageUrl: string | undefined;
      if (req.file) {
        try {
          // Generate a unique blob name for the staff profile image
          const blobName = `staff-images/${uuidv4()}${path.extname(req.file.originalname)}`;

          // Upload to Azure Blob Storage
          profileImageUrl = await azureBlobStorage.uploadBlob(
            blobName,
            req.file.buffer,
            req.file.mimetype
          );
          console.log('✅ Staff profile image uploaded:', profileImageUrl);
        } catch (uploadError) {
          console.error('❌ Error uploading staff profile image:', uploadError);
          // Continue with staff creation even if image upload fails
          profileImageUrl = undefined;
        }
      }

      const createStaffData: CreateStaffRequest = {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        salonIds: salonIds || [],
        position: validatePosition(position),
        hourlyRate,
        permissions: permissions || ['VIEW_SERVICES', 'VIEW_BOOKINGS'],
        workingHours,
        startDate: startDate ? new Date(startDate) : undefined,
        notes,
        profileImage: profileImageUrl,
        grantedBy: req.user!.id,
      };

      const result = await staffService.createStaff(createStaffData);

      res.status(201).json({
        success: true,
        data: {
          user: {
            id: result.user.id,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            email: result.user.email,
            phoneNumber: result.user.phoneNumber,
            role: result.user.role,
            profileImage: result.user.profileImage,
            isActive: result.user.isActive,
            createdAt: result.user.createdAt,
          },
          accesses: result.accesses,
        },
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error creating staff:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get staff members for a salon
   */
  async getStaffBySalon(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { salonId } = req.params;
      
      const filters: StaffSearchFilters = {
        salonId,
        status: req.query.status as StaffAccessStatus,
        position: validatePosition(req.query.position as string),
        search: req.query.search as string,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as any || 'DESC',
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      };

      const result = await staffService.getStaffBySalon(salonId, filters);

      // Format the response to include permissions
      const formattedData = {
        ...result,
        data: result.data.map(staffAccess => ({
          ...staffAccess,
          permissions: staffAccess.staffPermissions
            ?.filter(sp => sp.isCurrentlyActive)
            ?.map(sp => ({
              id: sp.permission.id,
              code: sp.permission.code,
              name: sp.permission.name,
              description: sp.permission.description,
              category: sp.permission.category,
              grantedAt: sp.grantedAt,
              grantedBy: sp.grantedBy,
            })) || [],
          permissionCodes: staffAccess.activePermissions || [],
        }))
      };

      res.status(200).json({
        success: true,
        data: formattedData,
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error getting staff:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get staff member by ID
   */
  async getStaffById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { staffId } = req.params;
      const { salonId } = req.query;

      const staff = await staffService.getStaffById(staffId, salonId as string);

      // Format the response to include permissions
      const formattedStaff = {
        ...staff,
        permissions: staff.staffPermissions
          ?.filter(sp => sp.isCurrentlyActive)
          ?.map(sp => ({
            id: sp.permission.id,
            code: sp.permission.code,
            name: sp.permission.name,
            description: sp.permission.description,
            category: sp.permission.category,
            grantedAt: sp.grantedAt,
            grantedBy: sp.grantedBy,
          })) || [],
        permissionCodes: staff.activePermissions || [],
      };

      res.status(200).json({
        success: true,
        data: formattedStaff,
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error getting staff member:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Update staff member
   */
  async updateStaff(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { staffId } = req.params;
      const { salonId } = req.body;

      // Handle optional profile image upload
      let profileImageUrl: string | undefined;
      if (req.file) {
        try {
          // Generate a unique blob name for the staff profile image
          const blobName = `staff-images/${uuidv4()}${path.extname(req.file.originalname)}`;

          // Upload to Azure Blob Storage
          profileImageUrl = await azureBlobStorage.uploadBlob(
            blobName,
            req.file.buffer,
            req.file.mimetype
          );
          console.log('✅ Staff profile image updated:', profileImageUrl);
        } catch (uploadError) {
          console.error('❌ Error updating staff profile image:', uploadError);
          // Continue with update even if image upload fails
        }
      }

      const updateData: UpdateStaffRequest = {
        ...req.body,
        position: validatePosition(req.body.position),
        profileImage: profileImageUrl,
        lastModifiedBy: req.user!.id,
      };

      const updatedStaff = await staffService.updateStaff(staffId, salonId, updateData);

      res.status(200).json({
        success: true,
        data: updatedStaff,
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error updating staff:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Delete staff member
   */
  async deleteStaff(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { staffId } = req.params;
      const { salonId } = req.body;

      await staffService.deleteStaff(staffId, salonId, req.user!.id);

      res.status(200).json({
        success: true,
        message: 'Staff member deleted successfully',
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error deleting staff:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get salons accessible by staff member
   */
  async getStaffSalons(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { staffId } = req.params;

      const salons = await staffService.getStaffSalons(staffId);

      res.status(200).json({
        success: true,
        data: salons,
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error getting staff salons:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get my salons (for authenticated staff)
   */
  async getMySalons(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
     const { staffId } = req.params;


      const salons = await staffService.getStaffSalons(staffId);
      console.log("salons ======>", salons);

      // Format the response to include permissions
      const formattedSalons = salons.map(salonAccess => ({
        ...salonAccess,
        permissions: salonAccess.staffPermissions
          ?.filter(sp => sp.isCurrentlyActive)
          ?.map(sp => ({
            id: sp.permission.id,
            code: sp.permission.code,
            name: sp.permission.name,
            description: sp.permission.description,
            category: sp.permission.category,
            grantedAt: sp.grantedAt,
            grantedBy: sp.grantedBy,
          })) || [],
        permissionCodes: salonAccess.activePermissions || [],
      }));

      res.status(200).json({
        success: true,
        data: formattedSalons,
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error getting my salons:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get available permissions
   */
  async getAvailablePermissions(_req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      console.log('🔍 StaffController: Getting available permissions');

      const permissions = await permissionService.getPermissionsForUI();

      console.log('✅ StaffController: Retrieved permissions:', permissions.length);

      res.status(200).json({
        success: true,
        data: permissions,
      });
    } catch (error: any) {
      console.error('❌ StaffController: Error getting permissions:', error);
      console.error('❌ StaffController: Error stack:', error.stack);

      // Fallback response with basic permissions
      const fallbackPermissions = [
        { id: '1', code: 'VIEW_SERVICES', name: 'View Services', description: 'View salon services and pricing', category: 'SERVICES', categoryLabel: 'Services' },
        { id: '2', code: 'MANAGE_SERVICES', name: 'Manage Services', description: 'Create, edit, and delete salon services', category: 'SERVICES', categoryLabel: 'Services' },
        { id: '3', code: 'VIEW_OFFERS', name: 'View Offers', description: 'View salon offers and promotions', category: 'OFFERS', categoryLabel: 'Offers' },
        { id: '4', code: 'MANAGE_OFFERS', name: 'Manage Offers', description: 'Create, edit, and delete salon offers', category: 'OFFERS', categoryLabel: 'Offers' },
        { id: '5', code: 'VIEW_BOOKINGS', name: 'View Bookings', description: 'View customer bookings and appointments', category: 'BOOKINGS', categoryLabel: 'Bookings' },
        { id: '6', code: 'MANAGE_BOOKINGS', name: 'Manage Bookings', description: 'Create, edit, and cancel bookings', category: 'BOOKINGS', categoryLabel: 'Bookings' },
        { id: '7', code: 'VIEW_CUSTOMERS', name: 'View Customers', description: 'View customer information and history', category: 'CUSTOMERS', categoryLabel: 'Customers' },
        { id: '8', code: 'MANAGE_CUSTOMERS', name: 'Manage Customers', description: 'Edit customer information and preferences', category: 'CUSTOMERS', categoryLabel: 'Customers' },
        { id: '9', code: 'VIEW_REPORTS', name: 'View Reports', description: 'View salon analytics and reports', category: 'REPORTS', categoryLabel: 'Reports' },
        { id: '10', code: 'MANAGE_STAFF', name: 'Manage Staff', description: 'Manage other staff members (supervisor role)', category: 'STAFF', categoryLabel: 'Staff' },
      ];

      res.status(200).json({
        success: true,
        data: fallbackPermissions,
      });
    }
  }


}

export const staffController = new StaffController();
