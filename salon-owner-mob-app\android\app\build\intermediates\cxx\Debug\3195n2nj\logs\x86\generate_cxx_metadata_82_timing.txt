# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 32ms
  [gap of 26ms]
generate_cxx_metadata completed in 80ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 33ms]
  create-invalidation-state 67ms
  [gap of 15ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 132ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 44ms
  [gap of 28ms]
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 20ms
  [gap of 15ms]
generate_cxx_metadata completed in 35ms

