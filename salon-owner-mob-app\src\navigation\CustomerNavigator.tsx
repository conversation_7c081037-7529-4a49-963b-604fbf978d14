import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { CustomerHomeScreen } from '../screens/customer/CustomerHomeScreen';
import { SearchScreen } from '../screens/customer/SearchScreen';
import { CustomerBookingsScreen } from '../screens/customer/CustomerBookingsScreen';
import { FavoritesScreen } from '../screens/customer/FavoritesScreen';
import { ProfileScreen } from '../screens/profile/ProfileScreen';
import CustomerSalonSelectionScreen from '../screens/customer/CustomerSalonSelectionScreen';
import { ServicesScreen } from '../screens/customer/ServicesScreen';
import { ServiceDetailsScreen } from '../screens/customer/ServiceDetailsScreen';
import { CustomerTabParamList, CustomerStackParamList } from '../types';
import { Colors } from '../constants/colors';

const Stack = createStackNavigator<CustomerStackParamList>();
const Tab = createBottomTabNavigator<CustomerTabParamList>();

// Customer Tab Navigator (main customer screens)
const CustomerTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Search':
              iconName = 'search';
              break;
            case 'Bookings':
              iconName = 'event';
              break;
            case 'Favorites':
              iconName = 'favorite';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'home';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textSecondary,
        tabBarStyle: {
          backgroundColor: Colors.white,
          borderTopColor: Colors.gray200,
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={CustomerHomeScreen}
        options={{ title: 'Home' }}
      />
      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{ title: 'Search' }}
      />
      <Tab.Screen
        name="Bookings"
        component={CustomerBookingsScreen}
        options={{ title: 'Bookings' }}
      />
      <Tab.Screen
        name="Favorites"
        component={FavoritesScreen}
        options={{ title: 'Favorites' }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

// Main Customer Navigator (handles salon selection and main tabs)
export const CustomerNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      {/* Salon Selection Screen */}
      <Stack.Screen
        name="CustomerSalonSelection"
        component={CustomerSalonSelectionScreen}
        options={{
          title: 'Select Salon',
        }}
      />

      {/* Main Customer Tab Navigator */}
      <Stack.Screen
        name="CustomerHome"
        component={CustomerTabNavigator}
        options={{
          title: 'Customer Home',
        }}
      />

      {/* Services Screens */}
      <Stack.Screen
        name="Services"
        component={ServicesScreen}
        options={{
          title: 'Services',
        }}
      />

      <Stack.Screen
        name="ServiceDetails"
        component={ServiceDetailsScreen}
        options={{
          title: 'Service Details',
        }}
      />
    </Stack.Navigator>
  );
};
