{"logs": [{"outputFile": "com.saloon_app-mergeDebugResources-54:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cf9bc6b4f1abcf764423b202ca0f1044\\transformed\\material-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1048,1114,1203,1272,1331,1426,1492,1557,1615,1680,1741,1801,1907,1968,2028,2086,2157,2276,2362,2439,2529,2614,2696,2839,2914,2990,3121,3211,3289,3344,3399,3465,3534,3608,3679,3758,3831,3908,3977,4047,4144,4229,4304,4397,4490,4564,4633,4727,4779,4862,4929,5013,5097,5159,5223,5286,5356,5455,5553,5648,5742,5801,5860,5939,6024,6101", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "264,338,409,490,576,659,774,893,976,1043,1109,1198,1267,1326,1421,1487,1552,1610,1675,1736,1796,1902,1963,2023,2081,2152,2271,2357,2434,2524,2609,2691,2834,2909,2985,3116,3206,3284,3339,3394,3460,3529,3603,3674,3753,3826,3903,3972,4042,4139,4224,4299,4392,4485,4559,4628,4722,4774,4857,4924,5008,5092,5154,5218,5281,5351,5450,5548,5643,5737,5796,5855,5934,6019,6096,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2986,3060,3131,3212,3298,4087,4202,4321,6734,6801,6867,6956,7177,7303,7398,7464,7529,7587,7652,7713,7773,7879,7940,8000,8058,8129,8315,8401,8478,8568,8653,8735,8878,8953,9029,9160,9250,9328,9383,9438,9504,9573,9647,9718,9797,9870,9947,10016,10086,10183,10268,10343,10436,10529,10603,10672,10766,10818,10901,10968,11052,11136,11198,11262,11325,11395,11494,11592,11687,11781,11840,11899,12060,12145,12222", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "314,3055,3126,3207,3293,3376,4197,4316,4399,6796,6862,6951,7020,7231,7393,7459,7524,7582,7647,7708,7768,7874,7935,7995,8053,8124,8243,8396,8473,8563,8648,8730,8873,8948,9024,9155,9245,9323,9378,9433,9499,9568,9642,9713,9792,9865,9942,10011,10081,10178,10263,10338,10431,10524,10598,10667,10761,10813,10896,10963,11047,11131,11193,11257,11320,11390,11489,11587,11682,11776,11835,11894,11973,12140,12217,12293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "38,39,40,41,42,43,44,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3477,3580,3679,3777,3878,3976,12440", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3472,3575,3674,3772,3873,3971,4082,12536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4480,4588,4758,4888,4995,5147,5272,5380,5628,5780,5885,6051,6180,6351,6512,6580,6649", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "4583,4753,4883,4990,5142,5267,5375,5491,5775,5880,6046,6175,6346,6507,6575,6644,6729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,11978", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,12055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,131,201,283,350,417,488", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "126,196,278,345,412,483,554"}, "to": {"startLines": "48,71,72,74,88,139,140", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4404,7025,7095,7236,8248,12298,12369", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "4475,7090,7172,7298,8310,12364,12435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5496", "endColumns": "131", "endOffsets": "5623"}}]}]}