import { Repository } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { firebaseConfig } from '../config/firebase';
import { UserDeviceToken, DevicePlatform } from '../entities/UserDeviceToken';
import { Notification, NotificationType, NotificationStatus } from '../entities/Notification';
import { User, UserRole } from '../entities/User';
import { Salon } from '../entities/Salon';
import { Offer } from '../entities/Offer';
import logger from '../utils/logger';

export interface PushNotificationPayload {
  title: string;
  body: string;
  imageUrl?: string;
  data?: Record<string, string>;
}

export interface OfferNotificationData {
  offerId: string;
  salonId: string;
  offerTitle: string;
  discountValue: number;
  discountType: string;
  validUntil: string;
  code: string;
}

class PushNotificationService {
  private userDeviceTokenRepository: Repository<UserDeviceToken>;
  private notificationRepository: Repository<Notification>;
  private userRepository: Repository<User>;
  private salonRepository: Repository<Salon>;
  private offerRepository: Repository<Offer>;

  constructor() {
    this.userDeviceTokenRepository = AppDataSource.getRepository(UserDeviceToken);
    this.notificationRepository = AppDataSource.getRepository(Notification);
    this.userRepository = AppDataSource.getRepository(User);
    this.salonRepository = AppDataSource.getRepository(Salon);
    this.offerRepository = AppDataSource.getRepository(Offer);
  }

  // Register or update device token
  async registerDeviceToken(
    userId: string,
    token: string,
    platform: DevicePlatform,
    deviceInfo?: {
      deviceName?: string;
      appVersion?: string;
      osVersion?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<UserDeviceToken> {
    try {
      // Check if token already exists for this user
      let deviceToken = await this.userDeviceTokenRepository.findOne({
        where: { userId, token },
      });

      if (deviceToken) {
        // Update existing token
        deviceToken.platform = platform;
        deviceToken.deviceName = deviceInfo?.deviceName || deviceToken.deviceName;
        deviceToken.appVersion = deviceInfo?.appVersion || deviceToken.appVersion;
        deviceToken.osVersion = deviceInfo?.osVersion || deviceToken.osVersion;
        deviceToken.metadata = deviceInfo?.metadata || deviceToken.metadata;
        deviceToken.isActive = true;
        deviceToken.updateLastUsed();
      } else {
        // Create new token
        deviceToken = this.userDeviceTokenRepository.create({
          userId,
          token,
          platform,
          deviceName: deviceInfo?.deviceName,
          appVersion: deviceInfo?.appVersion,
          osVersion: deviceInfo?.osVersion,
          metadata: deviceInfo?.metadata,
          isActive: true,
          lastUsedAt: new Date(),
        });
      }

      await this.userDeviceTokenRepository.save(deviceToken);
      logger.info(`Device token registered for user ${userId}`);
      
      return deviceToken;
    } catch (error) {
      logger.error('Error registering device token:', error);
      throw error;
    }
  }

  // Remove device token
  async removeDeviceToken(userId: string, token: string): Promise<void> {
    try {
      await this.userDeviceTokenRepository.delete({ userId, token });
      logger.info(`Device token removed for user ${userId}`);
    } catch (error) {
      logger.error('Error removing device token:', error);
      throw error;
    }
  }

  // Get active device tokens for user
  async getUserDeviceTokens(userId: string): Promise<string[]> {
    try {
      const deviceTokens = await this.userDeviceTokenRepository.find({
        where: { userId, isActive: true },
      });

      return deviceTokens.map(dt => dt.token);
    } catch (error) {
      logger.error('Error getting user device tokens:', error);
      return [];
    }
  }

  // Send notification to specific users
  async sendNotificationToUsers(
    userIds: string[],
    payload: PushNotificationPayload,
    type: NotificationType,
    relatedData?: {
      salonId?: string;
      offerId?: string;
    }
  ): Promise<void> {
    try {
      const tokens: string[] = [];
      const notifications: Notification[] = [];

      // Get device tokens for all users
      for (const userId of userIds) {
        const userTokens = await this.getUserDeviceTokens(userId);
        logger.info(`User ${userId} has ${userTokens.length} device tokens`);
        tokens.push(...userTokens);

        // Create notification record
        const notification = this.notificationRepository.create({
          userId,
          salonId: relatedData?.salonId,
          offerId: relatedData?.offerId,
          type,
          title: payload.title,
          body: payload.body,
          imageUrl: payload.imageUrl,
          data: payload.data,
          status: NotificationStatus.PENDING,
        });

        notifications.push(notification);
      }

      logger.info(`Total device tokens collected: ${tokens.length}`);

      // Save notification records
      await this.notificationRepository.save(notifications);

      if (tokens.length > 0) {
        try {
          // Send push notifications
          const response = await firebaseConfig.sendNotification(
            tokens,
            {
              title: payload.title,
              body: payload.body,
              imageUrl: payload.imageUrl,
            },
            payload.data
          );

          // Update notification status
          for (const notification of notifications) {
            notification.markAsSent();
          }
          await this.notificationRepository.save(notifications);

          logger.info(`Push notifications sent to ${userIds.length} users with ${tokens.length} devices successfully`);
        } catch (firebaseError: any) {
          logger.error('Firebase error sending notifications:', firebaseError.message);

          // Mark notifications as failed
          for (const notification of notifications) {
            notification.markAsFailed(firebaseError.message);
          }
          await this.notificationRepository.save(notifications);

          // Don't throw error to prevent API crash, just log it
          logger.warn('Continuing despite Firebase error to prevent API crash');
        }
      } else {
        logger.warn(`No device tokens found for users: ${userIds.join(', ')}`);

        // Mark notifications as failed due to no tokens
        for (const notification of notifications) {
          notification.markAsFailed('No device tokens found');
        }
        await this.notificationRepository.save(notifications);
      }
    } catch (error) {
      logger.error('Error sending notifications to users:', error);
      throw error;
    }
  }

  // Send offer notification to applicable customers
  async sendOfferNotification(offerId: string): Promise<void> {
    try {
      logger.info(`🎯 Sending offer notification for offer: ${offerId}`);

      // Get offer details
      const offer = await this.offerRepository.findOne({
        where: { id: offerId },
        relations: ['salon'],
      });

      if (!offer) {
        throw new Error(`Offer not found: ${offerId}`);
      }

      if (!offer.isActive) {
        logger.info(`Offer ${offerId} is not active, skipping notification`);
        return;
      }

      // Get salon details
      const salon = offer.salon;
      if (!salon) {
        throw new Error(`Salon not found for offer: ${offerId}`);
      }

      // Find applicable customers based on offer criteria
      const applicableUsers = await this.findApplicableCustomers(offer, salon);

      if (applicableUsers.length === 0) {
        logger.info(`No applicable customers found for offer: ${offerId}`);
        return;
      }

      logger.info(`Found ${applicableUsers.length} applicable customers for offer ${offerId}`);

      // Prepare notification payload
      const discountDisplay = this.getDiscountDisplay(offer);
      const payload: PushNotificationPayload = {
        title: `🎉 New Offer at ${salon.name}!`,
        body: `${offer.title} - ${discountDisplay}. Use code: ${offer.code}`,
        imageUrl: offer.image || salon.logo,
        data: {
          type: 'offer_published',
          offerId: offer.id,
          salonId: salon.id,
          offerTitle: offer.title,
          discountValue: offer.discountValue.toString(),
          discountType: offer.discountType,
          validUntil: offer.validUntil.toISOString(),
          code: offer.code,
          channelId: 'salon_offers', // Specify channel for Android
        },
      };

      // Send notifications
      const userIds = applicableUsers.map(user => user.id);
      await this.sendNotificationToUsers(
        userIds,
        payload,
        NotificationType.OFFER_PUBLISHED,
        {
          salonId: salon.id,
          offerId: offer.id,
        }
      );

      logger.info(`✅ Offer notification sent to ${applicableUsers.length} customers`);
    } catch (error) {
      logger.error('Error sending offer notification:', error);
      throw error;
    }
  }

  // Find customers applicable for the offer
  private async findApplicableCustomers(offer: Offer, salon: Salon): Promise<User[]> {
    try {
      const queryBuilder = this.userRepository.createQueryBuilder('user')
        .where('user.role = :role', { role: UserRole.CUSTOMER })
        .andWhere('user.isActive = :isActive', { isActive: true })
        .andWhere('user.accountId = :accountId', { accountId: salon.accountId });

      // Apply customer type filters
      switch (offer.customerType) {
        case 'NEW':
          // Add logic for new customers (e.g., created within last 30 days)
          queryBuilder.andWhere('user.createdAt >= :thirtyDaysAgo', {
            thirtyDaysAgo: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          });
          break;
        
        case 'EXISTING':
          // Add logic for existing customers
          queryBuilder.andWhere('user.createdAt < :thirtyDaysAgo', {
            thirtyDaysAgo: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          });
          break;
        
        case 'VIP':
          // Add logic for VIP customers (could be based on metadata or separate field)
          // For now, we'll skip this filter
          break;
        
        case 'LOYALTY_TIER':
          // Add logic for specific loyalty tier
          if (offer.loyaltyTierRequired) {
            // This would require additional customer loyalty data
            // For now, we'll skip this filter
          }
          break;
        
        case 'ALL':
        default:
          // No additional filters
          break;
      }

      // Apply minimum visits filter if specified
      if (offer.minVisitsRequired && offer.minVisitsRequired > 0) {
        // This would require booking/visit history
        // For now, we'll skip this filter
      }

      const users = await queryBuilder.getMany();
      
      logger.info(`Found ${users.length} applicable customers for offer ${offer.id}`);
      return users;
    } catch (error) {
      logger.error('Error finding applicable customers:', error);
      return [];
    }
  }

  // Helper method to format discount display
  private getDiscountDisplay(offer: Offer): string {
    switch (offer.discountType) {
      case 'PERCENTAGE':
        return `${offer.discountValue}% OFF`;
      case 'FIXED_AMOUNT':
        return `$${offer.discountValue} OFF`;
      case 'BUY_ONE_GET_ONE':
        return 'BUY 1 GET 1';
      case 'FREE_SERVICE':
        return 'FREE SERVICE';
      default:
        return `${offer.discountValue} OFF`;
    }
  }

  // Clean up expired tokens
  async cleanupExpiredTokens(daysThreshold: number = 30): Promise<void> {
    try {
      const expiredTokens = await this.userDeviceTokenRepository
        .createQueryBuilder('token')
        .where('token.lastUsedAt < :threshold', {
          threshold: new Date(Date.now() - daysThreshold * 24 * 60 * 60 * 1000),
        })
        .getMany();

      if (expiredTokens.length > 0) {
        await this.userDeviceTokenRepository.remove(expiredTokens);
        logger.info(`Cleaned up ${expiredTokens.length} expired device tokens`);
      }
    } catch (error) {
      logger.error('Error cleaning up expired tokens:', error);
    }
  }
}

export const pushNotificationService = new PushNotificationService();
