import { Repository } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { Permission, PermissionCategory, DEFAULT_PERMISSIONS } from '../entities/Permission';
import { StaffPermission } from '../entities/StaffPermission';
import ApiError from '../utils/apiError';

export interface CreatePermissionRequest {
  code: string;
  name: string;
  description?: string;
  category: PermissionCategory;
  resource?: string;
  action?: string;
  sortOrder?: number;
}

export interface UpdatePermissionRequest {
  name?: string;
  description?: string;
  category?: PermissionCategory;
  resource?: string;
  action?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface PermissionFilters {
  category?: PermissionCategory;
  isActive?: boolean;
  search?: string;
  sortBy?: 'code' | 'name' | 'category' | 'sortOrder' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

class PermissionService {
  private permissionRepository: Repository<Permission>;
  private staffPermissionRepository: Repository<StaffPermission>;

  constructor() {
    this.permissionRepository = AppDataSource.getRepository(Permission);
    this.staffPermissionRepository = AppDataSource.getRepository(StaffPermission);
  }

  /**
   * Initialize default permissions
   */
  async initializeDefaultPermissions(): Promise<void> {
    try {
      console.log('🔍 PermissionService: Initializing default permissions');

      for (const permissionData of DEFAULT_PERMISSIONS) {
        const existingPermission = await this.permissionRepository.findOne({
          where: { code: permissionData.code }
        });

        if (!existingPermission) {
          const permission = this.permissionRepository.create(permissionData);
          await this.permissionRepository.save(permission);
          console.log('✅ PermissionService: Created permission:', permissionData.code);
        }
      }

      console.log('✅ PermissionService: Default permissions initialized');
    } catch (error) {
      console.error('❌ PermissionService: Error initializing permissions:', error);
      throw error;
    }
  }

  /**
   * Get all permissions
   */
  async getAllPermissions(filters: PermissionFilters = {}): Promise<{
    permissions: Permission[];
    total: number;
    page?: number;
    limit?: number;
  }> {
    try {
      const queryBuilder = this.permissionRepository.createQueryBuilder('permission');

      // Apply filters
      if (filters.category) {
        queryBuilder.andWhere('permission.category = :category', { category: filters.category });
      }

      if (filters.isActive !== undefined) {
        queryBuilder.andWhere('permission.isActive = :isActive', { isActive: filters.isActive });
      }

      if (filters.search) {
        queryBuilder.andWhere(
          '(permission.code ILIKE :search OR permission.name ILIKE :search OR permission.description ILIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'sortOrder';
      const sortOrder = filters.sortOrder || 'ASC';
      queryBuilder.orderBy(`permission.${sortBy}`, sortOrder);

      // Apply pagination
      if (filters.limit) {
        queryBuilder.limit(filters.limit);
        if (filters.offset) {
          queryBuilder.offset(filters.offset);
        }
      }

      const [permissions, total] = await queryBuilder.getManyAndCount();

      return {
        permissions,
        total,
        page: filters.offset && filters.limit ? Math.floor(filters.offset / filters.limit) + 1 : undefined,
        limit: filters.limit,
      };
    } catch (error) {
      console.error('❌ PermissionService: Error getting permissions:', error);
      throw new ApiError(500, 'Failed to retrieve permissions');
    }
  }

  /**
   * Get permissions by category
   */
  async getPermissionsByCategory(): Promise<Record<PermissionCategory, Permission[]>> {
    try {
      const permissions = await this.permissionRepository.find({
        where: { isActive: true },
        order: { sortOrder: 'ASC', name: 'ASC' }
      });

      const categorizedPermissions: Record<PermissionCategory, Permission[]> = {
        [PermissionCategory.SERVICES]: [],
        [PermissionCategory.OFFERS]: [],
        [PermissionCategory.BOOKINGS]: [],
        [PermissionCategory.CUSTOMERS]: [],
        [PermissionCategory.REPORTS]: [],
        [PermissionCategory.STAFF]: [],
        [PermissionCategory.SALON]: [],
      };

      permissions.forEach(permission => {
        categorizedPermissions[permission.category].push(permission);
      });

      return categorizedPermissions;
    } catch (error) {
      console.error('❌ PermissionService: Error getting permissions by category:', error);
      throw new ApiError(500, 'Failed to retrieve permissions by category');
    }
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(id: string): Promise<Permission> {
    try {
      const permission = await this.permissionRepository.findOne({
        where: { id }
      });

      if (!permission) {
        throw new ApiError(404, 'Permission not found');
      }

      return permission;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      console.error('❌ PermissionService: Error getting permission by ID:', error);
      throw new ApiError(500, 'Failed to retrieve permission');
    }
  }

  /**
   * Get permission by code
   */
  async getPermissionByCode(code: string): Promise<Permission> {
    try {
      const permission = await this.permissionRepository.findOne({
        where: { code }
      });

      if (!permission) {
        throw new ApiError(404, 'Permission not found');
      }

      return permission;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      console.error('❌ PermissionService: Error getting permission by code:', error);
      throw new ApiError(500, 'Failed to retrieve permission');
    }
  }

  /**
   * Create permission
   */
  async createPermission(data: CreatePermissionRequest): Promise<Permission> {
    try {
      // Check if permission code already exists
      const existingPermission = await this.permissionRepository.findOne({
        where: { code: data.code }
      });

      if (existingPermission) {
        throw new ApiError(400, 'Permission with this code already exists');
      }

      const permission = this.permissionRepository.create({
        ...data,
        isActive: true,
        sortOrder: data.sortOrder || 0,
      });

      return await this.permissionRepository.save(permission);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      console.error('❌ PermissionService: Error creating permission:', error);
      throw new ApiError(500, 'Failed to create permission');
    }
  }

  /**
   * Update permission
   */
  async updatePermission(id: string, data: UpdatePermissionRequest): Promise<Permission> {
    try {
      const permission = await this.getPermissionById(id);

      Object.assign(permission, data);

      return await this.permissionRepository.save(permission);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      console.error('❌ PermissionService: Error updating permission:', error);
      throw new ApiError(500, 'Failed to update permission');
    }
  }

  /**
   * Delete permission
   */
  async deletePermission(id: string): Promise<void> {
    try {
      const permission = await this.getPermissionById(id);

      // Check if permission is being used
      const usageCount = await this.staffPermissionRepository.count({
        where: { permissionId: id }
      });

      if (usageCount > 0) {
        throw new ApiError(400, 'Cannot delete permission that is currently assigned to staff members');
      }

      await this.permissionRepository.remove(permission);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      console.error('❌ PermissionService: Error deleting permission:', error);
      throw new ApiError(500, 'Failed to delete permission');
    }
  }

  /**
   * Get permissions for UI (formatted for frontend)
   */
  async getPermissionsForUI(): Promise<Array<{
    id: string;
    code: string;
    name: string;
    description: string;
    category: PermissionCategory;
    categoryLabel: string;
  }>> {
    try {
      const permissions = await this.permissionRepository.find({
        where: { isActive: true },
        order: { category: 'ASC', sortOrder: 'ASC', name: 'ASC' }
      });

      return permissions.map(permission => ({
        id: permission.id,
        code: permission.code,
        name: permission.displayName,
        description: permission.fullDescription,
        category: permission.category,
        categoryLabel: permission.category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
      }));
    } catch (error) {
      console.error('❌ PermissionService: Error getting permissions for UI:', error);
      throw new ApiError(500, 'Failed to retrieve permissions for UI');
    }
  }
}

export const permissionService = new PermissionService();
