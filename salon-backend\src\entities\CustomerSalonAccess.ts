import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './User';
import { Salon } from './Salon';

export enum CustomerAccessStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

@Entity('customer_salon_access')
@Index(['customerId', 'salonId'], { unique: true })
export class CustomerSalonAccess {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'uuid',
    comment: 'Customer user ID',
  })
  customerId!: string;

  @Column({
    type: 'uuid',
    comment: 'Salon ID',
  })
  salonId!: string;

  @Column({
    type: 'enum',
    enum: CustomerAccessStatus,
    default: CustomerAccessStatus.ACTIVE,
    comment: 'Customer access status to this salon',
  })
  status!: CustomerAccessStatus;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When customer was granted access',
  })
  grantedAt?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When access expires (null = no expiry)',
  })
  expiresAt?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional access settings and preferences',
  })
  settings?: Record<string, any>;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Notes about customer access',
  })
  notes?: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'customerId' })
  customer!: User;

  @ManyToOne(() => Salon, (salon) => salon.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'salonId' })
  salon!: Salon;

  // Helper methods
  isActive(): boolean {
    if (this.status !== CustomerAccessStatus.ACTIVE) {
      return false;
    }

    if (this.expiresAt && this.expiresAt < new Date()) {
      return false;
    }

    return true;
  }

  activate(): void {
    this.status = CustomerAccessStatus.ACTIVE;
    this.grantedAt = new Date();
  }

  deactivate(): void {
    this.status = CustomerAccessStatus.INACTIVE;
  }

  suspend(): void {
    this.status = CustomerAccessStatus.SUSPENDED;
  }

  setExpiry(expiryDate: Date): void {
    this.expiresAt = expiryDate;
  }

  removeExpiry(): void {
    this.expiresAt = undefined;
  }
}
