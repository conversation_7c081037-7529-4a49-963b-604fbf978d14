{"logs": [{"outputFile": "com.saloon_app-mergeDebugResources-54:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3491,3589,3691,3789,3887,3994,4103,13511", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3584,3686,3784,3882,3989,4098,4218,13607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff5b0ce559ae890269420b3dec0e4060\\transformed\\react-android-0.79.2-debug\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,352,436,505,575,652,730,813,892,964,1043,1122,1196,1280,1364,1443,1513,1583,1665,1740,1816,1888", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "123,206,278,347,431,500,570,647,725,808,887,959,1038,1117,1191,1275,1359,1438,1508,1578,1660,1735,1811,1883,1957"}, "to": {"startLines": "33,49,71,73,74,76,90,91,92,139,140,141,142,147,148,149,150,151,152,153,154,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2996,4518,7009,7147,7216,7363,8363,8433,8510,12263,12346,12425,12497,12889,12968,13042,13126,13210,13289,13359,13429,13612,13687,13763,13835", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "3064,4596,7076,7211,7295,7427,8428,8505,8583,12341,12420,12492,12571,12963,13037,13121,13205,13284,13354,13424,13506,13682,13758,13830,13904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7312d9ce0e64b42e563cee99ce0c7cbc\\transformed\\play-services-base-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4601,4708,4864,4990,5100,5250,5372,5493,5738,5904,6012,6169,6296,6435,6589,6655,6718", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "4703,4859,4985,5095,5245,5367,5488,5590,5899,6007,6164,6291,6430,6584,6650,6713,6792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cf9bc6b4f1abcf764423b202ca0f1044\\transformed\\material-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1051,1115,1203,1269,1332,1418,1480,1541,1599,1665,1728,1783,1901,1958,2020,2075,2144,2263,2351,2426,2519,2604,2687,2826,2909,2990,3118,3205,3282,3340,3391,3457,3526,3602,3673,3749,3823,3902,3975,4046,4149,4236,4307,4396,4486,4558,4633,4720,4771,4850,4917,4998,5082,5144,5208,5271,5341,5445,5548,5644,5744,5806,5861,5938,6021,6097", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "269,350,430,508,603,691,791,905,986,1046,1110,1198,1264,1327,1413,1475,1536,1594,1660,1723,1778,1896,1953,2015,2070,2139,2258,2346,2421,2514,2599,2682,2821,2904,2985,3113,3200,3277,3335,3386,3452,3521,3597,3668,3744,3818,3897,3970,4041,4144,4231,4302,4391,4481,4553,4628,4715,4766,4845,4912,4993,5077,5139,5203,5266,5336,5440,5543,5639,5739,5801,5856,5933,6016,6092,6165"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3069,3150,3230,3308,3403,4223,4323,4437,6797,6857,6921,7081,7300,7432,7518,7580,7641,7699,7765,7828,7883,8001,8058,8120,8175,8244,8588,8676,8751,8844,8929,9012,9151,9234,9315,9443,9530,9607,9665,9716,9782,9851,9927,9998,10074,10148,10227,10300,10371,10474,10561,10632,10721,10811,10883,10958,11045,11096,11175,11242,11323,11407,11469,11533,11596,11666,11770,11873,11969,12069,12131,12186,12657,12740,12816", "endLines": "5,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "319,3145,3225,3303,3398,3486,4318,4432,4513,6852,6916,7004,7142,7358,7513,7575,7636,7694,7760,7823,7878,7996,8053,8115,8170,8239,8358,8671,8746,8839,8924,9007,9146,9229,9310,9438,9525,9602,9660,9711,9777,9846,9922,9993,10069,10143,10222,10295,10366,10469,10556,10627,10716,10806,10878,10953,11040,11091,11170,11237,11318,11402,11464,11528,11591,11661,11765,11868,11964,12064,12126,12181,12258,12735,12811,12884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,12576", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,12652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a1c3fd62b5fb636be47a17086a4f32c\\transformed\\play-services-basement-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5595", "endColumns": "142", "endOffsets": "5733"}}]}]}