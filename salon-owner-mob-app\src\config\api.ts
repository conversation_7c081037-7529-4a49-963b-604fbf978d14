import { Platform } from 'react-native';
import { ENVIRONMENT_ACCOUNT_IDS } from '../constants/app';

// API Configuration
export const API_CONFIG = {
  // Development URLs
  DEV: {
    BASE_URL: Platform.OS === 'android'
      ? 'http://192.168.1.41:5000/api/v1'  // Android emulator
      : 'http://192.168.1.34:5000/api/v1', // iOS simulator
    TIMEOUT: 10000,
    ACCOUNT_ID: ENVIRONMENT_ACCOUNT_IDS.development, // Development account ID
  },

  // Production URLs

  PROD: {
    BASE_URL: 'https://your-production-api.com/api/v1',
    TIMEOUT: 15000,
    ACCOUNT_ID: ENVIRONMENT_ACCOUNT_IDS.production, // Production account ID
  },

  // Staging URLs
  STAGING: {
    BASE_URL: 'https://your-staging-api.com/api/v1',
    TIMEOUT: 12000,
    ACCOUNT_ID: ENVIRONMENT_ACCOUNT_IDS.staging, // Staging account ID
  }
};

// Environment detection
export const getEnvironment = () => {
  if (__DEV__) {
    return 'DEV';
  }
  // Add logic to detect staging vs production
  return 'PROD';
};

// Get current API config
export const getCurrentApiConfig = () => {
  const env = getEnvironment();
  return API_CONFIG[env as keyof typeof API_CONFIG];
};

// Get current account ID
export const getCurrentAccountId = () => {
  const config = getCurrentApiConfig();
  return config.ACCOUNT_ID;
};

// API Endpoints
export const ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    ME: '/auth/me',
    UPDATE_PASSWORD: '/auth/updatepassword',
    FORGOT_PASSWORD: '/auth/forgotpassword',
    RESET_PASSWORD: '/auth/resetpassword',
  },
  
  // Users
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    PROFILE_IMAGE: '/users/profile/image',
    BY_ID: (id: string) => `/users/${id}`,
    UPDATE_ROLE: (id: string) => `/users/${id}/role`,
  },
  
  // Roles
  ROLES: {
    BASE: '/roles',
    BY_ID: (id: string) => `/roles/${id}`,
  },

  // Offers
  OFFERS: {
    BASE: '/offers',
    BY_ID: (id: string) => `/offers/${id}`,
    BY_SALON: (salonId: string) => `/offers/salon/${salonId}`,
    BULK: '/offers/bulk',
    STATS: '/offers/stats',
    PUBLISH: (id: string) => `/offers/${id}/publish`,
  },

  // Customer Salon Access
  CUSTOMER_SALON_ACCESS: {
    BASE: '/customer-salon-access',
    MY_SALONS: '/customer-salon-access/my-salons',
    AVAILABLE_SALONS: '/customer-salon-access/available-salons',
    GRANT: '/customer-salon-access/grant',
    REVOKE: '/customer-salon-access/revoke',
    CHECK: (salonId: string) => `/customer-salon-access/check/${salonId}`,
    SALON_CUSTOMERS: (salonId: string) => `/customer-salon-access/salon/${salonId}/customers`,
  },
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Request Headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Multipart Headers for file uploads
export const MULTIPART_HEADERS = {
  'Content-Type': 'multipart/form-data',
  'Accept': 'application/json',
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: '@salon_app_auth_token',
  USER_DATA: '@salon_app_user_data',
  REMEMBER_ME: '@salon_app_remember_me',
  THEME: '@salon_app_theme',
  LANGUAGE: '@salon_app_language',
} as const;
