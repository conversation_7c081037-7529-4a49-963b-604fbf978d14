ninja: Entering directory `D:\sgic-product\salon-owner-mob-app\android\app\.cxx\Debug\3195n2nj\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: D:/sgic-product/salon-owner-mob-app/android/app/.cxx/Debug/3195n2nj/armeabi-v7a
[0/2] Re-checking globbed directories...
[1/4] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[2/4] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[3/4] Building CXX object CMakeFiles/appmodules.dir/D_/sgic-product/salon-owner-mob-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[4/4] Linking CXX shared library D:\sgic-product\salon-owner-mob-app\android\app\build\intermediates\cxx\Debug\3195n2nj\obj\armeabi-v7a\libappmodules.so
