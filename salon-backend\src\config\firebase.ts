import admin from 'firebase-admin';
import logger from '../utils/logger';

class FirebaseConfig {
  private app: admin.app.App | null = null;

  async initialize(): Promise<void> {
    try {
      // Check if Firebase is already initialized
      if (this.app) {
        logger.info('Firebase Admin SDK already initialized');
        return;
      }

      // Initialize Firebase Admin SDK
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        // Parse the service account key from environment variable
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);

        logger.info(`Initializing Firebase Admin SDK for project: ${serviceAccount.project_id}`);

        this.app = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: serviceAccount.project_id,
        });

        // Test the connection by getting project info
        try {
          const messaging = this.getMessaging();
          logger.info('Firebase Messaging service initialized successfully');

          // Test with a dummy notification to validate configuration
          await this.testFirebaseConnection();
        } catch (testError) {
          logger.warn('Firebase Messaging service test failed:', testError);
        }

      } else if (process.env.FIREBASE_PROJECT_ID) {
        // Use default credentials (for production with service account file)
        logger.info(`Initializing Firebase Admin SDK with default credentials for project: ${process.env.FIREBASE_PROJECT_ID}`);

        this.app = admin.initializeApp({
          projectId: process.env.FIREBASE_PROJECT_ID,
        });
      } else {
        throw new Error('Firebase configuration not found. Please set FIREBASE_SERVICE_ACCOUNT_KEY or FIREBASE_PROJECT_ID');
      }

      logger.info('Firebase Admin SDK initialized successfully');
    } catch (error) {
      if (error instanceof Error) {
        logger.error(`Error initializing Firebase Admin SDK: ${error.message}`);
        logger.error('Stack trace:', error.stack);
      } else {
        logger.error('Unknown error initializing Firebase Admin SDK');
      }
      throw error;
    }
  }

  getApp(): admin.app.App {
    if (!this.app) {
      throw new Error('Firebase Admin SDK not initialized. Call initialize() first.');
    }
    return this.app;
  }

  getMessaging(): admin.messaging.Messaging {
    return this.getApp().messaging();
  }

  async sendNotification(
    tokens: string | string[],
    notification: {
      title: string;
      body: string;
      imageUrl?: string;
    },
    data?: Record<string, string>
  ): Promise<admin.messaging.BatchResponse | admin.messaging.MessagingTopicResponse> {
    try {
      const messaging = this.getMessaging();

      // Validate that we have tokens
      if (Array.isArray(tokens) && tokens.length === 0) {
        logger.warn('No tokens provided for notification');
        return {
          successCount: 0,
          failureCount: 0,
          responses: [],
        } as admin.messaging.BatchResponse;
      }

      if (Array.isArray(tokens)) {
        // Filter out invalid tokens
        const validTokens = tokens.filter(token => token && token.trim().length > 0);

        if (validTokens.length === 0) {
          logger.warn('No valid tokens found for notification');
          return {
            successCount: 0,
            failureCount: 0,
            responses: [],
          } as admin.messaging.BatchResponse;
        }

        logger.info(`Sending notification to ${validTokens.length} devices`);

        // Send to multiple tokens
        const multicastMessage: admin.messaging.MulticastMessage = {
          notification: {
            title: notification.title,
            body: notification.body,
            imageUrl: notification.imageUrl,
          },
          data: data || {},
          android: {
            notification: {
              channelId: 'salon_offers',
              priority: 'high' as const,
              defaultSound: true,
              defaultVibrateTimings: true,
              icon: 'ic_notification',
              color: '#b363e0',
            },
          },
          apns: {
            payload: {
              aps: {
                sound: 'default',
                badge: 1,
              },
            },
          },
          tokens: validTokens,
        };

        const response = await messaging.sendMulticast(multicastMessage);

        logger.info(`Push notification sent to ${validTokens.length} devices. Success: ${response.successCount}, Failure: ${response.failureCount}`);

        // Log failed tokens for cleanup
        if (response.failureCount > 0) {
          response.responses.forEach((resp, idx) => {
            if (!resp.success) {
              logger.warn(`Failed to send to token ${validTokens[idx]}: ${resp.error?.message}`);
            }
          });
        }

        return response;
      } else {
        // Send to single token
        const singleMessage: admin.messaging.Message = {
          notification: {
            title: notification.title,
            body: notification.body,
            imageUrl: notification.imageUrl,
          },
          data: data || {},
          android: {
            notification: {
              channelId: 'salon_offers',
              priority: 'high' as const,
              defaultSound: true,
              defaultVibrateTimings: true,
              icon: 'ic_notification',
              color: '#b363e0',
            },
          },
          apns: {
            payload: {
              aps: {
                sound: 'default',
                badge: 1,
              },
            },
          },
          token: tokens,
        };
        
        const messageId = await messaging.send(singleMessage);
        logger.info(`Push notification sent successfully. Message ID: ${messageId}`);
        
        // Return a compatible response format
        return {
          successCount: 1,
          failureCount: 0,
          responses: [{ success: true, messageId }],
        } as admin.messaging.BatchResponse;
      }
    } catch (error) {
      if (error instanceof Error) {
        logger.error(`Error sending push notification: ${error.message}`);

        // Check for specific Firebase errors
        if (error.message.includes('404') || error.message.includes('/batch')) {
          logger.error('Firebase project configuration error. Please check:');
          logger.error('1. FIREBASE_PROJECT_ID is correct');
          logger.error('2. Firebase project exists and has FCM enabled');
          logger.error('3. Service account has proper permissions');
          logger.error('4. Firebase Admin SDK is properly configured');

          // Don't throw error for now, just log it to prevent app crash
          logger.warn('Continuing without push notifications due to Firebase configuration issue');
          return {
            successCount: 0,
            failureCount: Array.isArray(tokens) ? tokens.length : 1,
            responses: [],
          } as admin.messaging.BatchResponse;
        }

        // Check for authentication errors
        if (error.message.includes('authentication') || error.message.includes('credential')) {
          logger.error('Firebase authentication error. Please check service account credentials');
        }
      } else {
        logger.error('Unknown error sending push notification');
      }
      throw error;
    }
  }

  async sendToTopic(
    topic: string,
    notification: {
      title: string;
      body: string;
      imageUrl?: string;
    },
    data?: Record<string, string>
  ): Promise<string> {
    try {
      const messaging = this.getMessaging();

      const message: admin.messaging.TopicMessage = {
        topic: topic,
        notification: {
          title: notification.title,
          body: notification.body,
          ...(notification.imageUrl && { imageUrl: notification.imageUrl }),
        },
        data: data || {},
        android: {
          notification: {
            channelId: 'salon_offers',
            priority: 'high' as const,
            defaultSound: true,
            defaultVibrateTimings: true,
            icon: 'ic_notification',
            color: '#b363e0',
          },
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1,
            },
          },
        },
      };

      const messageId = await messaging.send(message);
      logger.info(`Push notification sent to topic ${topic}. Message ID: ${messageId}`);

      return messageId;
     } catch (error) {
      if (error instanceof Error) {
        logger.error(`Error sending push notification to topic: ${error.message}`);
      } else {
        logger.error('Unknown error sending push notification to topic');
      }
      throw error;
    }
  }

  async subscribeToTopic(tokens: string[], topic: string): Promise<admin.messaging.MessagingTopicManagementResponse> {
    try {
      const messaging = this.getMessaging();
      const response = await messaging.subscribeToTopic(tokens, topic);
      
      logger.info(`Subscribed ${response.successCount} devices to topic ${topic}`);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        logger.error(`Error subscribing to topic: ${error.message}`);
      } else {
        logger.error('Unknown error subscribing to topic');
      }
      throw error;
    }
  }

  async unsubscribeFromTopic(tokens: string[], topic: string): Promise<admin.messaging.MessagingTopicManagementResponse> {
    try {
      const messaging = this.getMessaging();
      const response = await messaging.unsubscribeFromTopic(tokens, topic);
      
      logger.info(`Unsubscribed ${response.successCount} devices from topic ${topic}`);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        logger.error(`Error unsubscribing from topic: ${error.message}`);
      } else {
        logger.error('Unknown error unsubscribing from topic');
      }
      throw error;
    }
  }

  // Test Firebase connection without actually sending notifications
  private async testFirebaseConnection(): Promise<void> {
    try {
      logger.info('Testing Firebase connection...');

      // Just test that we can access the messaging service
      const messaging = this.getMessaging();

      // We don't actually send a test notification to avoid spam
      // Just verify the service is accessible
      logger.info('Firebase connection test passed');
    } catch (error) {
      logger.error('Firebase connection test failed:', error);
      throw error;
    }
  }
}

export const firebaseConfig = new FirebaseConfig();
