import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Dimensions,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  FlatList,
  Text,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { getAccountId } from '../../constants/app';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import { customerSalonService } from '../../services/customerSalonService';
import { Input } from '../../components/common/Input';
import {
  <PERSON>T<PERSON><PERSON>,
  DarkBody,
  WhiteButton,
  ErrorText
} from '../../components/common/Typography';
import { Colors, Gradients } from '../../constants/colors';
import {
  validateEmail,
  validatePassword,
  validateFirstName,
  validateLastName,
  validatePhoneNumber
} from '../../utils/validation';
import { SignUpForm, AuthStackParamList, AvailableSalon } from '../../types';

const { width, height } = Dimensions.get('window');

type SignUpScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'SignUp'>;

export const SignUpScreen: React.FC = () => {
  const navigation = useNavigation<SignUpScreenNavigationProp>();
  const { signUp, error, clearError } = useAuth();
  const { showToast } = useToast();

  const [form, setForm] = useState<SignUpForm>({
    firstName: { value: '', error: null, touched: false },
    lastName: { value: '', error: null, touched: false },
    email: { value: '', error: null, touched: false },
    password: { value: '', error: null, touched: false },
    phoneNumber: { value: '', error: null, touched: false },
  });

  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Salon selection state
  const [availableSalons, setAvailableSalons] = useState<AvailableSalon[]>([]);
  const [selectedSalonIds, setSelectedSalonIds] = useState<string[]>([]);
  const [isLoadingSalons, setIsLoadingSalons] = useState(false);
  const [showSalonSelection, setShowSalonSelection] = useState(false);

  // Load available salons when component mounts
  useEffect(() => {
    loadAvailableSalons();
  }, []);

  const loadAvailableSalons = async () => {
    try {
      setIsLoadingSalons(true);
      const accountId = getAccountId();
      console.log('🔍 Loading available salons for account:', accountId);

      const salons = await customerSalonService.getAvailableSalons(accountId);
      setAvailableSalons(salons);

      // Show salon selection if salons are available
      if (salons.length > 0) {
        setShowSalonSelection(true);
      }

      console.log('✅ Found', salons.length, 'available salons');
    } catch (error) {
      console.error('❌ Error loading available salons:', error);
      setAvailableSalons([]);
      showToast('Failed to load available salons', 'error', 3000);
    } finally {
      setIsLoadingSalons(false);
    }
  };

  const toggleSalonSelection = (salonId: string) => {
    setSelectedSalonIds(prev => {
      if (prev.includes(salonId)) {
        return prev.filter(id => id !== salonId);
      } else {
        return [...prev, salonId];
      }
    });
  };

  const renderSalonItem = ({ item }: { item: AvailableSalon }) => {
    const isSelected = selectedSalonIds.includes(item.id);

    return (
      <TouchableOpacity
        style={[styles.salonItem, isSelected && styles.salonItemSelected]}
        onPress={() => toggleSalonSelection(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.salonInfo}>
          <Text style={styles.salonName}>{item.name}</Text>
          {item.description && (
            <Text style={styles.salonDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
          {item.address && (
            <Text style={styles.salonAddress}>📍 {item.address}</Text>
          )}
        </View>
        <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
          {isSelected && <Text style={styles.checkmark}>✓</Text>}
        </View>
      </TouchableOpacity>
    );
  };

  const updateField = (field: keyof SignUpForm, value: string) => {
    clearError();
    setForm(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        value,
        touched: true,
        error: validateField(field, value),
      },
    }));
  };

  const validateField = (field: keyof SignUpForm, value: string): string | null => {
    switch (field) {
      case 'firstName':
        return validateFirstName(value);
      case 'lastName':
        return validateLastName(value);
      case 'email':
        return validateEmail(value);
      case 'password':
        return validatePassword(value);
      case 'phoneNumber':
        return validatePhoneNumber(value);
      default:
        return null;
    }
  };

  const handleSignUp = async () => {
    // Validate all fields
    const newForm = { ...form };
    let hasErrors = false;

    Object.keys(newForm).forEach(key => {
      const field = key as keyof SignUpForm;
      const error = validateField(field, newForm[field].value);
      newForm[field] = {
        ...newForm[field],
        touched: true,
        error,
      };
      if (error) hasErrors = true;
    });

    setForm(newForm);

    // Validate salon selection if salons are available
    if (showSalonSelection && availableSalons.length > 0 && selectedSalonIds.length === 0) {
      showToast('Please select at least one salon to continue', 'error', 3000);
      return;
    }

    if (!hasErrors) {
      try {
        // Get the account ID that will be used for registration
        const accountId = getAccountId();
        console.log('🏢 Registering user with Account ID:', accountId);
        console.log('🏢 Selected salon IDs:', selectedSalonIds);

        const result = await signUp({
          firstName: form.firstName.value,
          lastName: form.lastName.value,
          email: form.email.value,
          password: form.password.value,
          phoneNumber: form.phoneNumber.value || undefined,
          accountId,
          salonIds: selectedSalonIds.length > 0 ? selectedSalonIds : undefined,
        }, selectedImage || undefined);

        // Show success toast message and navigate to login
        if (result.success) {
          // Show success toast
          showToast(
            `🎉 Welcome ${form.firstName.value}! Registration successful. You can now login with your credentials.`,
            'success',
            4000
          );

          // Clear form and navigate to login after a short delay
          setTimeout(() => {
            setForm({
              firstName: { value: '', error: null, touched: false },
              lastName: { value: '', error: null, touched: false },
              email: { value: '', error: null, touched: false },
              password: { value: '', error: null, touched: false },
              phoneNumber: { value: '', error: null, touched: false },
            });
            setSelectedImage(null);
            setSelectedSalonIds([]);
            navigation.navigate('Login');
          }, 2000); // Navigate after 2 seconds to let user see the toast
        }
      } catch (error: any) {
        // Error is already handled by AuthContext, but we can add additional handling here if needed
        console.error('Signup error:', error);
      }
    }
  };

  const handleImagePress = () => {
    const options = [
      {
        text: 'Camera',
        onPress: () => openCamera(),
      },
      {
        text: 'Gallery',
        onPress: () => openGallery(),
      },
    ];

    // Add remove option if image is already selected
    if (selectedImage) {
      options.push({
        text: 'Remove Image',
        onPress: () => {
          setSelectedImage(null);
          showToast('Profile image removed', 'info', 2000);
        },
      });
    }

    options.push({
      text: 'Cancel',
      onPress: () => {},
    });

    Alert.alert(
      'Profile Image',
      selectedImage ? 'Change or remove your profile image' : 'Select your profile image',
      options
    );
  };

  const openCamera = () => {
    launchImageLibrary(
      {
        mediaType: 'photo' as MediaType,
        quality: 0.8,
        maxWidth: 1024,
        maxHeight: 1024,
        includeBase64: false,
      },
      handleImageResponse
    );
  };

  const openGallery = () => {
    launchImageLibrary(
      {
        mediaType: 'photo' as MediaType,
        quality: 0.8,
        maxWidth: 1024,
        maxHeight: 1024,
        includeBase64: false,
      },
      handleImageResponse
    );
  };

  const handleImageResponse = (response: ImagePickerResponse) => {
    if (response.didCancel || response.errorMessage) {
      return;
    }

    if (response.assets && response.assets[0]) {
      const asset = response.assets[0];
      if (asset.uri) {
        setSelectedImage(asset.uri);
        showToast('✅ Profile image selected successfully!', 'success', 2000);
      }
    }
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleLoginPress = () => {
    navigation.navigate('Login');
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Header with Back Button */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
              <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Profile Image Section */}
          <View style={styles.profileSection}>
            <View style={styles.profileImageContainer}>
              {selectedImage ? (
                <Image source={{ uri: selectedImage }} style={styles.profileImage} />
              ) : (
                <View style={styles.profileImage}>
                  <Icon name="person" size={width * 0.12} color={Colors.textSecondary} />
                </View>
              )}
              <TouchableOpacity style={styles.cameraButton} onPress={handleImagePress}>
                <Icon name="camera-alt" size={16} color={Colors.white} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Title Section */}
          <View style={styles.titleSection}>
            <DarkTitle level={2} textAlign="center">Personal Info</DarkTitle>
            <DarkBody size="medium" textAlign="center" style={styles.subtitle}>
              Enter your personal info to receive{'\n'}special offers & promos first.
            </DarkBody>
          </View>

          {/* Form Section */}
          <View style={styles.formSection}>
            <Input
              label="First Name *"
              placeholder="Enter your first name"
              value={form.firstName.value}
              onChangeText={(value) => updateField('firstName', value)}
              error={form.firstName.touched ? form.firstName.error || undefined : undefined}
              autoCapitalize="words"
              autoComplete="given-name"
              containerStyle={styles.inputContainer}
              leftIcon={<Icon name="person" size={20} color={Colors.textSecondary} />}
            />

            <Input
              label="Last Name *"
              placeholder="Enter your last name"
              value={form.lastName.value}
              onChangeText={(value) => updateField('lastName', value)}
              error={form.lastName.touched ? form.lastName.error || undefined : undefined}
              autoCapitalize="words"
              autoComplete="family-name"
              containerStyle={styles.inputContainer}
              leftIcon={<Icon name="person" size={20} color={Colors.textSecondary} />}
            />

            <Input
              label="Email *"
              placeholder="Enter your email"
              value={form.email.value}
              onChangeText={(value) => updateField('email', value)}
              error={form.email.touched ? form.email.error || undefined : undefined}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              containerStyle={styles.inputContainer}
              leftIcon={<Icon name="email" size={20} color={Colors.textSecondary} />}
            />

            <Input
              label="Phone Number"
              placeholder="Enter your phone number (optional)"
              value={form.phoneNumber.value}
              onChangeText={(value) => updateField('phoneNumber', value)}
              error={form.phoneNumber.touched ? form.phoneNumber.error || undefined : undefined}
              keyboardType="phone-pad"
              autoComplete="tel"
              containerStyle={styles.inputContainer}
              leftIcon={<Icon name="phone" size={20} color={Colors.textSecondary} />}
            />

            <Input
              label="Password *"
              placeholder="Enter your password"
              value={form.password.value}
              onChangeText={(value) => updateField('password', value)}
              error={form.password.touched ? form.password.error || undefined : undefined}
              secureTextEntry
              autoComplete="password-new"
              containerStyle={styles.inputContainer}
              leftIcon={<Icon name="lock" size={20} color={Colors.textSecondary} />}
            />

            {error && (
              <ErrorText textAlign="center" color={Colors.error} style={styles.errorText}>
                {error}
              </ErrorText>
            )}
          </View>

          {/* Salon Selection Section */}
          {showSalonSelection && (
            <View style={styles.salonSection}>
              <DarkTitle level={3} textAlign="left" style={styles.salonSectionTitle}>
                Select Salons
              </DarkTitle>
              <DarkBody size="small" textAlign="left" style={styles.salonSectionSubtitle}>
                Choose one or more salons you'd like to access
              </DarkBody>

              {isLoadingSalons ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color={Colors.primary} />
                  <DarkBody size="small" style={styles.loadingText}>Loading salons...</DarkBody>
                </View>
              ) : availableSalons.length > 0 ? (
                <FlatList
                  data={availableSalons}
                  renderItem={renderSalonItem}
                  keyExtractor={(item) => item.id}
                  style={styles.salonList}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              ) : (
                <DarkBody size="small" style={styles.noSalonsText}>
                  No salons available for selection
                </DarkBody>
              )}
            </View>
          )}

          {/* Sign Up Button */}
          <View style={styles.buttonSection}>
            <TouchableOpacity
              style={styles.signUpButtonContainer}
              onPress={handleSignUp}

            >
              <LinearGradient
                colors={Gradients.primaryButton}
                style={styles.signUpButton}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <WhiteButton size="large">
                 Signup
                </WhiteButton>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Login Link */}
          <View style={styles.loginSection}>
            <DarkBody size="medium" textAlign="center">
              Already have an account?{' '}
              <TouchableOpacity onPress={handleLoginPress}>
                <DarkBody
                  size="medium"
                  style={styles.loginLink}
                >
                  Login
                </DarkBody>
              </TouchableOpacity>
            </DarkBody>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: width * 0.06, // 6% horizontal padding
    paddingBottom: height * 0.05, // 5% bottom padding
  },
  header: {
    paddingTop: height * 0.06, // 6% from top for status bar
    marginBottom: height * 0.02, // 2% margin
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: height * 0.04, // 4% margin
  },
  profileImageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: width * 0.25, // 25% of screen width
    height: width * 0.25,
    borderRadius: width * 0.125, // Half of width for perfect circle
    backgroundColor: Colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.gray300,
    overflow: 'hidden', // Ensure image stays within circle
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: Colors.white,
  },
  titleSection: {
    marginBottom: height * 0.04, // 4% margin
  },
  subtitle: {
    marginTop: height * 0.01, // 1% margin
    color: Colors.textSecondary,
  },
  formSection: {
    marginBottom: height * 0.03, // 3% margin
  },
  inputContainer: {
    marginBottom: height * 0.025, // 2.5% margin
  },
  errorText: {
    marginTop: height * 0.02, // 2% margin
  },
  buttonSection: {
    marginBottom: height * 0.03, // 3% margin
  },
  signUpButtonContainer: {
    // No additional styling needed
  },
  signUpButton: {
    height: Math.max(height * 0.065, 52), // 6.5% of screen height, min 52
    borderRadius: width * 0.04, // 4% border radius
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 10,
  },
  loginSection: {
    alignItems: 'center',
    marginTop: height * 0.02, // 2% margin
  },
  loginLink: {
    color: Colors.primary,
    fontWeight: '600' as const,
    textDecorationLine: 'underline',
  },
  // Salon Selection Styles
  salonSection: {
    marginBottom: height * 0.03, // 3% margin
  },
  salonSectionTitle: {
    marginBottom: height * 0.01, // 1% margin
    color: Colors.textPrimary,
  },
  salonSectionSubtitle: {
    marginBottom: height * 0.02, // 2% margin
    color: Colors.textSecondary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: height * 0.02, // 2% padding
  },
  loadingText: {
    marginLeft: 8,
    color: Colors.textSecondary,
  },
  salonList: {
    maxHeight: height * 0.25, // 25% of screen height
  },
  salonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.gray300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  salonItemSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primaryLight || '#f3e8ff',
  },
  salonInfo: {
    flex: 1,
    marginRight: 12,
  },
  salonName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  salonDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  salonAddress: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.gray300,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.white,
  },
  checkboxSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  checkmark: {
    color: Colors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  noSalonsText: {
    textAlign: 'center',
    color: Colors.textSecondary,
    paddingVertical: height * 0.02, // 2% padding
  },
});
