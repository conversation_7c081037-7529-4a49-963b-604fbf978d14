# C/C++ build system timings
generate_cxx_metadata
  [gap of 91ms]
  create-invalidation-state 203ms
  [gap of 98ms]
  write-metadata-json-to-file 57ms
generate_cxx_metadata completed in 457ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 88ms]
  create-invalidation-state 147ms
  [gap of 72ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 333ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 142ms]
  create-invalidation-state 217ms
  [gap of 63ms]
  write-metadata-json-to-file 52ms
generate_cxx_metadata completed in 474ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 48ms]
  create-invalidation-state 130ms
  [gap of 57ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 251ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 66ms]
  create-invalidation-state 189ms
  [gap of 87ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 361ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 53ms]
  create-invalidation-state 148ms
  [gap of 89ms]
  write-metadata-json-to-file 32ms
generate_cxx_metadata completed in 322ms

