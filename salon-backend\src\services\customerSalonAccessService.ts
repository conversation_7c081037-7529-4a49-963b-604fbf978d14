import { Repository } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { CustomerSalonAccess, CustomerAccessStatus } from '../entities/CustomerSalonAccess';
import { User, UserRole } from '../entities/User';
import { Salon } from '../entities/Salon';
import logger from '../utils/logger';

export interface GrantAccessRequest {
  customerId: string;
  salonId: string;
  expiresAt?: Date;
  notes?: string;
  settings?: Record<string, any>;
}

export interface CustomerSalonAccessResponse {
  id: string;
  customerId: string;
  salonId: string;
  status: CustomerAccessStatus;
  grantedAt?: Date;
  expiresAt?: Date;
  settings?: Record<string, any>;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  salon?: {
    id: string;
    name: string;
    description?: string;
    logo?: string;
    address?: string;
    city?: string;
    phone?: string;
    email?: string;
  };
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profileImage?: string;
  };
}

class CustomerSalonAccessService {
  private customerSalonAccessRepository: Repository<CustomerSalonAccess>;
  private userRepository: Repository<User>;
  private salonRepository: Repository<Salon>;

  constructor() {
    this.customerSalonAccessRepository = AppDataSource.getRepository(CustomerSalonAccess);
    this.userRepository = AppDataSource.getRepository(User);
    this.salonRepository = AppDataSource.getRepository(Salon);
  }

  // Grant customer access to a salon
  async grantAccess(data: GrantAccessRequest): Promise<CustomerSalonAccessResponse> {
    try {
      logger.info(`Granting salon access: customer ${data.customerId} to salon ${data.salonId}`);

      // Validate customer exists and is a CUSTOMER role
      const customer = await this.userRepository.findOne({
        where: { id: data.customerId, role: UserRole.CUSTOMER },
      });

      if (!customer) {
        throw new Error('Customer not found or invalid role');
      }

      // Validate salon exists
      const salon = await this.salonRepository.findOne({
        where: { id: data.salonId },
      });

      if (!salon) {
        throw new Error('Salon not found');
      }

      // Check if access already exists
      const existingAccess = await this.customerSalonAccessRepository.findOne({
        where: { customerId: data.customerId, salonId: data.salonId },
      });

      if (existingAccess) {
        throw new Error('Customer already has access to this salon');
      }

      // Create new access record
      const access = this.customerSalonAccessRepository.create({
        customerId: data.customerId,
        salonId: data.salonId,
        status: CustomerAccessStatus.ACTIVE,
        grantedAt: new Date(),
        expiresAt: data.expiresAt,
        notes: data.notes,
        settings: data.settings,
      });

      const savedAccess = await this.customerSalonAccessRepository.save(access);

      logger.info(`✅ Salon access granted: ${savedAccess.id}`);

      return this.formatAccessResponse(savedAccess);
    } catch (error) {
      logger.error('❌ Error granting salon access:', error);
      throw error;
    }
  }

  // Get customer's accessible salons
  async getCustomerSalons(customerId: string): Promise<CustomerSalonAccessResponse[]> {
    try {
      logger.info(`Getting accessible salons for customer: ${customerId}`);

      const accessRecords = await this.customerSalonAccessRepository.find({
        where: { 
          customerId,
          status: CustomerAccessStatus.ACTIVE,
        },
        relations: ['salon'],
        order: { createdAt: 'DESC' },
      });

      // Filter out expired access
      const activeAccess = accessRecords.filter(access => access.isActive());

      logger.info(`✅ Found ${activeAccess.length} accessible salons for customer ${customerId}`);

      return activeAccess.map(access => this.formatAccessResponse(access));
    } catch (error) {
      logger.error('❌ Error getting customer salons:', error);
      throw error;
    }
  }

  // Get salon's customers
  async getSalonCustomers(salonId: string): Promise<CustomerSalonAccessResponse[]> {
    try {
      logger.info(`Getting customers for salon: ${salonId}`);

      const accessRecords = await this.customerSalonAccessRepository.find({
        where: { 
          salonId,
          status: CustomerAccessStatus.ACTIVE,
        },
        relations: ['customer'],
        order: { createdAt: 'DESC' },
      });

      // Filter out expired access
      const activeAccess = accessRecords.filter(access => access.isActive());

      logger.info(`✅ Found ${activeAccess.length} customers for salon ${salonId}`);

      return activeAccess.map(access => this.formatAccessResponse(access));
    } catch (error) {
      logger.error('❌ Error getting salon customers:', error);
      throw error;
    }
  }

  // Revoke customer access to a salon
  async revokeAccess(customerId: string, salonId: string): Promise<void> {
    try {
      logger.info(`Revoking salon access: customer ${customerId} from salon ${salonId}`);

      const access = await this.customerSalonAccessRepository.findOne({
        where: { customerId, salonId },
      });

      if (!access) {
        throw new Error('Access record not found');
      }

      access.deactivate();
      await this.customerSalonAccessRepository.save(access);

      logger.info(`✅ Salon access revoked: ${access.id}`);
    } catch (error) {
      logger.error('❌ Error revoking salon access:', error);
      throw error;
    }
  }

  // Grant access to multiple salons during customer registration
  async grantMultipleSalonAccess(customerId: string, salonIds: string[]): Promise<CustomerSalonAccessResponse[]> {
    try {
      logger.info(`Granting access to ${salonIds.length} salons for customer: ${customerId}`);

      const results: CustomerSalonAccessResponse[] = [];

      for (const salonId of salonIds) {
        try {
          const access = await this.grantAccess({
            customerId,
            salonId,
            notes: 'Granted during customer registration',
          });
          results.push(access);
        } catch (error) {
          logger.warn(`Failed to grant access to salon ${salonId}:`, error);
          // Continue with other salons
        }
      }

      logger.info(`✅ Granted access to ${results.length}/${salonIds.length} salons`);

      return results;
    } catch (error) {
      logger.error('❌ Error granting multiple salon access:', error);
      throw error;
    }
  }

  // Check if customer has access to a specific salon
  async hasAccess(customerId: string, salonId: string): Promise<boolean> {
    try {
      const access = await this.customerSalonAccessRepository.findOne({
        where: { 
          customerId, 
          salonId,
          status: CustomerAccessStatus.ACTIVE,
        },
      });

      return access ? access.isActive() : false;
    } catch (error) {
      logger.error('❌ Error checking salon access:', error);
      return false;
    }
  }

  private formatAccessResponse(access: CustomerSalonAccess): CustomerSalonAccessResponse {
    const response: CustomerSalonAccessResponse = {
      id: access.id,
      customerId: access.customerId,
      salonId: access.salonId,
      status: access.status,
      grantedAt: access.grantedAt,
      expiresAt: access.expiresAt,
      settings: access.settings,
      notes: access.notes,
      createdAt: access.createdAt,
      updatedAt: access.updatedAt,
    };

    // Include salon details if loaded
    if (access.salon) {
      response.salon = {
        id: access.salon.id,
        name: access.salon.name,
        description: access.salon.description,
        logo: access.salon.logo,
        address: access.salon.address,
        city: access.salon.city,
        phone: access.salon.phone,
        email: access.salon.email,
      };
    }

    // Include customer details if loaded
    if (access.customer) {
      response.customer = {
        id: access.customer.id,
        firstName: access.customer.firstName,
        lastName: access.customer.lastName,
        email: access.customer.email,
        profileImage: access.customer.profileImage,
      };
    }

    return response;
  }
}

export const customerSalonAccessService = new CustomerSalonAccessService();
