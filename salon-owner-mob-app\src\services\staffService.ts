import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import {
  StaffMember,
  CreateStaffRequest,
  UpdateStaffRequest,
  StaffSearchFilters,
  StaffPermissionInfo,
  StaffSalonAccess,
} from '../types/staff';

export interface StaffListResponse {
  data: StaffMember[];
  total: number;
  page: number;
  totalPages: number;
}

class StaffService {
  /**
   * Create a new staff member
   */
  async createStaff(data: CreateStaffRequest): Promise<{ user: any; access: StaffSalonAccess }> {
    try {
      console.log('🔍 StaffService: Creating staff member:', data.email);

      const response = await apiService.post(ENDPOINTS.STAFF.BASE, data);
      
      console.log('✅ StaffService: Staff member created successfully');
      return response.data?.data;
    } catch (error: any) {
      console.error('❌ StaffService: Error creating staff member:', error);
      throw error;
    }
  }

  /**
   * Get staff members for a salon
   */
  async getStaffBySalon(salonId: string, filters: StaffSearchFilters = {}): Promise<StaffListResponse> {
    try {
      console.log('🔍 StaffService: Getting staff for salon:', salonId);

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const endpoint = `${ENDPOINTS.STAFF.BY_SALON(salonId)}?${queryParams}`;
      const response = await apiService.get(endpoint);
      
      console.log('✅ StaffService: Retrieved staff members:', response.data?.data?.total || 0);
      return response.data?.data || { data: [], total: 0, page: 1, totalPages: 0 };
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff members:', error);
      throw error;
    }
  }

  /**
   * Get staff member by ID
   */
  async getStaffById(staffId: string, salonId?: string): Promise<StaffMember> {
    try {
      console.log('🔍 StaffService: Getting staff member:', staffId);

      const queryParams = salonId ? `?salonId=${salonId}` : '';
      const endpoint = `${ENDPOINTS.STAFF.BY_ID(staffId)}${queryParams}`;
      const response = await apiService.get(endpoint);
      
      console.log('✅ StaffService: Retrieved staff member:', response.data?.data?.staff?.email);
      return response.data?.data;
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff member:', error);
      throw error;
    }
  }

  /**
   * Update staff member
   */
  async updateStaff(staffId: string, data: UpdateStaffRequest): Promise<StaffMember> {
    try {
      console.log('🔍 StaffService: Updating staff member:', staffId);

      const response = await apiService.put(ENDPOINTS.STAFF.BY_ID(staffId), data);
      
      console.log('✅ StaffService: Staff member updated successfully');
      return response.data?.data;
    } catch (error: any) {
      console.error('❌ StaffService: Error updating staff member:', error);
      throw error;
    }
  }

  /**
   * Delete staff member
   */
  async deleteStaff(staffId: string, salonId: string): Promise<void> {
    try {
      console.log('🔍 StaffService: Deleting staff member:', staffId);

      await apiService.delete(ENDPOINTS.STAFF.BY_ID(staffId), { salonId });
      
      console.log('✅ StaffService: Staff member deleted successfully');
    } catch (error: any) {
      console.error('❌ StaffService: Error deleting staff member:', error);
      throw error;
    }
  }

  /**
   * Get salons accessible by staff member
   */
  async getStaffSalons(staffId: string): Promise<StaffSalonAccess[]> {
    try {
      console.log('🔍 StaffService: Getting salons for staff:', staffId);

      const response = await apiService.get(ENDPOINTS.STAFF.STAFF_SALONS(staffId));
      
      console.log('✅ StaffService: Retrieved staff salons:', response.data?.data?.length || 0);
      return response.data?.data || [];
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff salons:', error);
      throw error;
    }
  }

  /**
   * Get my salons (for authenticated staff)
   */
  async getMySalons(): Promise<StaffSalonAccess[]> {
    try {
      console.log('🔍 StaffService: Getting my salons');

      const response = await apiService.get(ENDPOINTS.STAFF.MY_SALONS);
      
      console.log('✅ StaffService: Retrieved my salons:', response.data?.data?.length || 0);
      return response.data?.data || [];
    } catch (error: any) {
      console.error('❌ StaffService: Error getting my salons:', error);
      throw error;
    }
  }

  /**
   * Get available permissions
   */
  async getAvailablePermissions(): Promise<StaffPermissionInfo[]> {
    try {
      console.log('🔍 StaffService: Getting available permissions');

      const response = await apiService.get(ENDPOINTS.STAFF.PERMISSIONS);
      
      console.log('✅ StaffService: Retrieved permissions:', response.data?.data?.length || 0);
      return response.data?.data || [];
    } catch (error: any) {
      console.error('❌ StaffService: Error getting permissions:', error);
      throw error;
    }
  }

  /**
   * Search staff members
   */
  async searchStaff(salonId: string, query: string): Promise<StaffMember[]> {
    try {
      console.log('🔍 StaffService: Searching staff:', query);

      const filters: StaffSearchFilters = {
        search: query,
        limit: 20,
      };

      const result = await this.getStaffBySalon(salonId, filters);
      
      console.log('✅ StaffService: Search completed:', result.data.length);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffService: Error searching staff:', error);
      throw error;
    }
  }

  /**
   * Get staff by status
   */
  async getStaffByStatus(salonId: string, status: string): Promise<StaffMember[]> {
    try {
      console.log('🔍 StaffService: Getting staff by status:', status);

      const filters: StaffSearchFilters = {
        status: status as any,
        limit: 100,
      };

      const result = await this.getStaffBySalon(salonId, filters);
      
      console.log('✅ StaffService: Retrieved staff by status:', result.data.length);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff by status:', error);
      throw error;
    }
  }

  /**
   * Get staff statistics for a salon
   */
  async getStaffStats(salonId: string): Promise<{
    total: number;
    active: number;
    suspended: number;
    revoked: number;
  }> {
    try {
      console.log('🔍 StaffService: Getting staff statistics for salon:', salonId);

      const result = await this.getStaffBySalon(salonId, { limit: 1000 });
      const staff = result.data;

      const stats = {
        total: staff.length,
        active: staff.filter(s => s.status === 'ACTIVE').length,
        suspended: staff.filter(s => s.status === 'SUSPENDED').length,
        revoked: staff.filter(s => s.status === 'REVOKED').length,
      };

      console.log('✅ StaffService: Staff statistics:', stats);
      return stats;
    } catch (error: any) {
      console.error('❌ StaffService: Error getting staff statistics:', error);
      throw error;
    }
  }

  /**
   * Bulk update staff status
   */
  async bulkUpdateStatus(staffIds: string[], status: string, salonId: string): Promise<void> {
    try {
      console.log('🔍 StaffService: Bulk updating staff status:', { staffIds, status });

      // For now, update each staff member individually
      // In the future, this could be optimized with a bulk API endpoint
      const promises = staffIds.map(staffId => 
        this.updateStaff(staffId, { status: status as any, salonId })
      );

      await Promise.all(promises);
      
      console.log('✅ StaffService: Bulk status update completed');
    } catch (error: any) {
      console.error('❌ StaffService: Error in bulk status update:', error);
      throw error;
    }
  }

  /**
   * Check if staff has specific permission
   */
  hasPermission(staff: StaffMember, permission: string): boolean {
    return staff.permissions.includes(permission as any);
  }

  /**
   * Get staff working status for today
   */
  isWorkingToday(staff: StaffMember): boolean {
    if (!staff.workingHours) return false;
    
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayHours = staff.workingHours[today];
    
    return todayHours?.isWorking || false;
  }

  /**
   * Format staff display name
   */
  getDisplayName(staff: StaffMember): string {
    return `${staff.staff.firstName} ${staff.staff.lastName}`;
  }

  /**
   * Format staff position
   */
  getDisplayPosition(staff: StaffMember): string {
    return staff.position || 'Staff Member';
  }

  /**
   * Get staff contact info
   */
  getContactInfo(staff: StaffMember): { email: string; phone?: string } {
    return {
      email: staff.staff.email,
      phone: staff.staff.phoneNumber,
    };
  }
}

export const staffService = new StaffService();
