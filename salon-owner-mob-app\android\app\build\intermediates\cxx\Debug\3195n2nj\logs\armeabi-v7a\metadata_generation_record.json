[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\sgic-product\\salon-owner-mob-app\\android\\app\\.cxx\\Debug\\3195n2nj\\armeabi-v7a\\android_gradle_build.json due to:", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a file changed", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - D:\\sgic-product\\salon-owner-mob-app\\android\\app\\.cxx\\Debug\\3195n2nj\\armeabi-v7a\\build.ninja (LAST_MODIFIED_CHANGED)", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - D:\\sgic-product\\salon-owner-mob-app\\android\\app\\.cxx\\Debug\\3195n2nj\\armeabi-v7a\\compile_commands.json (LAST_MODIFIED_CHANGED)", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Microsoft\\\\jdk-17.0.15.6-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  armeabi-v7a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging15661578948604764603\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\ff5b0ce559ae890269420b3dec0e4060\\\\transformed\\\\react-android-0.79.2-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\7eeb3e74c63d5948104a854a18ed214d\\\\transformed\\\\hermes-android-0.79.2-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\4b60134eb4741b931e894271dbfdd4cc\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "keeping json folder 'D:\\sgic-product\\salon-owner-mob-app\\android\\app\\.cxx\\Debug\\3195n2nj\\armeabi-v7a' but regenerating project", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\sgic-product\\\\salon-owner-mob-app\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3195n2nj\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3195n2nj\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3195n2nj\\\\prefab\\\\armeabi-v7a\\\\prefab\" ^\n  \"-BD:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3195n2nj\\\\armeabi-v7a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\sgic-product\\\\salon-owner-mob-app\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3195n2nj\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3195n2nj\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3195n2nj\\\\prefab\\\\armeabi-v7a\\\\prefab\" ^\n  \"-BD:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3195n2nj\\\\armeabi-v7a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\sgic-product\\\\salon-owner-mob-app\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\sgic-product\\salon-owner-mob-app\\android\\app\\.cxx\\Debug\\3195n2nj\\armeabi-v7a\\compile_commands.json.bin normally", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output D:\\sgic-product\\salon-owner-mob-app\\android\\app\\build\\intermediates\\cxx\\Debug\\3195n2nj\\obj\\armeabi-v7a\\libc++_shared.so in incremental regenerate", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output D:\\sgic-product\\salon-owner-mob-app\\android\\app\\build\\intermediates\\cxx\\Debug\\3195n2nj\\obj\\armeabi-v7a\\libfbjni.so in incremental regenerate", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output D:\\sgic-product\\salon-owner-mob-app\\android\\app\\build\\intermediates\\cxx\\Debug\\3195n2nj\\obj\\armeabi-v7a\\libjsi.so in incremental regenerate", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output D:\\sgic-product\\salon-owner-mob-app\\android\\app\\build\\intermediates\\cxx\\Debug\\3195n2nj\\obj\\armeabi-v7a\\libreactnative.so in incremental regenerate", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\sgic-product\\salon-owner-mob-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]