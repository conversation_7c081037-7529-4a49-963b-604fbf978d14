# C/C++ build system timings
generate_cxx_metadata
  [gap of 101ms]
  create-invalidation-state 183ms
  [gap of 64ms]
  write-metadata-json-to-file 48ms
generate_cxx_metadata completed in 396ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 95ms]
  create-invalidation-state 157ms
  [gap of 74ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 367ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 169ms]
  create-invalidation-state 166ms
  [gap of 35ms]
  write-metadata-json-to-file 32ms
generate_cxx_metadata completed in 402ms

