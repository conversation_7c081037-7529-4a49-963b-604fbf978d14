import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Image,
  FlatList,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import { useCustomerSalon } from '../../context/CustomerSalonContext';
import {
  DarkTitle,
  DarkBody,
  WhiteTitle,
} from '../../components/common/Typography';
import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { Colors, Gradients } from '../../constants/colors';
import { customerServiceService, ServiceCategory } from '../../services/customerServiceService';
import { customerOfferService, CustomerOffer } from '../../services/customerOfferService';
import { ServiceResponse, formatPrice, formatDuration } from '../../types/service';

const { width, height } = Dimensions.get('window');

export const CustomerHomeScreen: React.FC = () => {
  const { user } = useAuth();
  const { showToast } = useToast();
  const {
    selectedSalon: customerSalon,
    isLoading: salonLoading,
    accessibleSalons,
    needsSalonSelection
  } = useCustomerSalon();
  const navigation = useNavigation();

  // Debug logging
  useEffect(() => {
    console.log('🔍 CustomerHomeScreen: Debug info');
    console.log('🔍 CustomerHomeScreen: user:', user?.firstName);
    console.log('🔍 CustomerHomeScreen: salonLoading:', salonLoading);
    console.log('🔍 CustomerHomeScreen: accessibleSalons:', accessibleSalons.length);
    console.log('🔍 CustomerHomeScreen: customerSalon:', customerSalon?.salon?.name);
    console.log('🔍 CustomerHomeScreen: needsSalonSelection:', needsSalonSelection);
  }, [user, salonLoading, accessibleSalons, customerSalon, needsSalonSelection]);

  // State
  const [greeting, setGreeting] = useState('Good Morning!');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [serviceCategories, setServiceCategories] = useState<ServiceCategory[]>([]);
  const [activeOffers, setActiveOffers] = useState<CustomerOffer[]>([]);
  const [popularServices, setPopularServices] = useState<ServiceResponse[]>([]);
  const [recommendedServices, setRecommendedServices] = useState<ServiceResponse[]>([]);

  // Set greeting based on time
  useEffect(() => {
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good Morning!');
    } else if (hour < 17) {
      setGreeting('Good Afternoon!');
    } else {
      setGreeting('Good Evening!');
    }
  }, []);

  // Load data when salon is selected
  const loadData = useCallback(async (showLoading = true) => {
    if (!customerSalon?.salon?.id) return;

    try {
      if (showLoading) setLoading(true);

      console.log('🔍 CustomerHomeScreen: Loading data for salon:', customerSalon.salon.id);

      // Load all data in parallel
      const [categoriesResult, offersResult, popularResult, recommendedResult] = await Promise.allSettled([
        customerServiceService.getServiceCategories(customerSalon.salon.id),
        customerOfferService.getActiveOffers(customerSalon.salon.id, 5),
        customerServiceService.getPopularServices(customerSalon.salon.id, 6),
        customerServiceService.getRecommendedServices(customerSalon.salon.id, 4),
      ]);

      // Handle categories
      if (categoriesResult.status === 'fulfilled') {
        setServiceCategories(categoriesResult.value);
        console.log('✅ CustomerHomeScreen: Loaded categories:', categoriesResult.value.length);
      } else {
        console.error('❌ CustomerHomeScreen: Error loading categories:', categoriesResult.reason);
      }

      // Handle offers
      if (offersResult.status === 'fulfilled') {
        setActiveOffers(offersResult.value);
        console.log('✅ CustomerHomeScreen: Loaded offers:', offersResult.value.length);
      } else {
        console.error('❌ CustomerHomeScreen: Error loading offers:', offersResult.reason);
      }

      // Handle popular services
      if (popularResult.status === 'fulfilled') {
        setPopularServices(popularResult.value);
        console.log('✅ CustomerHomeScreen: Loaded popular services:', popularResult.value.length);
      } else {
        console.error('❌ CustomerHomeScreen: Error loading popular services:', popularResult.reason);
      }

      // Handle recommended services
      if (recommendedResult.status === 'fulfilled') {
        setRecommendedServices(recommendedResult.value);
        console.log('✅ CustomerHomeScreen: Loaded recommended services:', recommendedResult.value.length);
      } else {
        console.error('❌ CustomerHomeScreen: Error loading recommended services:', recommendedResult.reason);
      }

    } catch (error: any) {
      console.error('❌ CustomerHomeScreen: Error loading data:', error);
      showToast('Failed to load data', 'error', 3000);
    } finally {
      if (showLoading) setLoading(false);
      setRefreshing(false);
    }
  }, [customerSalon, showToast]);

  // Focus effect to reload data
  useFocusEffect(
    useCallback(() => {
      console.log('🔍 CustomerHomeScreen: Focus effect triggered');
      console.log('🔍 CustomerHomeScreen: salonLoading:', salonLoading);
      console.log('🔍 CustomerHomeScreen: customerSalon:', customerSalon?.salon?.name || 'null');
      loadData();
    }, [loadData, salonLoading, customerSalon])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadData(false);
  };

  // Navigation handlers
  const handleSearchPress = () => {
    (navigation as any).navigate('Search');
  };

  const handleCategoryPress = (category: ServiceCategory) => {
    console.log('📱 Category pressed:', category.name);
    (navigation as any).navigate('Services', {
      category: category.name === 'All' ? undefined : category.name
    });
  };

  const handleOfferPress = (offer: CustomerOffer) => {
    console.log('📱 Offer pressed:', offer.title);
    // Navigate to offer details (to be implemented)
    // navigation.navigate('OfferDetails', { offerId: offer.id });
  };

  const handleServicePress = (service: ServiceResponse) => {
    console.log('📱 Service pressed:', service.name);
    (navigation as any).navigate('ServiceDetails', { serviceId: service.id });
  };

  const handleSeeAllServices = () => {
    (navigation as any).navigate('Services', {});
  };

  const handleSeeAllOffers = () => {
    (navigation as any).navigate('Services', {});
  };

  // Render methods
  const renderServiceCategory = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.categoryIcon, { backgroundColor: item.color }]}>
        <Icon name={item.icon} size={24} color={Colors.white} />
      </View>
      <DarkBody size="small" style={styles.categoryText}>
        {item.name}
      </DarkBody>
      <DarkBody size="small" style={styles.categoryCount}>
        {item.serviceCount} services
      </DarkBody>
    </TouchableOpacity>
  );

  const renderOffer = ({ item }: { item: CustomerOffer }) => (
    <TouchableOpacity
      style={styles.offerCard}
      onPress={() => handleOfferPress(item)}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={Gradients.primaryButton}
        style={styles.offerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.offerContent}>
          <View style={styles.offerText}>
            <WhiteTitle level={4}>{item.title}</WhiteTitle>
            <WhiteTitle level={2} style={styles.discountText}>
              {customerOfferService.formatDiscount(item)}
            </WhiteTitle>
            <DarkBody size="small" style={styles.validText}>
              {customerOfferService.formatValidPeriod(item)}
            </DarkBody>
            <TouchableOpacity style={styles.offerButton}>
              <DarkBody size="medium" style={styles.offerButtonText}>
                {item.isEligible ? 'Get Offer' : 'View Details'}
              </DarkBody>
            </TouchableOpacity>
          </View>
          {item.image && (
            <Image
              source={{ uri: item.image }}
              style={styles.offerImage}
            />
          )}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderService = ({ item }: { item: ServiceResponse }) => (
    <TouchableOpacity
      style={styles.serviceCard}
      onPress={() => handleServicePress(item)}
      activeOpacity={0.7}
    >
      {item.image && (
        <Image source={{ uri: item.image }} style={styles.serviceImage} />
      )}
      <View style={styles.serviceInfo}>
        <DarkTitle level={4} style={styles.serviceName}>
          {item.name}
        </DarkTitle>
        <DarkBody size="small" style={styles.serviceCategory}>
          {item.category}
        </DarkBody>
        <View style={styles.serviceDetails}>
          <DarkBody size="small" style={styles.servicePrice}>
            {formatPrice(item)}
          </DarkBody>
          <DarkBody size="small" style={styles.serviceDuration}>
            • {formatDuration(item)}
          </DarkBody>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Show loading while salon context is loading
  if (salonLoading) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody size="medium" style={styles.loadingText}>
            Loading salon information...
          </DarkBody>
        </View>
      </ScreenWrapper>
    );
  }

  // Show salon selection if user needs to select a salon
  if (needsSalonSelection) {
    return (
      <ScreenWrapper>
        <View style={styles.noSalonContainer}>
          <Icon name="business" size={width * 0.2} color={Colors.primary} />
          <DarkTitle level={3} textAlign="center" style={styles.noSalonText}>
            Multiple Salons Available
          </DarkTitle>
          <DarkBody size="medium" textAlign="center" style={styles.noSalonSubtext}>
            You have access to {accessibleSalons.length} salons. Please select one to continue.
          </DarkBody>
          <TouchableOpacity
            style={styles.selectSalonButton}
            onPress={() => (navigation as any).navigate('CustomerSalonSelection')}
          >
            <DarkBody size="medium" style={styles.selectSalonButtonText}>
              Select Salon
            </DarkBody>
          </TouchableOpacity>
        </View>
      </ScreenWrapper>
    );
  }

  // Show no salon selected if no salon after loading
  if (!customerSalon?.salon) {
    return (
      <ScreenWrapper>
        <View style={styles.noSalonContainer}>
          <Icon name="business" size={width * 0.2} color={Colors.gray400} />
          <DarkTitle level={3} textAlign="center" style={styles.noSalonText}>
            No salon access
          </DarkTitle>
          <DarkBody size="medium" textAlign="center" style={styles.noSalonSubtext}>
            You don't have access to any salons yet. Please contact a salon owner to get access.
          </DarkBody>
          <TouchableOpacity
            style={styles.selectSalonButton}
            onPress={() => (navigation as any).navigate('CustomerSalonSelection')}
          >
            <DarkBody size="medium" style={styles.selectSalonButtonText}>
              Refresh
            </DarkBody>
          </TouchableOpacity>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
            progressBackgroundColor={Colors.white}
          />
        }
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <DarkBody size="medium" style={styles.greetingText}>
            {greeting}
          </DarkBody>
          <DarkTitle level={2} style={styles.userName}>
            {user?.firstName || 'Customer'}
          </DarkTitle>
          <DarkBody size="small" style={styles.salonName}>
            at {customerSalon.salon?.name}
          </DarkBody>

          {/* Search Bar */}
          <TouchableOpacity style={styles.searchBar} onPress={handleSearchPress}>
            <Icon name="search" size={20} color={Colors.textSecondary} />
            <DarkBody size="medium" style={styles.searchText}>
              Search services, offers...
            </DarkBody>
            <Icon name="tune" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Service Categories */}
        {serviceCategories.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <DarkTitle level={3} style={styles.sectionTitle}>
                Service Categories
              </DarkTitle>
            </View>
            <FlatList
              data={serviceCategories}
              renderItem={renderServiceCategory}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesContainer}
            />
          </View>
        )}

        {/* Special Offers */}
        {activeOffers.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <DarkTitle level={3} style={styles.sectionTitle}>
                Special Offers
              </DarkTitle>
              <TouchableOpacity onPress={handleSeeAllOffers}>
                <DarkBody size="medium" style={styles.seeAllText}>
                  See all
                </DarkBody>
              </TouchableOpacity>
            </View>

            <FlatList
              data={activeOffers}
              renderItem={renderOffer}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.offersContainer}
            />
          </View>
        )}

        {/* Popular Services */}
        {popularServices.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <DarkTitle level={3} style={styles.sectionTitle}>
                Popular Services
              </DarkTitle>
              <TouchableOpacity onPress={handleSeeAllServices}>
                <DarkBody size="medium" style={styles.seeAllText}>
                  See all
                </DarkBody>
              </TouchableOpacity>
            </View>

            <FlatList
              data={popularServices}
              renderItem={renderService}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.servicesContainer}
            />
          </View>
        )}

        {/* Recommended for You */}
        {recommendedServices.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <DarkTitle level={3} style={styles.sectionTitle}>
                Recommended for You
              </DarkTitle>
              <TouchableOpacity onPress={handleSeeAllServices}>
                <DarkBody size="medium" style={styles.seeAllText}>
                  See all
                </DarkBody>
              </TouchableOpacity>
            </View>

            <FlatList
              data={recommendedServices}
              renderItem={renderService}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.servicesContainer}
            />
          </View>
        )}

        {/* Empty State */}
        {!loading && serviceCategories.length === 0 && activeOffers.length === 0 && popularServices.length === 0 && (
          <View style={styles.emptyContainer}>
            <Icon name="spa" size={width * 0.15} color={Colors.gray400} />
            <DarkTitle level={3} textAlign="center" style={styles.emptyTitle}>
              Welcome to {customerSalon.salon?.name}!
            </DarkTitle>
            <DarkBody size="medium" textAlign="center" style={styles.emptyText}>
              This salon is setting up their services and offers. Check back soon for amazing deals and services!
            </DarkBody>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={() => loadData()}
            >
              <DarkBody size="medium" style={styles.refreshButtonText}>
                Refresh
              </DarkBody>
            </TouchableOpacity>
          </View>
        )}

        {/* Loading State */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <DarkBody size="medium" style={styles.loadingText}>
              Loading services and offers...
            </DarkBody>
          </View>
        )}
      </ScrollView>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: height * 0.05,
  },
  welcomeSection: {
    paddingHorizontal: width * 0.05,
    paddingTop: height * 0.02,
    paddingBottom: height * 0.02,
  },
  greetingText: {
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  userName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  salonName: {
    color: Colors.primary,
    marginBottom: height * 0.02,
  },
  noSalonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  noSalonText: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  noSalonSubtext: {
    color: Colors.textSecondary,
    marginBottom: height * 0.03,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  loadingText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  selectSalonButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.015,
    borderRadius: width * 0.02,
  },
  selectSalonButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.015,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchText: {
    flex: 1,
    marginLeft: width * 0.03,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: height * 0.03,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: width * 0.05,
    marginBottom: height * 0.02,
  },
  sectionTitle: {
    color: Colors.textPrimary,
  },
  seeAllText: {
    color: Colors.primary,
  },
  categoriesContainer: {
    paddingHorizontal: width * 0.05,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: width * 0.06,
  },
  categoryIcon: {
    width: width * 0.15,
    height: width * 0.15,
    borderRadius: width * 0.075,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: height * 0.01,
  },
  categoryText: {
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: height * 0.005,
  },
  categoryCount: {
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  offersContainer: {
    paddingHorizontal: width * 0.05,
  },
  servicesContainer: {
    paddingHorizontal: width * 0.05,
  },
  serviceCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    marginRight: width * 0.04,
    width: width * 0.4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  serviceImage: {
    width: '100%',
    height: width * 0.25,
  },
  serviceInfo: {
    padding: width * 0.03,
  },
  serviceName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  serviceCategory: {
    color: Colors.textSecondary,
    marginBottom: height * 0.01,
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  servicePrice: {
    color: Colors.primary,
    fontWeight: '600',
  },
  serviceDuration: {
    color: Colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
    paddingVertical: height * 0.1,
  },
  emptyTitle: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  emptyText: {
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: height * 0.03,
  },
  refreshButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.015,
    borderRadius: width * 0.02,
  },
  refreshButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  offerCard: {
    marginHorizontal: width * 0.05,
    borderRadius: width * 0.04,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  offerGradient: {
    padding: width * 0.05,
  },
  offerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  offerText: {
    flex: 1,
  },
  discountText: {
    marginVertical: height * 0.01,
  },
  validText: {
    color: Colors.white,
    opacity: 0.9,
    marginBottom: height * 0.02,
  },
  offerButton: {
    backgroundColor: Colors.white,
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.01,
    borderRadius: width * 0.02,
    alignSelf: 'flex-start',
  },
  offerButtonText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  offerImage: {
    width: width * 0.25,
    height: width * 0.25,
    borderRadius: width * 0.03,
    marginLeft: width * 0.03,
  },
  staffContainer: {
    paddingHorizontal: width * 0.05,
  },
  staffCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.03,
    marginRight: width * 0.04,
    width: width * 0.35,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  staffImage: {
    width: '100%',
    height: width * 0.25,
    borderRadius: width * 0.02,
    marginBottom: height * 0.01,
  },
  staffInfo: {
    alignItems: 'center',
  },
  staffName: {
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: 2,
  },
  staffSpecialty: {
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 4,
  },
  staffRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    color: Colors.textPrimary,
    marginLeft: 2,
  },
  experienceText: {
    color: Colors.textSecondary,
    marginLeft: 2,
  },
  nearbyGrid: {
    flexDirection: 'row',
    paddingHorizontal: width * 0.05,
    justifyContent: 'space-between',
  },
  nearbyCard: {
    position: 'relative',
    width: (width - width * 0.15) / 2,
    height: width * 0.4,
    borderRadius: width * 0.03,
    overflow: 'hidden',
  },
  nearbyImage: {
    width: '100%',
    height: '100%',
  },
  favoriteButton: {
    position: 'absolute',
    top: width * 0.02,
    right: width * 0.02,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
