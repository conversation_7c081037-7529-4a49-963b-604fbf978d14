# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 43ms
  [gap of 15ms]
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 24ms
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 48ms
  [gap of 16ms]
generate_cxx_metadata completed in 80ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 22ms
  [gap of 12ms]
generate_cxx_metadata completed in 50ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 15ms
  [gap of 20ms]
generate_cxx_metadata completed in 53ms

