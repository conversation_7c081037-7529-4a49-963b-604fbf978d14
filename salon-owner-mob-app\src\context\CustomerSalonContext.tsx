import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiService } from '../services/api';
import { useAuth } from './AuthContext';

export interface CustomerSalonData {
  id: string;
  customerId: string;
  salonId: string;
  status: string;
  grantedAt?: string;
  expiresAt?: string;
  settings?: Record<string, any>;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  salon?: {
    id: string;
    name: string;
    description?: string;
    logo?: string;
    address?: string;
    city?: string;
    phone?: string;
    email?: string;
  };
  // Staff-specific fields
  isStaff?: boolean;
  permissions?: string[];
  position?: string;
}

interface CustomerSalonContextType {
  accessibleSalons: CustomerSalonData[];
  selectedSalon: CustomerSalonData | null;
  isLoading: boolean;
  selectSalon: (salon: CustomerSalonData) => void;
  refreshAccessibleSalons: () => Promise<void>;
  hasMultipleSalons: boolean;
  needsSalonSelection: boolean;
}

interface CustomerSalonProviderProps {
  children: ReactNode;
}

const CustomerSalonContext = createContext<CustomerSalonContextType | undefined>(undefined);

export const useCustomerSalon = (): CustomerSalonContextType => {
  const context = useContext(CustomerSalonContext);
  if (!context) {
    throw new Error('useCustomerSalon must be used within a CustomerSalonProvider');
  }
  return context;
};

export const CustomerSalonProvider: React.FC<CustomerSalonProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [accessibleSalons, setAccessibleSalons] = useState<CustomerSalonData[]>([]);
  const [selectedSalon, setSelectedSalon] = useState<CustomerSalonData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check if customer has multiple salons
  const hasMultipleSalons = accessibleSalons.length > 1;
  
  // Check if customer needs to select a salon (has multiple salons but none selected)
  const needsSalonSelection = hasMultipleSalons && !selectedSalon;

  // Load accessible salons for customer or staff
  const refreshAccessibleSalons = async (): Promise<void> => {
    if (!isAuthenticated || !user || (user.role !== 'CUSTOMER' && user.role !== 'STAFF')) {
      return;
    }

    try {
      setIsLoading(true);
      console.log('🔍 CustomerSalon: Fetching accessible salons for user:', user.id, 'role:', user.role);

      let response;

      if (user.role === 'CUSTOMER') {
        // Use customer salon access API
        response = await apiService.get(`/customer-salon-access/my-salons/${user.id}`);
      } else if (user.role === 'STAFF') {
        // Use staff salon access API
        response = await apiService.get(`/staff/my/salons/${user.id}`);
      }

      if (response?.data) {
        const salons = (response.data as any) || [];
       
        // Transform staff salon data to match customer salon data structure
        const transformedSalons = user.role === 'STAFF'
          ? salons.map((staffAccess: any) => ({
              id: staffAccess.id,
              customerId: user.id, // Use user ID for consistency
              salonId: staffAccess.salonId,
              status: staffAccess.status,
              grantedAt: staffAccess.grantedAt,
              createdAt: staffAccess.createdAt,
              updatedAt: staffAccess.updatedAt,
              salon: staffAccess.salon,
              // Add staff-specific data
              isStaff: true,
              permissions: staffAccess.permissions,
              position: staffAccess.position,
            }))
          : salons;

        setAccessibleSalons(transformedSalons);

        console.log('✅ CustomerSalon: Found', transformedSalons.length, 'accessible salons');

        // Auto-select salon if user has only one
        if (transformedSalons.length === 1 && !selectedSalon) {
          setSelectedSalon(transformedSalons[0]);
          console.log('✅ CustomerSalon: Auto-selected single salon:', transformedSalons[0].salon?.name);
        }

        // If user had a selected salon but it's no longer accessible, clear selection
        if (selectedSalon && !transformedSalons.find((s: any) => s.salonId === selectedSalon.salonId)) {
          setSelectedSalon(null);
          console.log('⚠️ CustomerSalon: Previously selected salon no longer accessible');
        }
      } else {
        console.error('❌ CustomerSalon: Failed to fetch accessible salons:', (response?.data as any)?.error);
        setAccessibleSalons([]);
      }
    } catch (error) {
      console.error('❌ CustomerSalon: Error fetching accessible salons:', error);
      setAccessibleSalons([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Select a salon
  const selectSalon = (salon: CustomerSalonData): void => {
    setSelectedSalon(salon);
    console.log('✅ CustomerSalon: Selected salon:', salon.salon?.name);
    
    // Store selection in AsyncStorage for persistence
    try {
      import('@react-native-async-storage/async-storage').then(({ default: AsyncStorage }) => {
        AsyncStorage.setItem('selectedSalonId', salon.salonId);
      });
    } catch (error) {
      console.error('❌ CustomerSalon: Error storing salon selection:', error);
    }
  };

  // Load persisted salon selection
  const loadPersistedSalonSelection = async (): Promise<void> => {
    try {
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage');
      const selectedSalonId = await AsyncStorage.getItem('selectedSalonId');
      
      if (selectedSalonId && accessibleSalons.length > 0) {
        const salon = accessibleSalons.find(s => s.salonId === selectedSalonId);
        if (salon) {
          setSelectedSalon(salon);
          console.log('✅ CustomerSalon: Restored salon selection:', salon.salon?.name);
        }
      }
    } catch (error) {
      console.error('❌ CustomerSalon: Error loading persisted salon selection:', error);
    }
  };

  // Initialize accessible salons when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user && (user.role === 'CUSTOMER' || user.role === 'STAFF')) {
      refreshAccessibleSalons();
    } else {
      // Clear data when user is not authenticated or not a customer/staff
      setAccessibleSalons([]);
      setSelectedSalon(null);
    }
  }, [isAuthenticated, user]);

  // Load persisted selection when accessible salons are loaded
  useEffect(() => {
    if (accessibleSalons.length > 0 && !selectedSalon) {
      loadPersistedSalonSelection();
    }
  }, [accessibleSalons]);

  // Handle login response with accessible salons
  useEffect(() => {
    if (user && (user.role === 'CUSTOMER' || user.role === 'STAFF') && (user as any).accessibleSalons) {
      const salons = (user as any).accessibleSalons;
      setAccessibleSalons(salons);

      console.log('✅ CustomerSalon: Loaded salons from login response:', salons.length);

      // Auto-select if only one salon
      if (salons.length === 1) {
        setSelectedSalon(salons[0]);
        console.log('✅ CustomerSalon: Auto-selected single salon from login:', salons[0].salon?.name);
      }
    }
  }, [user]);

  const contextValue: CustomerSalonContextType = {
    accessibleSalons,
    selectedSalon,
    isLoading,
    selectSalon,
    refreshAccessibleSalons,
    hasMultipleSalons,
    needsSalonSelection,
  };

  return (
    <CustomerSalonContext.Provider value={contextValue}>
      {children}
    </CustomerSalonContext.Provider>
  );
};

export default CustomerSalonProvider;
