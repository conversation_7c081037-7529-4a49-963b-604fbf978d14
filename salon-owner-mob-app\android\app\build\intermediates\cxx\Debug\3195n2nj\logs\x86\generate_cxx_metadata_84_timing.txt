# C/C++ build system timings
generate_cxx_metadata
  [gap of 36ms]
  create-invalidation-state 52ms
  [gap of 23ms]
generate_cxx_metadata completed in 111ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 11ms
  [gap of 16ms]
generate_cxx_metadata completed in 30ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 22ms
  [gap of 13ms]
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 56ms
  [gap of 16ms]
generate_cxx_metadata completed in 97ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 12ms
  [gap of 23ms]
generate_cxx_metadata completed in 39ms

# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 809ms
    [gap of 31ms]
  generate-prefab-packages completed in 841ms
  execute-generate-process
    exec-configure 737ms
    [gap of 150ms]
  execute-generate-process completed in 887ms
  [gap of 82ms]
generate_cxx_metadata completed in 1819ms

