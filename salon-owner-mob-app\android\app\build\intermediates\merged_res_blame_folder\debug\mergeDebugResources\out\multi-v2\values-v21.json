{"logs": [{"outputFile": "com.saloon_app-mergeDebugResources-50:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cf9bc6b4f1abcf764423b202ca0f1044\\transformed\\material-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,248,344,441,509,588,676,764,852,940,1027,1114,1201,1288,1384,1474,1570,1660,1753,1860,1965,2084,2209,2330,2543,2802,3073,3291,3523,3759,4009,4222,4431,4662,4863,4979,5149,5470,6499,6956,7460,7968,8477,8991,9496,10000,10505,11011,11513,12019,12528,13036,13535,14042,14550,14842,15136,15436,15736,16065,16406,16544,16688,16844,17237,17455,17677,17903,18119,18229,18399,18589,18830,19089", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,94,95,96,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,243,339,436,504,583,671,759,847,935,1022,1109,1196,1283,1379,1469,1565,1655,1748,1855,1960,2079,2204,2325,2538,2797,3068,3286,3518,3754,4004,4217,4426,4657,4858,4974,5144,5465,6494,6951,7455,7963,8472,8986,9491,9995,10500,11006,11508,12014,12523,13031,13530,14037,14545,14837,15131,15431,15731,16060,16401,16539,16683,16839,17232,17450,17672,17898,18114,18224,18394,18584,18825,19084,19261"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,84,85,86,87,89,90,91,94,97,192,195,198,201,207,210,213,280,283,284,287,292,303,351,360,369,378,387,396,405,414,423,432,441,450,459,468,477,486,492,498,504,510,514,518,519,520,521,525,528,531,534,545,546,549,552,556,560", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,366,462,559,627,706,794,882,970,1058,1145,1232,1319,1601,1697,1787,1883,7340,7433,7540,7645,7867,7992,8113,8326,8585,14766,14984,15216,15452,15901,16114,16323,21110,21311,21427,21597,21918,22947,26052,26556,27064,27573,28087,28592,29096,29601,30107,30609,31115,31624,32132,32631,33138,33646,33938,34232,34532,34832,35161,35502,35640,35784,35940,36333,36551,36773,36999,37739,37849,38019,38209,38450,38709", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,84,85,86,87,89,90,93,96,99,194,197,200,203,209,212,215,282,283,286,291,302,308,359,368,377,386,395,404,413,422,431,440,449,458,467,476,485,491,497,503,509,513,517,518,519,520,524,527,530,533,536,545,548,551,555,559,562", "endColumns": "97,94,95,96,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,361,457,554,622,701,789,877,965,1053,1140,1227,1314,1401,1692,1782,1878,1968,7428,7535,7640,7759,7987,8108,8321,8580,8851,14979,15211,15447,15697,16109,16318,16549,21306,21422,21592,21913,22942,23399,26551,27059,27568,28082,28587,29091,29596,30102,30604,31110,31619,32127,32626,33133,33641,33933,34227,34527,34827,35156,35497,35635,35779,35935,36328,36546,36768,36994,37210,37844,38014,38204,38445,38704,38881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af63e50a53e992ea9c751fc2deb2fc57\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "24,25,26,27,28,29,30,31,32,33,34,35,36,37,39,41,42,43,44,46,48,49,50,51,52,54,56,58,60,62,64,65,70,72,74,75,76,78,80,81,82,83,88,100,143,146,189,204,216,218,220,222,225,229,232,233,234,237,238,239,240,241,242,245,246,248,250,252,254,258,260,261,262,263,265,269,271,273,274,275,276,277,278,309,310,311,321,322,323,335", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1973,2064,2167,2270,2375,2482,2591,2700,2809,2918,3027,3134,3237,3356,3511,3666,3771,3892,3993,4140,4281,4384,4503,4610,4713,4868,5039,5188,5353,5510,5661,5780,6131,6280,6429,6541,6688,6841,6988,7063,7152,7239,7764,8856,11614,11799,14569,15702,16554,16677,16800,16913,17096,17351,17552,17641,17752,17985,18086,18181,18304,18433,18550,18727,18826,18961,19104,19239,19358,19559,19678,19771,19882,19938,20045,20240,20351,20484,20579,20670,20761,20854,20971,23404,23475,23558,24181,24238,24296,24920", "endLines": "24,25,26,27,28,29,30,31,32,33,34,35,36,38,40,41,42,43,45,47,48,49,50,51,53,55,57,59,61,63,64,69,71,73,74,75,77,79,80,81,82,83,88,142,145,188,191,206,217,219,221,224,228,231,232,233,236,237,238,239,240,241,244,245,247,249,251,253,257,259,260,261,262,264,268,270,272,273,274,275,276,277,279,309,310,320,321,322,334,346", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2059,2162,2265,2370,2477,2586,2695,2804,2913,3022,3129,3232,3351,3506,3661,3766,3887,3988,4135,4276,4379,4498,4605,4708,4863,5034,5183,5348,5505,5656,5775,6126,6275,6424,6536,6683,6836,6983,7058,7147,7234,7335,7862,11609,11794,14564,14761,15896,16672,16795,16908,17091,17346,17547,17636,17747,17980,18081,18176,18299,18428,18545,18722,18821,18956,19099,19234,19353,19554,19673,19766,19877,19933,20040,20235,20346,20479,20574,20665,20756,20849,20966,21105,23470,23553,24176,24233,24291,24915,25551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3135f157d17fbe3966fe84b667792db9\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,347,348,349,350,537,540", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1406,1470,1537,25556,25672,25798,25924,37215,37387", "endLines": "2,17,18,19,347,348,349,350,539,544", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1465,1532,1596,25667,25793,25919,26047,37382,37734"}}]}]}