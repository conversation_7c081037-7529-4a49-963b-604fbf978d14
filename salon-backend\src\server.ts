import 'reflect-metadata';
import dotenv from 'dotenv';
dotenv.config();

import app from './app';
import logger from './utils/logger';
import { initializeDatabase } from './config/typeorm';
import { connectToRedis } from './config/redis';
import { firebaseConfig } from './config/firebase';
import { initializePermissions } from './scripts/initializePermissions';

const PORT = process.env.PORT || 5000;

// Initialize TypeORM
initializeDatabase()
  .then(() => {
    // Initialize default permissions
    logger.info('Initializing default permissions...');
    return initializePermissions();
  })
  .then(() => {
    // Connect to Redis
    connectToRedis();

    // Initialize Firebase
    return firebaseConfig.initialize();
  })
  .then(() => {
    // Start the server
    app.listen(PORT, () => {
      logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
      logger.info('✅ All services initialized successfully');
    });
  })
  .catch((error) => {
    logger.error('Failed to initialize application:', error);
    process.exit(1);
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  logger.error(`Unhandled Rejection: ${err.message}`);
  // Close server & exit process
  process.exit(1);
});