import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from './User';
import { Salon } from './Salon';

export enum StaffAccessStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  REVOKED = 'REVOKED',
}

export enum StaffPermission {
  VIEW_SERVICES = 'VIEW_SERVICES',
  MANAGE_SERVICES = 'MANAGE_SERVICES',
  VIEW_OFFERS = 'VIEW_OFFERS',
  MANAGE_OFFERS = 'MANAGE_OFFERS',
  VIEW_BOOKINGS = 'VIEW_BOOKINGS',
  MANAGE_BOOKINGS = 'MANAGE_BOOKINGS',
  VIEW_CUSTOMERS = 'VIEW_CUSTOMERS',
  MANAGE_CUSTOMERS = 'MANAGE_CUSTOMERS',
  VIEW_REPORTS = 'VIEW_REPORTS',
  MANAGE_STAFF = 'MANAGE_STAFF',
}

@Entity('staff_salon_access')
@Unique(['staffId', 'salonId'])
@Index(['staffId', 'status'])
@Index(['salonId', 'status'])
export class StaffSalonAccess {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'uuid',
  })
  staffId!: string;

  @Column({
    type: 'uuid',
  })
  salonId!: string;

  @Column({
    type: 'enum',
    enum: StaffAccessStatus,
    default: StaffAccessStatus.ACTIVE,
  })
  status!: StaffAccessStatus;

  @Column({
    type: 'json',
    comment: 'Array of permissions for this staff member at this salon',
  })
  permissions!: StaffPermission[];

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Job title or position at this salon',
  })
  position?: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Hourly rate or salary for this position',
  })
  hourlyRate?: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Working hours for each day of the week',
  })
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Start date of employment',
  })
  startDate?: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'End date of employment (if terminated)',
  })
  endDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes about this staff member',
  })
  notes?: string;

  @Column({
    type: 'uuid',
    comment: 'ID of the admin who granted this access',
  })
  grantedBy!: string;

  @Column({
    type: 'timestamp',
    comment: 'When the access was granted',
  })
  grantedAt!: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the admin who last modified this access',
  })
  lastModifiedBy?: string;

  // Relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'staffId' })
  staff!: User;

  @ManyToOne(() => Salon, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'salonId' })
  salon!: Salon;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'grantedBy' })
  grantor!: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lastModifiedBy' })
  lastModifier?: User;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === StaffAccessStatus.ACTIVE;
  }

  get hasPermission(): (permission: StaffPermission) => boolean {
    return (permission: StaffPermission) => {
      return this.isActive && this.permissions.includes(permission);
    };
  }

  get displayPosition(): string {
    return this.position || 'Staff Member';
  }
}
