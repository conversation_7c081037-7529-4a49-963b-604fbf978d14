import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Join<PERSON>olumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from './User';
import { Salon } from './Salon';
import { StaffPermission } from './StaffPermission';

export enum StaffAccessStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  REVOKED = 'REVOKED',
}

// Note: StaffPermission enum moved to Permission entity
// Individual permissions are now stored in the permissions table

export enum StaffPosition {
  // Management
  SALON_MANAGER = 'SALON_MANAGER',
  ASSISTANT_MANAGER = 'ASSISTANT_MANAGER',
  SUPERVISOR = 'SUPERVISOR',

  // Hair Services
  SENIOR_HAIR_STYLIST = 'SENIOR_HAIR_STYLIST',
  HAIR_STYLIST = 'HAIR_STYLIST',
  JUNIOR_HAIR_STYLIST = 'JUNIOR_HAIR_STYLIST',
  HAIR_COLORIST = 'HAIR_COLORIST',
  BARBER = 'BARBER',

  // Beauty Services
  ESTHETICIAN = 'ESTHETICIAN',
  MAKEUP_ARTIST = 'MAKEUP_ARTIST',
  EYEBROW_SPECIALIST = 'EYEBROW_SPECIALIST',
  LASH_TECHNICIAN = 'LASH_TECHNICIAN',

  // Nail Services
  NAIL_TECHNICIAN = 'NAIL_TECHNICIAN',
  NAIL_ARTIST = 'NAIL_ARTIST',

  // Spa Services
  MASSAGE_THERAPIST = 'MASSAGE_THERAPIST',
  SPA_THERAPIST = 'SPA_THERAPIST',

  // Support Staff
  RECEPTIONIST = 'RECEPTIONIST',
  ASSISTANT = 'ASSISTANT',
  APPRENTICE = 'APPRENTICE',
  CLEANER = 'CLEANER',

  // Other
  FREELANCER = 'FREELANCER',
  CONSULTANT = 'CONSULTANT',
  TRAINER = 'TRAINER',
  OTHER = 'OTHER',
}

@Entity('staff_salon_access')
@Unique(['staffId', 'salonId'])
@Index(['staffId', 'status'])
@Index(['salonId', 'status'])
export class StaffSalonAccess {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'uuid',
  })
  staffId!: string;

  @Column({
    type: 'uuid',
  })
  salonId!: string;

  @Column({
    type: 'enum',
    enum: StaffAccessStatus,
    default: StaffAccessStatus.ACTIVE,
  })
  status!: StaffAccessStatus;

  // Permissions are now stored in separate StaffPermission table

  @Column({
    type: 'enum',
    enum: StaffPosition,
    nullable: true,
    comment: 'Job title or position at this salon',
  })
  position?: StaffPosition;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Hourly rate or salary for this position',
  })
  hourlyRate?: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Working hours for each day of the week',
  })
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Start date of employment',
  })
  startDate?: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'End date of employment (if terminated)',
  })
  endDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes about this staff member',
  })
  notes?: string;

  @Column({
    type: 'uuid',
    comment: 'ID of the admin who granted this access',
  })
  grantedBy!: string;

  @Column({
    type: 'timestamp',
    comment: 'When the access was granted',
  })
  grantedAt!: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the admin who last modified this access',
  })
  lastModifiedBy?: string;

  // Relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'staffId' })
  staff!: User;

  @ManyToOne(() => Salon, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'salonId' })
  salon!: Salon;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'grantedBy' })
  grantor!: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lastModifiedBy' })
  lastModifier?: User;

  @OneToMany(() => StaffPermission, staffPermission => staffPermission.staffSalonAccess)
  staffPermissions!: StaffPermission[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === StaffAccessStatus.ACTIVE;
  }

  get activePermissions(): string[] {
    if (!this.staffPermissions) return [];
    return this.staffPermissions
      .filter(sp => sp.isCurrentlyActive)
      .map(sp => sp.permission.code);
  }

  hasPermission(permissionCode: string): boolean {
    return this.isActive && this.activePermissions.includes(permissionCode);
  }

  get displayPosition(): string {
    if (!this.position) return 'Staff Member';
    return this.position.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }
}
