import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getCustomerSalons,
  getSalonCustomers,
  grantAccess,
  revokeAccess,
  checkAccess,
  getAvailableSalons,
} from '../controllers/customerSalonAccessController';
import { protect, authorize } from '../middlewares/authMiddleware';
import { UserRole } from '../entities/User';

const router = express.Router();

// All routes require authentication
router.use(protect);

/**
 * @swagger
 * /api/v1/customer-salon-access/my-salons:
 *   get:
 *     summary: Get customer's accessible salons
 *     tags: [Customer Salon Access]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Customer's accessible salons retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/my-salons', getCustomerSalons);

/**
 * @swagger
 * /api/v1/customer-salon-access/salon/{salonId}/customers:
 *   get:
 *     summary: Get salon's customers (Admin/Staff only)
 *     tags: [Customer Salon Access]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: salonId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Salon ID
 *     responses:
 *       200:
 *         description: Salon customers retrieved successfully
 *       400:
 *         description: Invalid salon ID
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.get(
  '/salon/:salonId/customers',
  [
    param('salonId')
      .isUUID()
      .withMessage('Invalid salon ID'),
  ],
  authorize(UserRole.ADMIN, UserRole.STAFF),
  getSalonCustomers
);

/**
 * @swagger
 * /api/v1/customer-salon-access/grant:
 *   post:
 *     summary: Grant customer access to a salon (Admin only)
 *     tags: [Customer Salon Access]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - salonId
 *             properties:
 *               customerId:
 *                 type: string
 *                 format: uuid
 *                 description: Customer user ID
 *               salonId:
 *                 type: string
 *                 format: uuid
 *                 description: Salon ID
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *                 description: Access expiry date (optional)
 *               notes:
 *                 type: string
 *                 description: Notes about the access grant
 *               settings:
 *                 type: object
 *                 description: Additional access settings
 *     responses:
 *       201:
 *         description: Access granted successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.post(
  '/grant',
  [
    body('customerId')
      .isUUID()
      .withMessage('Valid customer ID is required'),
    body('salonId')
      .isUUID()
      .withMessage('Valid salon ID is required'),
    body('expiresAt')
      .optional()
      .isISO8601()
      .withMessage('Invalid expiry date format'),
    body('notes')
      .optional()
      .isString()
      .withMessage('Notes must be a string'),
  ],
  authorize(UserRole.ADMIN),
  grantAccess
);

/**
 * @swagger
 * /api/v1/customer-salon-access/revoke:
 *   post:
 *     summary: Revoke customer access to a salon (Admin only)
 *     tags: [Customer Salon Access]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - salonId
 *             properties:
 *               customerId:
 *                 type: string
 *                 format: uuid
 *                 description: Customer user ID
 *               salonId:
 *                 type: string
 *                 format: uuid
 *                 description: Salon ID
 *     responses:
 *       200:
 *         description: Access revoked successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
router.post(
  '/revoke',
  [
    body('customerId')
      .isUUID()
      .withMessage('Valid customer ID is required'),
    body('salonId')
      .isUUID()
      .withMessage('Valid salon ID is required'),
  ],
  authorize(UserRole.ADMIN),
  revokeAccess
);

/**
 * @swagger
 * /api/v1/customer-salon-access/check/{salonId}:
 *   get:
 *     summary: Check if customer has access to a specific salon
 *     tags: [Customer Salon Access]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: salonId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Salon ID
 *     responses:
 *       200:
 *         description: Access check completed
 *       400:
 *         description: Invalid salon ID
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/check/:salonId',
  [
    param('salonId')
      .isUUID()
      .withMessage('Invalid salon ID'),
  ],
  checkAccess
);

/**
 * @swagger
 * /api/v1/customer-salon-access/available-salons:
 *   get:
 *     summary: Get available salons for customer registration
 *     tags: [Customer Salon Access]
 *     parameters:
 *       - in: query
 *         name: accountId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Account ID
 *     responses:
 *       200:
 *         description: Available salons retrieved successfully
 *       400:
 *         description: Account ID is required
 */
router.get(
  '/available-salons',
  [
    query('accountId')
      .isUUID()
      .withMessage('Valid account ID is required'),
  ],
  getAvailableSalons
);

export default router;
