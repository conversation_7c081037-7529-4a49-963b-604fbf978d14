-- Merging decision tree log ---
manifest
ADDED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml:1:1-4:12
INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml:1:1-4:12
	package
		ADDED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml:2:5-52
		INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml:1:11-69
uses-sdk
INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml
INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\src\main\AndroidManifest.xml
