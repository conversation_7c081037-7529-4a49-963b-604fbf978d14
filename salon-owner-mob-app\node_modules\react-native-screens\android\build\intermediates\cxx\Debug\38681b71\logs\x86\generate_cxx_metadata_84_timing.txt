# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 41ms
  [gap of 10ms]
generate_cxx_metadata completed in 78ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 25ms
generate_cxx_metadata completed in 48ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 46ms
  [gap of 29ms]
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 23ms
  [gap of 10ms]
generate_cxx_metadata completed in 45ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 16ms
  [gap of 15ms]
generate_cxx_metadata completed in 47ms

