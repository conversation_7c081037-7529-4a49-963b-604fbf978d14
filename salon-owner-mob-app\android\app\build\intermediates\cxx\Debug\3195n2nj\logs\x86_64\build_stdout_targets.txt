ninja: Entering directory `D:\sgic-product\salon-owner-mob-app\android\app\.cxx\Debug\3195n2nj\x86_64'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: D:/sgic-product/salon-owner-mob-app/android/app/.cxx/Debug/3195n2nj/x86_64
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/D_/sgic-product/salon-owner-mob-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[3/3] Linking CXX shared library D:\sgic-product\salon-owner-mob-app\android\app\build\intermediates\cxx\Debug\3195n2nj\obj\x86_64\libappmodules.so
