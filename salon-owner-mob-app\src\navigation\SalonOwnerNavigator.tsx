import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SalonRegisterScreen } from '../screens/salon/SalonRegisterScreen';
import { SalonSelectionScreen } from '../screens/salon/SalonSelectionScreen';
import { SalonOwnerHomeScreen } from '../screens/salon/SalonOwnerHomeScreen';
import { EditSalonScreen } from '../screens/salon/EditSalonScreen';
import { BookingsScreen } from '../screens/salon/BookingsScreen';
import { ServicesScreen } from '../screens/salon/ServicesScreen';
import { AddServiceScreen } from '../screens/services/AddServiceScreen';
import { EditServiceScreen } from '../screens/services/EditServiceScreen';
import { ServiceDetailsScreen } from '../screens/services/ServiceDetailsScreen';
import { StaffScreen } from '../screens/staff/StaffScreen';
import { AddStaffScreen } from '../screens/staff/AddStaffScreen';
import { OffersScreen } from '../screens/offers/OffersScreen';
import { ProfileScreen } from '../screens/profile/ProfileScreen';
import { SalonOwnerStackParamList, SalonOwnerTabParamList } from '../types';
import { Colors } from '../constants/colors';
import { salonService } from '../services/salonService';
import { getAccountId } from '../constants/app';
import { DarkTitle } from '../components/common/Typography';
import { AddOfferScreen } from '../screens/offers/AddOfferScreen';
import { EditOfferScreen } from '../screens/offers/EditOfferScreen';
import { ViewOfferDetailsScreen } from '../screens/offers/ViewOfferDetailsScreen';
import { NotificationsScreen } from '../screens/notifications/NotificationsScreen';

const Stack = createStackNavigator<SalonOwnerStackParamList>();
const Tab = createBottomTabNavigator<SalonOwnerTabParamList>();

// Salon Owner Tab Navigator (after salon registration)
const SalonOwnerTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Bookings':
              iconName = 'event';
              break;
            case 'Services':
              iconName = 'content-cut';
              break;
            case 'Offers':
              iconName = 'local-offer';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'home';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textSecondary,
        tabBarStyle: {
          backgroundColor: Colors.white,
          borderTopColor: Colors.gray200,
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={SalonOwnerHomeScreen}
        options={{ title: 'Home' }}
      />
      <Tab.Screen
        name="Bookings"
        component={BookingsScreen}
        options={{ title: 'Bookings' }}
      />
      <Tab.Screen
        name="Services"
        component={ServicesScreen}
        options={{ title: 'Services' }}
      />
      <Tab.Screen
        name="Offers"
        component={OffersScreen}
        options={{ title: 'Offers' }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

// Loading component
const LoadingScreen: React.FC = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={Colors.primary} />
    <DarkTitle level={3} textAlign="center" style={styles.loadingText}>
      Checking your salon...
    </DarkTitle>
  </View>
);

// Salon Owner Stack Navigator (includes salon registration flow)
export const SalonOwnerNavigator: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [navigationTarget, setNavigationTarget] = useState<'register' | 'selection' | 'main'>('register');

  useEffect(() => {
    const checkForExistingSalons = async () => {
      try {
        console.log('🔍 SalonOwnerNavigator: Checking for existing salons...');

        const accountId = getAccountId();
        const response = await salonService.getSalonsByAccount(accountId);

        if ( response.data && response.data.length > 0) {
          if (response.data.length === 1) {
            console.log('✅ SalonOwnerNavigator: Found 1 salon, going to main app');
            setNavigationTarget('main');
          } else {
            console.log('🏢 SalonOwnerNavigator: Found', response.data.length, 'salons, showing selection');
            setNavigationTarget('selection');
          }
        } else {
          console.log('📝 SalonOwnerNavigator: No salon found, showing registration');
          setNavigationTarget('register');
        }
      } catch (error: any) {
        console.error('❌ SalonOwnerNavigator: Error checking for salons:', error);
        // On error, default to registration (safer)
        setNavigationTarget('register');
      } finally {
        setLoading(false);
      }
    };

    checkForExistingSalons();
  }, []);

  // Show loading while checking
  if (loading) {
    return <LoadingScreen />;
  }

  // Determine initial route based on salon count
  const getInitialRoute = () => {
    switch (navigationTarget) {
      case 'main':
        return 'SalonOwnerMain';
      case 'selection':
        return 'SalonSelection';
      case 'register':
      default:
        return 'SalonRegister';
    }
  };

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
      initialRouteName={getInitialRoute()}
    >
      {/* Salon Registration Screen */}
      <Stack.Screen
        name="SalonRegister"
        component={SalonRegisterScreen}
        options={{
          animationTypeForReplace: 'push',
        }}
      />

      {/* Salon Selection Screen */}
      <Stack.Screen
        name="SalonSelection"
        component={SalonSelectionScreen}
        options={{
          animationTypeForReplace: 'push',
        }}
      />

      {/* Salon Owner Main App (Tab Navigator) */}
      <Stack.Screen
        name="SalonOwnerMain"
        component={SalonOwnerTabNavigator}
        options={{
          animationTypeForReplace: 'push',
        }}
      />

      {/* Edit Salon Screen */}
      <Stack.Screen
        name="EditSalon"
        component={EditSalonScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      {/* Add Service Screen */}
      <Stack.Screen
        name="AddService"
        component={AddServiceScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      {/* Edit Service Screen */}
      <Stack.Screen
        name="EditService"
        component={EditServiceScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      {/* Service Details Screen */}
      <Stack.Screen
        name="ServiceDetails"
        component={ServiceDetailsScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      {/* Staff Screen */}
      <Stack.Screen
        name="Staff"
        component={StaffScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      {/* Add Staff Screen */}
      <Stack.Screen
        name="AddStaff"
        component={AddStaffScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

       {/* Offer Screens */}
      <Stack.Screen
        name="AddOffer"
        component={AddOfferScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      <Stack.Screen
        name="EditOffer"
        component={EditOfferScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      <Stack.Screen
        name="ViewOfferDetails"
        component={ViewOfferDetailsScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />

      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          presentation: 'modal',
          animationTypeForReplace: 'push',
        }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 20,
    color: Colors.textSecondary,
  },
});
