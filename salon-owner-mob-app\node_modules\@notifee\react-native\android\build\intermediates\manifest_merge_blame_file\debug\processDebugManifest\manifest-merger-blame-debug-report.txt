1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.invertase.notifee" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <application>
7-->D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\src\main\AndroidManifest.xml:5:3-11:17
8        <provider
8-->D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\src\main\AndroidManifest.xml:6:5-10:34
9            android:name="io.invertase.notifee.NotifeeInitProvider"
9-->D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\src\main\AndroidManifest.xml:7:7-42
10            android:authorities="${applicationId}.notifee-init-provider"
10-->D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\src\main\AndroidManifest.xml:8:7-67
11            android:exported="false"
11-->D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\src\main\AndroidManifest.xml:9:7-31
12            android:initOrder="-100" />
12-->D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\src\main\AndroidManifest.xml:10:7-31
13    </application>
14
15</manifest>
