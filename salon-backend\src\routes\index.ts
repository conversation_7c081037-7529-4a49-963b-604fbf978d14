import express from 'express';
import authRoutes from './authRoutes';
import userRoutes from './userRoutes';
import roleRoutes from './roleRoutes';
import licenseRoutes from './licenseRoutes';
import accountRoutes from './accountRoutes';
import salonRoutes from './salonRoutes';
import serviceRoutes from './serviceRoutes';
import offerRoutes from './offerRoutes';
import notificationRoutes from './notificationRoutes';
import customerSalonAccessRoutes from './customerSalonAccessRoutes';


const router = express.Router();

// Mount routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/roles', roleRoutes);
router.use('/licenses', licenseRoutes);
router.use('/accounts', accountRoutes);
router.use('/salons', salonRoutes);
router.use('/services', serviceRoutes);
router.use('/offers', offerRoutes);
router.use('/notifications', notificationRoutes);
router.use('/customer-salon-access', customerSalonAccessRoutes);

export default router;