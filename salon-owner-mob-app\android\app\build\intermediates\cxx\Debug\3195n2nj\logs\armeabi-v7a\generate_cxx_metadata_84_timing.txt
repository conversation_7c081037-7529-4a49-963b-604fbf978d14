# C/C++ build system timings
generate_cxx_metadata
  [gap of 78ms]
  create-invalidation-state 71ms
  [gap of 24ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 187ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
  [gap of 32ms]
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 24ms
  [gap of 19ms]
generate_cxx_metadata completed in 57ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 34ms
  [gap of 16ms]
generate_cxx_metadata completed in 74ms

# C/C++ build system timings
generate_cxx_metadata 46ms

# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 758ms
    [gap of 31ms]
  generate-prefab-packages completed in 789ms
  execute-generate-process
    exec-configure 473ms
    [gap of 109ms]
  execute-generate-process completed in 582ms
  [gap of 66ms]
generate_cxx_metadata completed in 1453ms

