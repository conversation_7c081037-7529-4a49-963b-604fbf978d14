import { apiService } from './api';
import { ENDPOINTS } from '../config/api';

export interface CustomerOffer {
  id: string;
  title: string;
  description: string;
  discountType: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_SERVICE';
  discountValue: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  validFrom: string | Date;
  validUntil: string | Date;
  isActive: boolean;
  usageLimit?: number;
  usedCount: number;
  code: string;
  image?: string;
  
  // Service targeting
  applicableServices: string[];
  serviceCategories: string[];
  
  // Customer segmentation
  customerType: 'ALL' | 'NEW' | 'EXISTING' | 'VIP' | 'LOYALTY_TIER';
  loyaltyTierRequired?: string;
  minVisitsRequired?: number;
  maxUsagePerCustomer?: number;
  
  // Date and time controls
  validDays: string[];
  validTimeSlots: { startTime: string; endTime: string; }[];
  blackoutDates?: string[];
  
  // Duration and frequency
  offerDuration: {
    type: 'DAYS' | 'WEEKS' | 'MONTHS' | 'CUSTOM';
    value: number;
  };
  frequency: 'ONE_TIME' | 'RECURRING' | 'LIMITED_USES';
  cooldownPeriod?: number;
  
  // Advanced settings
  combinableWithOtherOffers: boolean;
  requiresAdvanceBooking?: number;
  autoApply: boolean;
  priority: number;
  
  // Customer-specific fields
  isEligible: boolean;
  remainingUses?: number;
  lastUsed?: string;
  canUseNow: boolean;
  eligibilityReason?: string;
  formattedDiscount: string;
  formattedValidPeriod: string;
}

export interface CustomerOfferFilters {
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_SERVICE';
  serviceCategory?: string;
  minDiscount?: number;
  maxDiscount?: number;
  validToday?: boolean;
  eligibleOnly?: boolean;
  sortBy?: 'discount' | 'validUntil' | 'popularity' | 'newest';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

class CustomerOfferService {
  /**
   * Get offers for a specific salon (customer view)
   */
  async getOffersBySalon(salonId: string, filters: CustomerOfferFilters = {}): Promise<{
    data: CustomerOffer[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('🔍 CustomerOfferService: Getting offers for salon:', salonId, filters);

      const queryParams = new URLSearchParams();

      // Map customer filters to backend filters
      if (filters.discountType) queryParams.append('discountType', filters.discountType);
      if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
      if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);
      if (filters.limit) queryParams.append('limit', filters.limit.toString());
      if (filters.offset) queryParams.append('offset', filters.offset.toString());

      // Only show active offers for customers
      queryParams.append('status', 'active');

      const endpoint = `${ENDPOINTS.OFFERS.BY_SALON(salonId)}?${queryParams}`;
      const response = await apiService.get(endpoint);

      console.log('✅ CustomerOfferService: Retrieved offers:', response.data?.data?.length || 0);

      // Handle the response structure and convert to CustomerOffer format
      const offers = response.data?.data || [];
      const customerOffers: CustomerOffer[] = offers.map((offer: any) => ({
        ...offer,
        isEligible: true, // Assume eligible for now
        canUseNow: this.isOfferValidNow(offer),
        formattedDiscount: this.formatDiscount(offer),
        formattedValidPeriod: this.formatValidPeriod(offer),
      }));

      const total = response.data?.total || customerOffers.length;

      return {
        data: customerOffers,
        total: total,
        page: Math.floor((filters.offset || 0) / (filters.limit || 20)) + 1,
        totalPages: Math.ceil(total / (filters.limit || 20))
      };
    } catch (error: any) {
      console.error('❌ CustomerOfferService: Error getting offers:', error);

      // Return empty result instead of throwing
      return { data: [], total: 0, page: 1, totalPages: 0 };
    }
  }

  /**
   * Get active offers for a salon (customer view)
   */
  async getActiveOffers(salonId: string, limit: number = 10): Promise<CustomerOffer[]> {
    try {
      console.log('🔍 CustomerOfferService: Getting active offers for salon:', salonId);

      const result = await this.getOffersBySalon(salonId, {
        limit,
        sortBy: 'validUntil',
        sortOrder: 'ASC'
      });

      console.log('✅ CustomerOfferService: Retrieved active offers:', result.data.length);
      return result.data;
    } catch (error: any) {
      console.error('❌ CustomerOfferService: Error getting active offers:', error);
      return [];
    }
  }

  /**
   * Get offer by ID (customer view)
   */
  async getOfferById(offerId: string): Promise<CustomerOffer> {
    try {
      console.log('🔍 CustomerOfferService: Getting offer by ID:', offerId);

      const response = await apiService.get(ENDPOINTS.OFFERS.BY_ID(offerId));
      const offer = response.data?.data;

      if (!offer) {
        throw new Error('Offer not found');
      }

      // Convert to CustomerOffer format
      const customerOffer: CustomerOffer = {
        ...offer,
        isEligible: true, // Assume eligible for now
        canUseNow: this.isOfferValidNow(offer),
        formattedDiscount: this.formatDiscount(offer),
        formattedValidPeriod: this.formatValidPeriod(offer),
      };

      console.log('✅ CustomerOfferService: Retrieved offer:', customerOffer.title);
      return customerOffer;
    } catch (error: any) {
      console.error('❌ CustomerOfferService: Error getting offer:', error);
      throw error;
    }
  }

  /**
   * Get eligible offers for customer
   */
  async getEligibleOffers(salonId: string, limit: number = 10): Promise<CustomerOffer[]> {
    try {
      console.log('🔍 CustomerOfferService: Getting eligible offers for salon:', salonId);

      const filters: CustomerOfferFilters = {
        eligibleOnly: true,
        sortBy: 'discount',
        sortOrder: 'DESC',
        limit,
      };

      const result = await this.getOffersBySalon(salonId, filters);
      
      console.log('✅ CustomerOfferService: Retrieved eligible offers:', result.data.length);
      return result.data;
    } catch (error: any) {
      console.error('❌ CustomerOfferService: Error getting eligible offers:', error);
      throw error;
    }
  }

  /**
   * Get offers by service category
   */
  async getOffersByCategory(salonId: string, category: string): Promise<CustomerOffer[]> {
    try {
      console.log('🔍 CustomerOfferService: Getting offers by category:', category);

      const filters: CustomerOfferFilters = {
        serviceCategory: category,
        eligibleOnly: true,
        sortBy: 'discount',
        sortOrder: 'DESC',
      };

      const result = await this.getOffersBySalon(salonId, filters);
      
      console.log('✅ CustomerOfferService: Retrieved category offers:', result.data.length);
      return result.data;
    } catch (error: any) {
      console.error('❌ CustomerOfferService: Error getting offers by category:', error);
      throw error;
    }
  }

  /**
   * Get trending offers (most used/popular)
   */
  async getTrendingOffers(salonId: string, limit: number = 5): Promise<CustomerOffer[]> {
    try {
      console.log('🔍 CustomerOfferService: Getting trending offers for salon:', salonId);

      const filters: CustomerOfferFilters = {
        sortBy: 'popularity',
        sortOrder: 'DESC',
        limit,
      };

      const result = await this.getOffersBySalon(salonId, filters);
      
      console.log('✅ CustomerOfferService: Retrieved trending offers:', result.data.length);
      return result.data;
    } catch (error: any) {
      console.error('❌ CustomerOfferService: Error getting trending offers:', error);
      throw error;
    }
  }

  /**
   * Check if offer is valid for current time/day
   */
  isOfferValidNow(offer: CustomerOffer): boolean {
    try {
      const now = new Date();
      const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toUpperCase();
      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

      // Check if offer is active
      if (!offer.isActive) return false;

      // Check date range
      const validFrom = new Date(offer.validFrom);
      const validUntil = new Date(offer.validUntil);
      if (now < validFrom || now > validUntil) return false;

      // Check valid days
      if (!offer.validDays.includes('ALL') && !offer.validDays.includes(currentDay)) {
        return false;
      }

      // Check time slots
      if (offer.validTimeSlots.length > 0) {
        const isValidTime = offer.validTimeSlots.some(slot => 
          currentTime >= slot.startTime && currentTime <= slot.endTime
        );
        if (!isValidTime) return false;
      }

      // Check blackout dates
      if (offer.blackoutDates && offer.blackoutDates.length > 0) {
        const todayStr = now.toISOString().split('T')[0];
        const isBlackedOut = offer.blackoutDates.some(date => 
          new Date(date).toISOString().split('T')[0] === todayStr
        );
        if (isBlackedOut) return false;
      }

      return true;
    } catch (error) {
      console.error('❌ Error checking offer validity:', error);
      return false;
    }
  }

  /**
   * Format discount display
   */
  formatDiscount(offer: CustomerOffer): string {
    switch (offer.discountType) {
      case 'PERCENTAGE':
        return `${offer.discountValue}% OFF`;
      case 'FIXED_AMOUNT':
        return `$${offer.discountValue} OFF`;
      case 'BUY_ONE_GET_ONE':
        return 'BUY 1 GET 1';
      case 'FREE_SERVICE':
        return 'FREE SERVICE';
      default:
        return `${offer.discountValue}% OFF`;
    }
  }

  /**
   * Format valid period display
   */
  formatValidPeriod(offer: CustomerOffer): string {
    try {
      const validFrom = new Date(offer.validFrom);
      const validUntil = new Date(offer.validUntil);
      
      const fromStr = validFrom.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
      const untilStr = validUntil.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
      
      return `${fromStr} - ${untilStr}`;
    } catch (error) {
      return 'Valid period';
    }
  }
}

export const customerOfferService = new CustomerOfferService();
