1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.saloon_app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" /> <!-- Push notification permissions -->
11-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:5:5-68
12-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:5:22-65
13    <uses-permission android:name="android.permission.VIBRATE" />
13-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:6:5-66
13-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:6:22-63
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- For Android 13+ (API level 33) -->
14-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:7:5-81
14-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:7:22-78
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
16-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
17    <!--
18    This manifest file is used only by Gradle to configure debug-only capabilities
19    for React Native Apps.
20    -->
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- Required by older versions of Google Play services to create IID tokens -->
21-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
21-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:22-75
22    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
22-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
22-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:5-77
23-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:22-74
24    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
25-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
25-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
27-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
27-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
28
29    <permission
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
30        android:name="com.saloon_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.saloon_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
34    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
34-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:16:5-79
34-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:16:22-76
35    <uses-permission
35-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:17:5-19:38
36        android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
36-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:18:9-73
37        android:maxSdkVersion="30" /> <!-- For Xiaomi devices to enable heads-up notifications as default (https://github.com/invertase/notifee/issues/296) -->
37-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:19:9-35
38    <uses-permission
38-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:21:5-23:38
39        android:name="android.permission.ACCESS_NOTIFICATION_POLICY"
39-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:22:9-69
40        android:minSdkVersion="23" />
40-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:23:9-35
41
42    <application
42-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:11:5-31:19
43        android:name="com.saloon_app.MainApplication"
43-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:12:7-38
44        android:allowBackup="false"
44-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:16:7-34
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:icon="@mipmap/ic_launcher"
48-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:14:7-41
49        android:label="@string/app_name"
49-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:13:7-39
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:15:7-52
51        android:supportsRtl="true"
51-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:18:7-33
52        android:theme="@style/AppTheme"
52-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:17:7-38
53        android:usesCleartextTraffic="true" >
53-->D:\sgic-product\salon-owner-mob-app\android\app\src\debug\AndroidManifest.xml:6:9-44
54        <activity
54-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:19:7-30:18
55            android:name="com.saloon_app.MainActivity"
55-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:20:9-37
56            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
56-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:22:9-118
57            android:exported="true"
57-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:25:9-32
58            android:label="@string/app_name"
58-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:21:9-41
59            android:launchMode="singleTask"
59-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:23:9-40
60            android:windowSoftInputMode="adjustResize" >
60-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:24:9-51
61            <intent-filter>
61-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:26:9-29:25
62                <action android:name="android.intent.action.MAIN" />
62-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:27:13-65
62-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:27:21-62
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:28:13-73
64-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:28:23-70
65            </intent-filter>
66        </activity>
67
68        <provider
68-->[:notifee_react-native] D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:40
69            android:name="io.invertase.notifee.NotifeeInitProvider"
69-->[:notifee_react-native] D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
70            android:authorities="com.saloon_app.notifee-init-provider"
70-->[:notifee_react-native] D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-73
71            android:exported="false"
71-->[:notifee_react-native] D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
72            android:initOrder="-100" />
72-->[:notifee_react-native] D:\sgic-product\salon-owner-mob-app\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
73
74        <service
74-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
75            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
75-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
76            android:exported="false" />
76-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
77        <service
77-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
78            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
78-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
79            android:exported="false" >
79-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
80            <intent-filter>
80-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
81                <action android:name="com.google.firebase.MESSAGING_EVENT" />
81-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
81-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
82            </intent-filter>
83        </service>
84
85        <receiver
85-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
86            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
86-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
87            android:exported="true"
87-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
88            android:permission="com.google.android.c2dm.permission.SEND" >
88-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
89            <intent-filter>
89-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
90                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
90-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
90-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
91            </intent-filter>
92        </receiver>
93
94        <meta-data
94-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:36
95            android:name="firebase_messaging_auto_init_enabled"
95-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-64
96            android:value="true" />
96-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-33
97        <meta-data
97-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:32
98            android:name="com.google.firebase.messaging.default_notification_channel_id"
98-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-89
99            android:value="" />
99-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-29
100        <meta-data
100-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:47
101            android:name="com.google.firebase.messaging.default_notification_color"
101-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-84
102            android:resource="@color/white" />
102-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-44
103        <meta-data
103-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
104            android:name="app_data_collection_default_enabled"
104-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
105            android:value="true" />
105-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
106
107        <service
107-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
108            android:name="com.google.firebase.components.ComponentDiscoveryService"
108-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
109            android:directBootAware="true"
109-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
110            android:exported="false" >
110-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
111            <meta-data
111-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
112                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
112-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
114            <meta-data
114-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
115                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
115-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
117            <meta-data
117-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
118                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
118-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
120            <meta-data
120-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
121                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
121-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
123            <meta-data
123-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
124                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
124-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
126            <meta-data
126-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
127                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
127-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
129            <meta-data
129-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
130                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
130-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
132            <meta-data
132-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
133                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
133-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
135            <meta-data
135-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
136                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
136-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
138        </service>
139
140        <provider
140-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
141            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
141-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
142            android:authorities="com.saloon_app.reactnativefirebaseappinitprovider"
142-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
143            android:exported="false"
143-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
144            android:initOrder="99" />
144-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
145        <provider
145-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
146            android:name="com.imagepicker.ImagePickerProvider"
146-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
147            android:authorities="com.saloon_app.imagepickerprovider"
147-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
148            android:exported="false"
148-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
149            android:grantUriPermissions="true" >
149-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
150            <meta-data
150-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
151                android:name="android.support.FILE_PROVIDER_PATHS"
151-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
152                android:resource="@xml/imagepicker_provider_paths" />
152-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
153        </provider>
154
155        <activity
155-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
156            android:name="com.facebook.react.devsupport.DevSettingsActivity"
156-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
157            android:exported="false" />
157-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
158
159        <receiver
159-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
160            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
160-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
161            android:exported="true"
161-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
162            android:permission="com.google.android.c2dm.permission.SEND" >
162-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
163            <intent-filter>
163-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
164                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
164-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
164-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
165            </intent-filter>
166
167            <meta-data
167-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
168                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
168-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
169                android:value="true" />
169-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
170        </receiver>
171        <!--
172             FirebaseMessagingService performs security checks at runtime,
173             but set to not exported to explicitly avoid allowing another app to call it.
174        -->
175        <service
175-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
176            android:name="com.google.firebase.messaging.FirebaseMessagingService"
176-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
177            android:directBootAware="true"
177-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
178            android:exported="false" >
178-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
179            <intent-filter android:priority="-500" >
179-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
180                <action android:name="com.google.firebase.MESSAGING_EVENT" />
180-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
180-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
181            </intent-filter>
182        </service>
183
184        <provider
184-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:29:9-37:20
185            android:name="androidx.startup.InitializationProvider"
185-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:30:13-67
186            android:authorities="com.saloon_app.androidx-startup"
186-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:31:13-68
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:32:13-37
188            <meta-data
188-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:34:13-36:52
189                android:name="androidx.work.WorkManagerInitializer"
189-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:35:17-68
190                android:value="androidx.startup" />
190-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:36:17-49
191            <meta-data
191-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
192                android:name="androidx.emoji2.text.EmojiCompatInitializer"
192-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
193                android:value="androidx.startup" />
193-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
194            <meta-data
194-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
195                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
195-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
196                android:value="androidx.startup" />
196-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
197            <meta-data
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
198                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
199                android:value="androidx.startup" />
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
200        </provider>
201
202        <service
202-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:39:9-45:35
203            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
203-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:40:13-88
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:41:13-44
205            android:enabled="@bool/enable_system_alarm_service_default"
205-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:42:13-72
206            android:exported="false" />
206-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:43:13-37
207        <service
207-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:46:9-52:35
208            android:name="androidx.work.impl.background.systemjob.SystemJobService"
208-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:47:13-84
209            android:directBootAware="false"
209-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:48:13-44
210            android:enabled="@bool/enable_system_job_service_default"
210-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:49:13-70
211            android:exported="true"
211-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:50:13-36
212            android:permission="android.permission.BIND_JOB_SERVICE" />
212-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:51:13-69
213        <service
213-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:53:9-59:35
214            android:name="androidx.work.impl.foreground.SystemForegroundService"
214-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:54:13-81
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:55:13-44
216            android:enabled="@bool/enable_system_foreground_service_default"
216-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:56:13-77
217            android:exported="false" />
217-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:57:13-37
218
219        <receiver
219-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:61:9-66:35
220            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
220-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:62:13-88
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:63:13-44
222            android:enabled="true"
222-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:64:13-35
223            android:exported="false" />
223-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:65:13-37
224        <receiver
224-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:67:9-77:20
225            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
225-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:68:13-106
226            android:directBootAware="false"
226-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:69:13-44
227            android:enabled="false"
227-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:70:13-36
228            android:exported="false" >
228-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:71:13-37
229            <intent-filter>
229-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:73:13-76:29
230                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
230-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:17-87
230-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:25-84
231                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
231-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:17-90
231-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:25-87
232            </intent-filter>
233        </receiver>
234        <receiver
234-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:78:9-88:20
235            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
235-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:79:13-104
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:80:13-44
237            android:enabled="false"
237-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:81:13-36
238            android:exported="false" >
238-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:82:13-37
239            <intent-filter>
239-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:84:13-87:29
240                <action android:name="android.intent.action.BATTERY_OKAY" />
240-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:17-77
240-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:25-74
241                <action android:name="android.intent.action.BATTERY_LOW" />
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:17-76
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:25-73
242            </intent-filter>
243        </receiver>
244        <receiver
244-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:89:9-99:20
245            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
245-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:90:13-104
246            android:directBootAware="false"
246-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:91:13-44
247            android:enabled="false"
247-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:92:13-36
248            android:exported="false" >
248-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:93:13-37
249            <intent-filter>
249-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:95:13-98:29
250                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
250-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:17-83
250-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:25-80
251                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:17-82
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:25-79
252            </intent-filter>
253        </receiver>
254        <receiver
254-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:100:9-109:20
255            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
255-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:101:13-103
256            android:directBootAware="false"
256-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:102:13-44
257            android:enabled="false"
257-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:103:13-36
258            android:exported="false" >
258-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:104:13-37
259            <intent-filter>
259-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:106:13-108:29
260                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
260-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:17-79
260-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:25-76
261            </intent-filter>
262        </receiver>
263        <receiver
263-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:110:9-121:20
264            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
264-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:111:13-88
265            android:directBootAware="false"
265-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:112:13-44
266            android:enabled="false"
266-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:113:13-36
267            android:exported="false" >
267-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:114:13-37
268            <intent-filter>
268-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:116:13-120:29
269                <action android:name="android.intent.action.BOOT_COMPLETED" />
269-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
269-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
270                <action android:name="android.intent.action.TIME_SET" />
270-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:17-73
270-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:25-70
271                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
271-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:17-81
271-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:25-78
272            </intent-filter>
273        </receiver>
274        <receiver
274-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:122:9-131:20
275            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
275-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:123:13-99
276            android:directBootAware="false"
276-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:124:13-44
277            android:enabled="@bool/enable_system_alarm_service_default"
277-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:125:13-72
278            android:exported="false" >
278-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:126:13-37
279            <intent-filter>
279-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:128:13-130:29
280                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:17-98
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:25-95
281            </intent-filter>
282        </receiver>
283        <receiver
283-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:132:9-142:20
284            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
284-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:133:13-78
285            android:directBootAware="false"
285-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:134:13-44
286            android:enabled="true"
286-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:135:13-35
287            android:exported="true"
287-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:136:13-36
288            android:permission="android.permission.DUMP" >
288-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:137:13-57
289            <intent-filter>
289-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:139:13-141:29
290                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
290-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:17-88
290-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:25-85
291            </intent-filter>
292        </receiver>
293
294        <activity
294-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
295            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
295-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
296            android:excludeFromRecents="true"
296-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
297            android:exported="false"
297-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
298            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
298-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
299        <!--
300            Service handling Google Sign-In user revocation. For apps that do not integrate with
301            Google Sign-In, this service will never be started.
302        -->
303        <service
303-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
304            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
304-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
305            android:exported="true"
305-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
306            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
306-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
307            android:visibleToInstantApps="true" />
307-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
308
309        <provider
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
310            android:name="com.google.firebase.provider.FirebaseInitProvider"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
311            android:authorities="com.saloon_app.firebaseinitprovider"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
312            android:directBootAware="true"
312-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
313            android:exported="false"
313-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
314            android:initOrder="100" />
314-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
315
316        <receiver
316-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
317            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
317-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
318            android:enabled="true"
318-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
319            android:exported="false" >
319-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
320        </receiver>
321
322        <service
322-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
323            android:name="com.google.android.gms.measurement.AppMeasurementService"
323-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
324            android:enabled="true"
324-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
325            android:exported="false" />
325-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
326        <service
326-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
327            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
327-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
328            android:enabled="true"
328-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
329            android:exported="false"
329-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
330            android:permission="android.permission.BIND_JOB_SERVICE" />
330-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
331
332        <activity
332-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
333            android:name="com.google.android.gms.common.api.GoogleApiActivity"
333-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
334            android:exported="false"
334-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
335            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
335-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
336
337        <uses-library
337-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
338            android:name="android.ext.adservices"
338-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
339            android:required="false" />
339-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
340
341        <meta-data
341-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
342            android:name="com.google.android.gms.version"
342-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
343            android:value="@integer/google_play_services_version" />
343-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
344
345        <service
345-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a644274a191d4cce76017f46d3f4eca\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
346            android:name="androidx.room.MultiInstanceInvalidationService"
346-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a644274a191d4cce76017f46d3f4eca\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
347            android:directBootAware="true"
347-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a644274a191d4cce76017f46d3f4eca\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
348            android:exported="false" />
348-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a644274a191d4cce76017f46d3f4eca\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
349
350        <receiver
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
351            android:name="androidx.profileinstaller.ProfileInstallReceiver"
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
352            android:directBootAware="false"
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
353            android:enabled="true"
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
354            android:exported="true"
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
355            android:permission="android.permission.DUMP" >
355-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
356            <intent-filter>
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
357                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
358            </intent-filter>
359            <intent-filter>
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
360                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
361            </intent-filter>
362            <intent-filter>
362-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
363                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
364            </intent-filter>
365            <intent-filter>
365-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
366                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
367            </intent-filter>
368        </receiver>
369
370        <service
370-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
371            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
371-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
372            android:exported="false" >
372-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
373            <meta-data
373-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
374                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
374-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
375                android:value="cct" />
375-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
376        </service>
377        <service
377-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
378            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
378-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
379            android:exported="false"
379-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
380            android:permission="android.permission.BIND_JOB_SERVICE" >
380-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
381        </service>
382
383        <receiver
383-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
384            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
384-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
385            android:exported="false" /> <!-- Receiver Service -->
385-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
386        <service
386-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:28:9-30:40
387            android:name="app.notifee.core.ReceiverService"
387-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:29:13-60
388            android:exported="false" />
388-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:30:13-37
389
390        <activity
390-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:32:9-38:75
391            android:name="app.notifee.core.NotificationReceiverActivity"
391-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:33:13-73
392            android:excludeFromRecents="true"
392-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:34:13-46
393            android:exported="true"
393-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:35:13-36
394            android:noHistory="true"
394-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:36:13-37
395            android:taskAffinity=""
395-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:37:13-36
396            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Foreground Service -->
396-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:38:13-72
397        <service
397-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:41:9-44:60
398            android:name="app.notifee.core.ForegroundService"
398-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:42:13-62
399            android:exported="false"
399-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:43:13-37
400            android:foregroundServiceType="shortService" />
400-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:44:13-57
401
402        <receiver
402-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:46:9-54:20
403            android:name="app.notifee.core.RebootBroadcastReceiver"
403-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:47:13-68
404            android:exported="false" >
404-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:48:13-37
405            <intent-filter>
405-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:49:13-53:29
406                <action android:name="android.intent.action.BOOT_COMPLETED" />
406-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
406-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
407                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
407-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:51:17-82
407-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:51:25-79
408                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
408-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:52:17-82
408-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:52:25-79
409            </intent-filter>
410        </receiver>
411        <receiver
411-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:55:9-61:20
412            android:name="app.notifee.core.AlarmPermissionBroadcastReceiver"
412-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:56:13-77
413            android:exported="true" >
413-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:57:13-36
414            <intent-filter>
414-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:58:13-60:29
415                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
415-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:59:17-107
415-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:59:25-104
416            </intent-filter>
417        </receiver>
418        <receiver
418-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:62:9-70:20
419            android:name="app.notifee.core.NotificationAlarmReceiver"
419-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:63:13-70
420            android:exported="false" >
420-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:64:13-37
421            <intent-filter>
421-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:49:13-53:29
422                <action android:name="android.intent.action.BOOT_COMPLETED" />
422-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
422-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a299ce3dee77ec4a92513163873ac57\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
423                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
423-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:51:17-82
423-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:51:25-79
424                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
424-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:52:17-82
424-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:52:25-79
425            </intent-filter>
426        </receiver> <!-- Broadcast Receiver -->
427        <receiver
427-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:73:9-81:20
428            android:name="app.notifee.core.BlockStateBroadcastReceiver"
428-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:74:13-72
429            android:exported="false" >
429-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:75:13-37
430            <intent-filter>
430-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:76:13-80:29
431                <action android:name="android.app.action.APP_BLOCK_STATE_CHANGED" />
431-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:77:17-85
431-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:77:25-82
432                <action android:name="android.app.action.NOTIFICATION_CHANNEL_BLOCK_STATE_CHANGED" />
432-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:78:17-102
432-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:78:25-99
433                <action android:name="android.app.action.NOTIFICATION_CHANNEL_GROUP_BLOCK_STATE_CHANGED" />
433-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:79:17-108
433-->[app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\3685a4a4dc40073abab77791b1d76d50\transformed\core-202108261754\AndroidManifest.xml:79:25-105
434            </intent-filter>
435        </receiver>
436
437        <meta-data
437-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
438            android:name="com.facebook.soloader.enabled"
438-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
439            android:value="false" />
439-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
440    </application>
441
442</manifest>
