1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.saloon_app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" /> <!-- Push notification permissions -->
11-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:5:5-68
12-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:5:22-65
13    <uses-permission android:name="android.permission.VIBRATE" />
13-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:6:5-66
13-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:6:22-63
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- For Android 13+ (API level 33) -->
14-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:7:5-81
14-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:7:22-78
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
16-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
17    <!--
18    This manifest file is used only by Gradle to configure debug-only capabilities
19    for React Native Apps.
20    -->
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- Required by older versions of Google Play services to create IID tokens -->
21-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
21-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:22-75
22    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
22-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
22-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
23    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
23-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
23-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
24    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
25-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
25-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
26    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
26-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
26-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
27
28    <permission
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
29        android:name="com.saloon_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.saloon_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
33    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
34    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
35    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
36    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
37    <!-- for Samsung -->
38    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
38-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
38-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
39    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
39-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
39-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
40    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
40-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
40-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
41    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
41-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
41-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
42    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
42-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
42-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
43    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
43-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
43-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
44    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
44-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
44-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
45    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
45-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
45-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
46    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
46-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
46-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
47    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
47-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
47-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
48    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
49    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
50    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
50-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
50-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
51    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
51-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
51-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
52    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
52-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
52-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
53    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
53-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
53-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\93a0383eca471a8a20b1beb3a1a3cdf6\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
54
55    <application
55-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:11:5-31:19
56        android:name="com.saloon_app.MainApplication"
56-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:12:7-38
57        android:allowBackup="false"
57-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:16:7-34
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
59        android:debuggable="true"
60        android:extractNativeLibs="false"
61        android:icon="@mipmap/ic_launcher"
61-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:14:7-41
62        android:label="@string/app_name"
62-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:13:7-39
63        android:roundIcon="@mipmap/ic_launcher_round"
63-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:15:7-52
64        android:supportsRtl="true"
64-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:18:7-33
65        android:theme="@style/AppTheme"
65-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:17:7-38
66        android:usesCleartextTraffic="true" >
66-->D:\sgic-product\salon-owner-mob-app\android\app\src\debug\AndroidManifest.xml:6:9-44
67        <activity
67-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:19:7-30:18
68            android:name="com.saloon_app.MainActivity"
68-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:20:9-37
69            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
69-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:22:9-118
70            android:exported="true"
70-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:25:9-32
71            android:label="@string/app_name"
71-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:21:9-41
72            android:launchMode="singleTask"
72-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:23:9-40
73            android:windowSoftInputMode="adjustResize" >
73-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:24:9-51
74            <intent-filter>
74-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:26:9-29:25
75                <action android:name="android.intent.action.MAIN" />
75-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:27:13-65
75-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:27:21-62
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:28:13-73
77-->D:\sgic-product\salon-owner-mob-app\android\app\src\main\AndroidManifest.xml:28:23-70
78            </intent-filter>
79        </activity>
80
81        <service
81-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
82            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
82-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
83            android:exported="false" />
83-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
84        <service
84-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
85            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
85-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
86            android:exported="false" >
86-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
87            <intent-filter>
87-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
88                <action android:name="com.google.firebase.MESSAGING_EVENT" />
88-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
88-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
89            </intent-filter>
90        </service>
91
92        <receiver
92-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
93            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
93-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
94            android:exported="true"
94-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
95            android:permission="com.google.android.c2dm.permission.SEND" >
95-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
96            <intent-filter>
96-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
97                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
97-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
97-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
98            </intent-filter>
99        </receiver>
100
101        <meta-data
101-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:36
102            android:name="firebase_messaging_auto_init_enabled"
102-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-64
103            android:value="true" />
103-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-33
104        <meta-data
104-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:32
105            android:name="com.google.firebase.messaging.default_notification_channel_id"
105-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-89
106            android:value="" />
106-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-29
107        <meta-data
107-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:47
108            android:name="com.google.firebase.messaging.default_notification_color"
108-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-84
109            android:resource="@color/white" />
109-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-44
110        <meta-data
110-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
111            android:name="app_data_collection_default_enabled"
111-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
112            android:value="true" />
112-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
113
114        <service
114-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
115            android:name="com.google.firebase.components.ComponentDiscoveryService"
115-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
116            android:directBootAware="true"
116-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
117            android:exported="false" >
117-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
118            <meta-data
118-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
119                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
119-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
121            <meta-data
121-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
122                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
122-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
124            <meta-data
124-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
125                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
125-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
127            <meta-data
127-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
128                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
128-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf4f384bf1fd8f285d4107d3d8ef7588\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
130            <meta-data
130-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
131                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
131-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
133            <meta-data
133-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
134                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
134-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ab52eeae13198c265894b3b8dc0e4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
136            <meta-data
136-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
137                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
137-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de127579c40774611313ddb57a3bd932\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
139            <meta-data
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
140                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
140-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
142            <meta-data
142-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
143                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
143-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3651116cc4ee1cbe9b24817c6e7f6a81\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
145        </service>
146
147        <provider
147-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
148            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
148-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
149            android:authorities="com.saloon_app.reactnativefirebaseappinitprovider"
149-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
150            android:exported="false"
150-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
151            android:initOrder="99" />
151-->[:react-native-firebase_app] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
152        <provider
152-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
153            android:name="com.imagepicker.ImagePickerProvider"
153-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
154            android:authorities="com.saloon_app.imagepickerprovider"
154-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
155            android:exported="false"
155-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
156            android:grantUriPermissions="true" >
156-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
157            <meta-data
157-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
158                android:name="android.support.FILE_PROVIDER_PATHS"
158-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
159                android:resource="@xml/imagepicker_provider_paths" />
159-->[:react-native-image-picker] D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
160        </provider>
161
162        <activity
162-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
163            android:name="com.facebook.react.devsupport.DevSettingsActivity"
163-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
164            android:exported="false" />
164-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
165
166        <receiver
166-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
167            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
167-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
168            android:exported="true"
168-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
169            android:permission="com.google.android.c2dm.permission.SEND" >
169-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
170            <intent-filter>
170-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
171                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
171-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
171-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
172            </intent-filter>
173
174            <meta-data
174-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
175                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
175-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
176                android:value="true" />
176-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
177        </receiver>
178        <!--
179             FirebaseMessagingService performs security checks at runtime,
180             but set to not exported to explicitly avoid allowing another app to call it.
181        -->
182        <service
182-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
183            android:name="com.google.firebase.messaging.FirebaseMessagingService"
183-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
184            android:directBootAware="true"
184-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
185            android:exported="false" >
185-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
186            <intent-filter android:priority="-500" >
186-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
187                <action android:name="com.google.firebase.MESSAGING_EVENT" />
187-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
187-->[:react-native-firebase_messaging] D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
188            </intent-filter>
189        </service>
190
191        <activity
191-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
192            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
192-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
193            android:excludeFromRecents="true"
193-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
194            android:exported="false"
194-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
195            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
195-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
196        <!--
197            Service handling Google Sign-In user revocation. For apps that do not integrate with
198            Google Sign-In, this service will never be started.
199        -->
200        <service
200-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
201            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
201-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
202            android:exported="true"
202-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
203            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
203-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
204            android:visibleToInstantApps="true" />
204-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
205
206        <provider
206-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
207            android:name="com.google.firebase.provider.FirebaseInitProvider"
207-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
208            android:authorities="com.saloon_app.firebaseinitprovider"
208-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
209            android:directBootAware="true"
209-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
210            android:exported="false"
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
211            android:initOrder="100" />
211-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
212
213        <receiver
213-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
214            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
214-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
215            android:enabled="true"
215-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
216            android:exported="false" >
216-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
217        </receiver>
218
219        <service
219-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
220            android:name="com.google.android.gms.measurement.AppMeasurementService"
220-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
221            android:enabled="true"
221-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
222            android:exported="false" />
222-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
223        <service
223-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
224            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
224-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
225            android:enabled="true"
225-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
226            android:exported="false"
226-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
227            android:permission="android.permission.BIND_JOB_SERVICE" />
227-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ab431ccb648f38cae082156aa3600c\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
228
229        <activity
229-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
230            android:name="com.google.android.gms.common.api.GoogleApiActivity"
230-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
231            android:exported="false"
231-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
232            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
232-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
233
234        <uses-library
234-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
235            android:name="android.ext.adservices"
235-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
236            android:required="false" />
236-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
237
238        <provider
238-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
239            android:name="androidx.startup.InitializationProvider"
239-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
240            android:authorities="com.saloon_app.androidx-startup"
240-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
241            android:exported="false" >
241-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
242            <meta-data
242-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
243                android:name="androidx.emoji2.text.EmojiCompatInitializer"
243-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
244                android:value="androidx.startup" />
244-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
245            <meta-data
245-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
246                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
246-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
247                android:value="androidx.startup" />
247-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
248            <meta-data
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
249                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
250                android:value="androidx.startup" />
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
251        </provider>
252
253        <meta-data
253-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
254            android:name="com.google.android.gms.version"
254-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
255            android:value="@integer/google_play_services_version" />
255-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
256
257        <receiver
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
258            android:name="androidx.profileinstaller.ProfileInstallReceiver"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
259            android:directBootAware="false"
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
260            android:enabled="true"
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
261            android:exported="true"
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
262            android:permission="android.permission.DUMP" >
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
264                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
267                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
268            </intent-filter>
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
270                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
271            </intent-filter>
272            <intent-filter>
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
273                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
274            </intent-filter>
275        </receiver>
276
277        <service
277-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
278            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
278-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
279            android:exported="false" >
279-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
280            <meta-data
280-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
281                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
281-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
282                android:value="cct" />
282-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b56c875c2526b9b55356cbfc850a82c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
283        </service>
284        <service
284-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
285            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
285-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
286            android:exported="false"
286-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
287            android:permission="android.permission.BIND_JOB_SERVICE" >
287-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
288        </service>
289
290        <receiver
290-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
291            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
291-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
292            android:exported="false" />
292-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\399dd0f6a7944e9aed2c917e7fd4e262\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
293
294        <meta-data
294-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
295            android:name="com.facebook.soloader.enabled"
295-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
296            android:value="false" />
296-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\583dec540f305a60c79ca1a1ad68882f\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
297    </application>
298
299</manifest>
