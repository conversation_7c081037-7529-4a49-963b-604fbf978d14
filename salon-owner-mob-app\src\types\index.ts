// Core Types aligned with backend
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  role: UserRole;
  profileImage?: string;
  isActive: boolean;
  accountId?: string; // Account ID for user association
  createdAt: string;
  updatedAt: string;
  token?: string;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  STAFF = 'STAFF',
  CUSTOMER = 'CUSTOMER'
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data:  T;
  message?: string;
  count?: number;
  token?: string;
}

export interface ApiError {
  success: false;
  message: string;
  statusCode: number;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber?: string;
  profileImage?: string; // File path for mobile
  accountId?: string; // Account ID for associating user with account
  salonIds?: string[]; // Array of salon IDs for customer registration
}

export interface AuthResponse {
  success: boolean;
  token: string;
  data: User;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  Dashboard: undefined;
  Profile: undefined;
  Settings: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  SignUp: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  CustomerSignUp: undefined;
};

export type MainStackParamList = {
  Landing: undefined;
  SalonOwner: undefined;
  Customer: undefined;
};

export type SalonOwnerStackParamList = {
  SalonRegister: undefined;
  SalonSelection: undefined;
  SalonOwnerMain: undefined;
  EditSalon: undefined;
  AddService: undefined;
  EditService: { serviceId: string };
  ServiceDetails: { serviceId: string };
  Staff: undefined;
  AddStaff: undefined;
  StaffDetails: { staffId: string };
  EditStaff: { staffId: string };
  Offers: undefined;
  AddOffer: undefined;
  EditOffer: { offerId: string };
  ViewOfferDetails: { offerId: string };
  Notifications: undefined;
};

export type SalonOwnerTabParamList = {
  Home: undefined;
  Bookings: undefined;
  Services: undefined;
  Offers: undefined;
  Profile: undefined;
};

export type CustomerTabParamList = {
  Home: undefined;
  Search: undefined;
  Bookings: undefined;
  Favorites: undefined;
  Profile: undefined;
};

export type CustomerStackParamList = {
  CustomerSalonSelection: undefined;
  CustomerHome: undefined;
  Services: { category?: string; search?: string };
  ServiceDetails: { serviceId: string };
  OfferDetails: { offerId: string };
  Search: { query?: string };
};

export type DashboardStackParamList = {
  Dashboard: undefined;
  Profile: undefined;
  Settings: undefined;
  UserManagement: undefined;
  RoleManagement: undefined;
};

// Form Types
export interface FormField {
  value: string;
  error: string | null;
  touched: boolean;
}

export interface LoginForm {
  email: FormField;
  password: FormField;
}

export interface RegisterForm {
  firstName: FormField;
  lastName: FormField;
  email: FormField;
  password: FormField;
  confirmPassword: FormField;
  phoneNumber: FormField;
}

// Sign up form type (for the new signup screen) - Updated to match backend
export interface SignUpForm {
  firstName: FormField;
  lastName: FormField;
  email: FormField;
  password: FormField;
  phoneNumber: FormField;
}

// Sign up data type (for API calls) - Updated to match backend
export interface SignUpData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber?: string;
  accountId?: string; // Account ID for associating user with account
  salonIds?: string[]; // Array of salon IDs for customer registration
}

// App State Types
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface AppState {
  auth: AuthState;
  loading: boolean;
  error: string | null;
}

// Salon Types for Registration
export interface AvailableSalon {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  address?: string;
  city?: string;
  phone?: string;
  email?: string;
  type?: string;
  status?: string;
}
