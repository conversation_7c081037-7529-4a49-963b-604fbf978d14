2025-05-29 17:03:06:36 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:06:36 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:07:37 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:07:37 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:07:37 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:07:37 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:08:38 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:08:38 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:08:38 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:09:39 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:09:39 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:10:310 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:10:310 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:11:311 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:12:312 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:13:313 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:13:313 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:14:314 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:15:315 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:16:316 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:17:317 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:18:318 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:19:319 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:21:321 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:22:322 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:23:323 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:24:324 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:26:326 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:03:27:327 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:09:99 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:10:910 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:10:910 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:10:910 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:10:910 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:10:910 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:11:911 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:11:911 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:11:911 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:12:912 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:12:912 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:13:913 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:14:914 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:14:914 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:15:915 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:16:916 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:17:917 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 17:09:17:917 [31merror[39m: [31mRedis error: connect ECONNREFUSED 127.0.0.1:6379[39m
2025-05-29 19:03:35:335 [31merror[39m: [31mError | Invalid credentials | /api/v1/auth/login | POST | ::1[39m
2025-05-29 19:11:23:1123 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-29 19:15:20:1520 [31merror[39m: [31mError | Role ADMIN is not allowed to access this route | /api/v1/roles | GET | ::1[39m
2025-05-29 19:18:48:1848 [31merror[39m: [31mError | Role ADMIN is not allowed to access this route | /api/v1/roles | GET | ::1[39m
2025-05-29 19:19:04:194 [31merror[39m: [31mError | Role ADMIN is not allowed to access this route | /api/v1/roles | GET | ::1[39m
2025-05-29 19:27:42:2742 [31merror[39m: [31mError | Role ADMIN is not allowed to access this route | /api/v1/roles | GET | ::1[39m
2025-05-29 19:31:50:3150 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/roles | GET | ::1[39m
2025-05-29 22:52:19:5219 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 02:03:40:340 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 09:00:42:042 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-05-30 09:00:43:043 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-05-30 09:00:43:043 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-05-30 09:00:43:043 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-05-30 10:03:06:36 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 10:03:17:317 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-05-30 10:03:27:327 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-05-30 10:03:31:331 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-05-30 10:03:32:332 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:32:332 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:32:332 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:33:333 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:33:333 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:34:334 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:34:334 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:35:335 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:35:335 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:36:336 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:37:337 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:37:337 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:38:338 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:39:339 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:40:340 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:41:341 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 10:03:42:342 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-05-30 11:21:34:2134 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/roles | GET | ::ffff:************[39m
2025-05-30 14:15:09:159 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 14:20:43:2043 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 15:05:59:559 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 15:30:19:3019 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 15:57:34:5734 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 16:22:07:227 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 16:37:16:3716 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 16:49:59:4959 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 17:04:02:42 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 20:05:12:512 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-05-30 20:05:13:513 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-05-30 20:05:13:513 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:13:2413 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:14:2414 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:14:2414 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:14:2414 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:15:2415 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:15:2415 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:16:2416 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:17:2417 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:18:2418 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:18:2418 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 08:24:20:2420 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 08:24:21:2421 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 08:24:22:2422 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:04:554 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 09:55:04:554 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:04:554 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:04:554 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:05:555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:05:555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:05:555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:06:556 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 09:55:06:556 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 10:00:42:042 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/auth/me | GET | ::ffff:192.168.1.50[39m
2025-06-02 10:06:52:652 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 10:41:41:4141 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 11:28:37:2837 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 12:02:18:218 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 14:03:20:320 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 14:19:19:1919 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 14:34:28:3428 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 14:51:36:5136 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 15:05:09:59 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 15:57:00:570 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 15:57:00:570 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:00:570 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:00:570 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:00:570 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:00:570 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:01:571 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:01:571 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:02:572 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:02:572 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:13:5713 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-02 15:57:46:5746 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 15:57:46:5746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:46:5746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:46:5746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:46:5746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:46:5746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:47:5747 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:47:5747 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:48:5748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:48:5748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:48:5748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:49:5749 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:50:5750 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:50:5750 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:51:5751 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:52:5752 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:53:5753 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:53:5753 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:54:5754 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:55:5755 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:56:5756 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 15:57:57:5757 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 16:40:17:4017 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 16:40:18:4018 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 16:40:19:4019 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 16:40:20:4020 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 16:40:22:4022 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 16:43:27:4327 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 16:43:27:4327 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 19:59:33:5933 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 19:59:35:5935 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:35:5935 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:35:5935 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:36:5936 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:36:5936 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:37:5937 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:38:5938 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:38:5938 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:39:5939 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:40:5940 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:40:5940 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:41:5941 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:41:5941 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:42:5942 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:43:5943 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:44:5944 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:45:5945 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:46:5946 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:46:5946 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:48:5948 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:49:5949 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:50:5950 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:51:5951 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-02 19:59:52:5952 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 19:59:54:5954 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-02 20:31:07:317 [31merror[39m: [31mError | User already exists | /api/v1/auth/register | POST | ::ffff:***********[39m
2025-06-02 20:31:31:3131 [31merror[39m: [31mError | User already exists | /api/v1/auth/register | POST | ::ffff:***********[39m
2025-06-02 20:39:09:399 [31merror[39m: [31mError | User already exists | /api/v1/auth/register | POST | ::ffff:***********[39m
2025-06-02 20:46:37:4637 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 22:14:01:141 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/users | GET | ::1[39m
2025-06-02 22:14:27:1427 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-02 22:16:29:1629 [31merror[39m: [31mError | Role CUSTOMER is not allowed to access this route | /api/v1/users | GET | ::1[39m
2025-06-02 22:17:23:1723 [31merror[39m: [31mError | Role ADMIN is not allowed to access this route | /api/v1/users | GET | ::1[39m
2025-06-03 02:07:36:736 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 08:43:51:4351 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:21:01:211 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:21:01:211 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:21:11:2111 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-03 10:21:11:2111 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:11:2111 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:12:2112 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:12:2112 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:12:2112 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:13:2113 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:13:2113 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:14:2114 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:14:2114 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:15:2115 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:15:2115 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:16:2116 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:17:2117 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:18:2118 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:19:2119 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:20:2120 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:21:2121 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:22:2122 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:23:2123 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:24:2124 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:25:2125 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:26:2126 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:27:2127 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:29:2129 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:30:2130 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:31:2131 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:33:2133 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:34:2134 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:36:2136 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:38:2138 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:39:2139 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:41:2141 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:43:2143 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-03 10:21:52:2152 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:22:03:223 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-03 10:26:01:261 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:26:01:261 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:26:11:2611 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-03 10:29:05:295 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:29:05:295 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:05:295 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:05:295 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:05:295 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:06:296 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:06:296 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:06:296 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:29:07:297 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:40:30:4030 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:40:30:4030 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:44:55:4455 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:44:55:4455 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:44:55:4455 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:47:4747 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:47:47:4747 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:48:4748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:48:4748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:48:4748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:48:4748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:49:4749 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:49:4749 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:47:49:4749 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:12:4812 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 10:48:12:4812 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:12:4812 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:12:4812 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:12:4812 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:12:4812 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:13:4813 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:13:4813 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:13:4813 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:14:4814 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:14:4814 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:15:4815 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 10:48:16:4816 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 12:48:17:4817 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 14:48:17:4817 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 15:13:32:1332 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/licenses | POST | ::1[39m
2025-06-03 15:16:09:169 [31merror[39m: [31mError | Invalid credentials | /api/v1/auth/login | POST | ::1[39m
2025-06-03 15:17:37:1737 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 15:17:37:1737 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:17:37:1737 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:17:41:1741 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 15:17:41:1741 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:17:41:1741 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:17:41:1741 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:17:42:1742 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:17:42:1742 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:24:2724 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 15:27:24:2724 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:27:2727 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 15:27:27:2727 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:27:2727 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:27:2727 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:27:2727 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:28:2728 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:28:2728 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:28:2728 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:29:2729 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:29:2729 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:30:2730 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:30:2730 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:31:2731 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:32:2732 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:32:2732 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:33:2733 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:34:2734 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:35:2735 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:36:2736 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:37:2737 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:38:2738 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:27:39:2739 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 15:39:59:3959 [31merror[39m: [31mError | Invalid credentials | /api/v1/auth/login | POST | ::1[39m
2025-06-03 15:40:19:4019 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 17:14:59:1459 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 20:15:04:154 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 20:15:04:154 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-03 20:15:07:157 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 21:55:08:558 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-03 22:07:42:742 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 01:50:45:5045 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 08:41:30:4130 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 10:58:39:5839 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 11:06:05:65 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 11:06:05:65 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:05:65 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:05:65 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:06:66 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:06:66 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:06:66 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:06:66 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:07:67 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:07:67 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:08:68 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:08:68 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:09:69 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:06:20:620 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:06:30:630 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:06:41:641 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:06:52:652 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:07:03:73 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:07:14:714 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:07:15:715 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:16:716 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:17:717 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:18:718 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:19:719 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:20:720 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:22:722 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:23:723 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:24:724 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:26:726 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:27:727 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:29:729 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:30:730 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:32:732 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:34:734 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:35:735 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:37:737 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:39:739 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:41:741 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:43:743 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:45:745 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:47:747 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:49:749 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:51:751 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:53:753 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:55:755 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:57:757 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:07:59:759 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:01:81 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:03:83 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:05:85 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:07:87 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:09:89 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:11:811 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:13:813 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:08:25:825 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:08:37:837 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:08:46:846 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-04 11:08:48:848 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:08:50:850 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:09:02:92 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:09:14:914 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:09:26:926 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:09:28:928 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:09:40:940 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:09:52:952 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:10:04:104 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:10:16:1016 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:10:28:1028 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:10:40:1040 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:10:52:1052 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:11:04:114 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:11:16:1116 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:11:28:1128 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:11:40:1140 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:11:52:1152 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:12:05:125 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:12:17:1217 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:12:29:1229 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:12:41:1241 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:12:53:1253 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:13:05:135 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:13:17:1317 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:13:29:1329 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:13:41:1341 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:13:53:1353 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:14:05:145 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:14:17:1417 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:14:29:1429 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:14:41:1441 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:14:53:1453 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:14:55:1455 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:15:02:152 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-04 11:15:04:154 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:06:156 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:08:158 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:10:1510 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:12:1512 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:14:1514 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:16:1516 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:18:1518 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:20:1520 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:22:1522 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:24:1524 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:26:1526 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:28:1528 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:30:1530 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:33:1533 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:35:1535 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:37:1537 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:39:1539 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:41:1541 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:43:1543 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:45:1545 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:47:1547 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:49:1549 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:51:1551 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:53:1553 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:15:55:1555 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:15:57:1557 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:15:59:1559 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:01:161 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:03:163 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:05:165 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:07:167 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:19:1619 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:16:21:1621 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:23:1623 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:25:1625 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:27:1627 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:29:1629 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:31:1631 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:33:1633 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:35:1635 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:37:1637 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:39:1639 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:41:1641 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:43:1643 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:45:1645 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:47:1647 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:16:49:1649 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:51:1651 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:53:1653 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:55:1655 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:57:1657 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:16:59:1659 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:01:171 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:03:173 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:05:175 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:07:177 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:09:179 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:11:1711 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:13:1713 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:15:1715 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:17:1717 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:19:1719 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:21:1721 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:23:1723 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:25:1725 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:27:1727 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:29:1729 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:31:1731 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:33:1733 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:44:1744 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-04 11:17:46:1746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:48:1748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:50:1750 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:52:1752 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:54:1754 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:56:1756 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:17:58:1758 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:00:180 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:02:182 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:04:184 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:06:186 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:08:188 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:15:1815 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-04 11:18:17:1817 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:19:1819 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:21:1821 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:23:1823 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:18:35:1835 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:18:47:1847 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:18:59:1859 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:19:11:1911 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:19:23:1923 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-04 11:19:25:1925 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 11:19:37:1937 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:19:49:1949 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:20:01:201 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:20:13:2013 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:20:25:2025 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:20:37:2037 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:20:49:2049 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:21:01:211 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:21:13:2113 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:21:25:2125 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:21:37:2137 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:21:49:2149 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:22:01:221 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:22:13:2213 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:22:25:2225 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:22:37:2237 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:22:49:2249 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:23:01:231 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:23:13:2313 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:23:25:2325 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:23:37:2337 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:23:49:2349 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:24:01:241 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:24:13:2413 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:24:25:2425 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:24:38:2438 [31merror[39m: [31mRedis error: connect ETIMEDOUT[39m
2025-06-04 11:24:40:2440 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:42:2442 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:44:2444 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:46:2446 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:48:2448 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:50:2450 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:52:2452 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:54:2454 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:56:2456 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:24:58:2458 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:25:00:250 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-04 11:56:00:560 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 13:17:00:170 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 13:17:00:170 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:47:53:4753 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 14:50:27:5027 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 14:50:27:5027 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:27:5027 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:28:5028 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:28:5028 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:28:5028 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:28:5028 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:29:5029 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:29:5029 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:30:5030 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:30:5030 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:31:5031 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:31:5031 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:32:5032 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:33:5033 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:33:5033 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:34:5034 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:35:5035 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:36:5036 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:37:5037 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:38:5038 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:39:5039 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:40:5040 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:41:5041 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:43:5043 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:44:5044 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:45:5045 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:46:5046 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:48:5048 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:49:5049 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:51:5051 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:52:5052 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:54:5054 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:56:5056 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:57:5057 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:50:59:5059 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:01:511 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:03:513 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:05:515 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:07:517 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:09:519 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:11:5111 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:13:5113 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:15:5115 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:17:5117 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:19:5119 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:21:5121 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:23:5123 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:25:5125 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:27:5127 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:29:5129 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:31:5131 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:33:5133 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:35:5135 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:37:5137 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:39:5139 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:41:5141 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:43:5143 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:45:5145 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:47:5147 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:49:5149 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:51:5151 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:53:5153 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:55:5155 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:57:5157 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:51:59:5159 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:01:521 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:03:523 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:05:525 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:07:527 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:09:529 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:11:5211 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:13:5213 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:15:5215 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:17:5217 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:52:19:5219 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:55:45:5545 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 14:55:45:5545 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:55:48:5548 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-04 14:55:48:5548 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:55:49:5549 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:55:49:5549 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 14:55:49:5549 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-04 15:49:32:4932 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 16:21:25:2125 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 20:04:35:435 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 21:33:22:3322 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 22:38:42:3842 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 23:03:38:338 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-04 23:06:06:66 [31merror[39m: [31mSyntaxError | Unexpected token 's', "string" is not valid JSON | /api/v1/salons | POST | ::1[39m
2025-06-04 23:14:42:1442 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/accounts | POST | ::1[39m
2025-06-05 02:16:34:1634 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 02:16:34:1634 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 02:16:34:1634 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 02:16:34:1634 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 02:16:35:1635 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 02:16:35:1635 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 02:16:35:1635 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 02:16:36:1636 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-05 05:16:43:1643 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 08:48:36:4836 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 08:59:54:5954 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 11:00:14:014 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 14:03:30:330 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 16:45:16:4516 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/salons/account/2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::1[39m
2025-06-05 16:45:47:4547 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 20:33:24:3324 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-05 21:45:36:4536 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m248[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m248[0m       new ForeignKey({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~[0m[39m
[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m253[0m         name: 'FK_SERVICE_SALON',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m254[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m249[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'columnNames' does not exist in type '(type?: any) => ObjectType<unknown>'.[39m

[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m        ~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-05 21:48:50:4850 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m248[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m248[0m       new ForeignKey({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~[0m[39m
[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m253[0m         name: 'FK_SERVICE_SALON',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m254[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m249[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'columnNames' does not exist in type '(type?: any) => ObjectType<unknown>'.[39m

[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m        ~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-05 21:49:15:4915 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m248[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m248[0m       new ForeignKey({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~[0m[39m
[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m253[0m         name: 'FK_SERVICE_SALON',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m254[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m249[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'columnNames' does not exist in type '(type?: any) => ObjectType<unknown>'.[39m

[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m        ~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-05 21:49:46:4946 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m248[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m248[0m       new ForeignKey({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~[0m[39m
[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m253[0m         name: 'FK_SERVICE_SALON',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m254[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m249[0m:[93m9[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'columnNames' does not exist in type '(type?: any) => ObjectType<unknown>'.[39m

[31m[7m249[0m         columnNames: ['salonId'],[39m
[31m[7m   [0m [91m        ~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-05 21:53:17:5317 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m246[0m:[93m24[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m246[0m     const foreignKey = new ForeignKey({[39m
[31m[7m   [0m [91m                       ~~~~~~~~~~~~~~~~[0m[39m
[31m[7m247[0m       columnNames: ['salonId'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m251[0m       name: 'FK_SERVICE_SALON',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m252[0m     });[39m
[31m[7m   [0m [91m~~~~~~[0m[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m247[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'columnNames' does not exist in type '(type?: any) => ObjectType<unknown>'.[39m

[31m[7m247[0m       columnNames: ['salonId'],[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-05 21:55:24:5524 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m58[0m:[93m11[0m - [91merror[0m[90m TS2304: [0mCannot find name 'Table'.[39m

[31m[7m58[0m       new Table({[39m
[31m[7m  [0m [91m          ~~~~~[0m[39m
[31m[96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m248[0m:[93m28[0m - [91merror[0m[90m TS2552: [0mCannot find name 'ForeignKey'. Did you mean 'foreignKey'?[39m

[31m[7m248[0m     const foreignKey = new ForeignKey({[39m
[31m[7m   [0m [91m                           ~~~~~~~~~~[0m[39m

[31m  [96msrc/migrations/*************-CreateServiceTable.ts[0m:[93m248[0m:[93m11[0m[39m
[31m    [7m248[0m     const foreignKey = new ForeignKey({[39m
[31m    [7m   [0m [96m          ~~~~~~~~~~[0m[39m
[31m    'foreignKey' is declared here.[39m
[31m[39m
2025-06-06 02:12:43:1243 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-06 08:46:04:464 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-06 08:46:04:464 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-06 14:03:19:319 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-06 15:44:59:4459 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:a2dc78db-b01e-0000-52cb-d6899c000000[39m
[31mTime:2025-06-06T10:15:00.6888455Z[39m
2025-06-06 15:46:18:4618 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:a2dd03ce-b01e-0000-4ccc-d6899c000000[39m
[31mTime:2025-06-06T10:16:18.8790996Z[39m
2025-06-06 16:06:45:645 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:ce0ebffd-a01e-000c-5cce-d61e94000000[39m
[31mTime:2025-06-06T10:36:46.3755089Z[39m
2025-06-06 16:14:43:1443 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:60f77bb2-501e-0045-30d0-d65c7f000000[39m
[31mTime:2025-06-06T10:44:44.1333601Z[39m
2025-06-06 16:43:31:4331 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:c6793786-201e-002d-77d4-d63aef000000[39m
[31mTime:2025-06-06T11:13:31.8293535Z[39m
2025-06-06 16:48:43:4843 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/services/f1bda00e-4c30-435f-9651-0165d7abe081 | GET | ::ffff:192.168.1.36[39m
2025-06-06 16:48:59:4859 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/services/f1bda00e-4c30-435f-9651-0165d7abe081 | GET | ::ffff:192.168.1.36[39m
2025-06-06 16:49:06:496 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/services/salon/4298c5a0-6eb7-4b06-a237-aa6e569e15f5?sortBy=sortOrder&sortOrder=ASC&category=HAIR_CUT | GET | ::ffff:192.168.1.36[39m
2025-06-06 16:49:18:4918 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/auth/logout | GET | ::ffff:192.168.1.36[39m
2025-06-06 16:50:12:5012 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:5d6d8145-e01e-0040-76d4-d68ea4000000[39m
[31mTime:2025-06-06T11:20:13.4399129Z[39m
2025-06-06 16:59:10:5910 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:bbeb1ca5-e01e-0022-4fd6-d64c83000000[39m
[31mTime:2025-06-06T11:29:11.6393154Z[39m
2025-06-06 17:00:31:031 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:bbebcafc-e01e-0022-35d6-d64c83000000[39m
[31mTime:2025-06-06T11:30:31.9759298Z[39m
2025-06-11 09:00:04:04 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/salons/account/2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::ffff:192.168.1.36[39m
2025-06-11 09:01:44:144 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 09:01:45:145 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:45:145 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:45:145 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:45:145 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:45:145 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:46:146 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:46:146 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:46:146 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:47:147 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:47:147 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:48:148 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:49:149 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:49:149 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:50:150 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:51:151 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:52:152 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:52:152 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:53:153 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:54:154 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:55:155 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:56:156 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:57:157 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:01:59:159 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:00:20 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:01:21 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:02:22 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:04:24 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:05:25 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:07:27 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:08:28 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:10:210 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:11:211 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:13:213 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:15:215 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:16:216 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:18:218 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:20:220 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:22:222 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:24:224 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:26:226 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:28:228 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:30:230 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:32:232 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:34:234 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:36:236 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:38:238 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:40:240 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:42:242 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:44:244 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:46:246 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:48:248 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:50:250 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:52:252 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:54:254 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:56:256 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:02:58:258 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:00:30 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:02:32 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:04:34 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:06:36 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:08:38 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:10:310 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:12:312 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:14:314 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:16:316 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:18:318 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:20:320 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:22:322 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:24:324 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:26:326 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:28:328 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:30:330 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:32:332 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:34:334 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:37:337 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:39:339 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:41:341 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:43:343 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:45:345 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:47:347 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:49:349 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:51:351 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:53:353 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:55:355 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:57:357 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:03:59:359 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:01:41 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:03:43 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:05:45 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:07:47 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:09:49 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:11:411 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:13:413 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:15:415 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:17:417 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:19:419 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:21:421 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:23:423 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:25:425 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:27:427 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:29:429 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:31:431 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:33:433 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:35:435 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:37:437 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:39:439 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:41:441 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:43:443 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:45:445 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:47:447 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:49:449 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:51:451 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:53:453 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:55:455 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:57:457 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:04:59:459 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:01:51 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:03:53 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:05:55 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:07:57 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:09:59 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:11:511 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:13:513 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:15:515 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:17:517 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:19:519 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:21:521 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:23:523 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:25:525 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:27:527 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:29:529 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:31:531 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:33:533 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:35:535 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:37:537 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:39:539 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:41:541 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:43:543 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:45:545 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:47:547 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:49:549 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:51:551 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:53:553 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:55:555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:57:557 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:05:59:559 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:01:61 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:03:63 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:05:65 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:08:68 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:10:610 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:12:612 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:14:614 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:16:616 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:18:618 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:20:620 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:22:622 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:24:624 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:26:626 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:28:628 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:30:630 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:32:632 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:34:634 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:36:636 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:38:638 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:40:640 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:42:642 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:44:644 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:46:646 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:48:648 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:50:650 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:52:652 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:54:654 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:56:656 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:06:58:658 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:00:70 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:02:72 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:04:74 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:06:76 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:08:78 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:10:710 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:12:712 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:14:714 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:16:716 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:18:718 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:20:720 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:22:722 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:24:724 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:26:726 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:28:728 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:30:730 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:32:732 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:34:734 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:36:736 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:38:738 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:40:740 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:42:742 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:44:744 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:46:746 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:48:748 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:50:750 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:52:752 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:54:754 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:56:756 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:07:58:758 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:00:80 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:02:82 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:04:84 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:06:86 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:08:88 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:10:810 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:12:812 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:14:814 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:16:816 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:18:818 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:20:820 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:22:822 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:24:824 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:26:826 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:28:828 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:30:830 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:32:832 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:34:834 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:36:836 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:38:838 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:40:840 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:43:843 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:45:845 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:47:847 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:49:849 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:51:851 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:53:853 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:55:855 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:57:857 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:08:59:859 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:01:91 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:03:93 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:05:95 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:07:97 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:09:99 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:11:911 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:13:913 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:15:915 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:17:917 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:19:919 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:21:921 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:23:923 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:25:925 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:27:927 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:29:929 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:31:931 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:33:933 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:35:935 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:37:937 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:39:939 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:41:941 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:43:943 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:45:945 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:47:947 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:49:949 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:51:951 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:53:953 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:55:955 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:57:957 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:09:59:959 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:01:101 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:03:103 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:05:105 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:07:107 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:09:109 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:11:1011 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:13:1013 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:15:1015 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:17:1017 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:19:1019 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:21:1021 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:23:1023 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:25:1025 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:27:1027 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:29:1029 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:31:1031 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:33:1033 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:10:35:1035 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:20:40:2040 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/services/salon/4298c5a0-6eb7-4b06-a237-aa6e569e15f5?sortBy=sortOrder&sortOrder=ASC | GET | ::ffff:192.168.1.36[39m
2025-06-11 09:39:51:3951 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 09:39:51:3951 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:51:3951 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:51:3951 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:52:3952 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:52:3952 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:52:3952 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:52:3952 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:53:3953 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:53:3953 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:54:3954 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:54:3954 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:55:3955 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:56:3956 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:56:3956 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:57:3957 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:58:3958 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:39:59:3959 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 09:40:00:400 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 10:34:37:3437 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/salons/account/2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::ffff:192.168.1.31[39m
2025-06-11 10:34:56:3456 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/auth/logout | GET | ::ffff:192.168.1.31[39m
2025-06-11 10:35:18:3518 [31merror[39m: [31mError | Invalid credentials | /api/v1/auth/login | POST | ::ffff:192.168.1.31[39m
2025-06-11 10:35:31:3531 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 10:38:20:3820 [31merror[39m: [31mError deleting blob: The specified blob does not exist.[39m
[31mRequestId:73cb10bc-201e-0012-3e8e-daf24c000000[39m
[31mTime:2025-06-11T05:08:24.3238634Z[39m
2025-06-11 10:57:16:5716 [31merror[39m: [31mError deleting blob: The specified container does not exist.[39m
[31mRequestId:daccd838-001e-0058-0591-da51c3000000[39m
[31mTime:2025-06-11T05:27:20.1726642Z[39m
2025-06-11 10:59:09:599 [31merror[39m: [31mError deleting blob: The specified container does not exist.[39m
[31mRequestId:847ecc18-901e-005a-7291-daef7b000000[39m
[31mTime:2025-06-11T05:29:13.1668368Z[39m
2025-06-11 13:55:45:5545 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 14:55:54:5554 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 14:55:54:5554 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:54:5554 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:54:5554 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:55:5555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:55:5555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:55:5555 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:56:5556 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:56:5556 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:57:5557 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 14:55:57:5557 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:05:45 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 15:04:05:45 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:12:412 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 15:04:12:412 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:12:412 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:13:413 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:13:413 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:13:413 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:13:413 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:14:414 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:14:414 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:15:415 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:15:415 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:16:416 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:16:416 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:17:417 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:18:418 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:18:418 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:19:419 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:20:420 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:21:421 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:22:422 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:23:423 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:24:424 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:25:425 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:26:426 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:28:428 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:29:429 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:30:430 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:39:439 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-11 15:04:40:440 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 15:04:41:441 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 20:08:56:856 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-11 20:08:56:856 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 20:08:57:857 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 20:08:57:857 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 20:08:58:858 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 20:08:58:858 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-11 21:33:20:3320 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m100[0m       new Index('IDX_user_device_tokens_userId_token', ['userId', 'token'], { isUnique: true })[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m11[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  Overload 1 of 7, '(name: string, fields: string[], options?: IndexOptions | undefined): ClassDecorator & PropertyDecorator', gave the following error.[39m
[31m    Object literal may only specify known properties, but 'isUnique' does not exist in type 'IndexOptions'. Did you mean to write 'unique'?[39m
[31m  Overload 2 of 7, '(name: string, fields: (object?: any) => any[] | { [key: string]: number; }, options?: IndexOptions | undefined): ClassDecorator & PropertyDecorator', gave the following error.[39m
[31m    Argument of type 'string[]' is not assignable to parameter of type '(object?: any) => any[] | { [key: string]: number; }'.[39m
[31m      Type 'string[]' provides no match for the signature '(object?: any): any[] | { [key: string]: number; }'.[39m

[31m[7m100[0m       new Index('IDX_user_device_tokens_userId_token', ['userId', 'token'], { isUnique: true })[39m
[31m[7m   [0m [91m          ~~~~~[0m[39m

[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m257[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m257[0m       new Index('IDX_notifications_userId_status', ['userId', 'status'])[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m262[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m262[0m       new Index('IDX_notifications_type_createdAt', ['type', 'createdAt'])[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-11 21:34:25:3425 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m100[0m       new Index('IDX_user_device_tokens_userId_token', ['userId', 'token'], { unique: true })[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m257[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m257[0m       new Index('IDX_notifications_userId_status', ['userId', 'status'])[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m262[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m262[0m       new Index('IDX_notifications_type_createdAt', ['type', 'createdAt'])[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[39m
2025-06-11 21:35:21:3521 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m100[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m103[0m         isUnique: true,[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m104[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m101[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m261[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m261[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m263[0m         columnNames: ['userId', 'status'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m264[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m262[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m269[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m269[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m271[0m         columnNames: ['type', 'createdAt'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m272[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m270[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[39m
2025-06-11 21:38:18:3818 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m100[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m103[0m         isUnique: true,[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m104[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m101[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m261[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m261[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m263[0m         columnNames: ['userId', 'status'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m264[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m262[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m269[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m269[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m271[0m         columnNames: ['type', 'createdAt'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m272[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m270[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[39m
2025-06-11 21:39:40:3940 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m100[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m103[0m         isUnique: true,[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m104[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m101[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m261[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m261[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m263[0m         columnNames: ['userId', 'status'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m264[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m262[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m269[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m269[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m271[0m         columnNames: ['type', 'createdAt'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m272[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m270[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[39m
2025-06-11 21:41:06:416 [31merror[39m: [31mError during Data Source initialization: ⨯ Unable to compile TypeScript:[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m100[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m100[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m...[0m [39m
[31m[7m103[0m         isUnique: true,[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m104[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m101[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m101[0m         name: 'IDX_user_device_tokens_userId_token',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m261[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m261[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m263[0m         columnNames: ['userId', 'status'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m264[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m262[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m262[0m         name: 'IDX_notifications_userId_status',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m269[0m:[93m7[0m - [91merror[0m[90m TS7009: [0m'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.[39m

[31m[7m269[0m       new Index({[39m
[31m[7m   [0m [91m      ~~~~~~~~~~~[0m[39m
[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m271[0m         columnNames: ['type', 'createdAt'],[39m
[31m[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m
[31m[7m272[0m       })[39m
[31m[7m   [0m [91m~~~~~~~~[0m[39m
[31m[96msrc/migrations/1703000000000-AddNotificationEntities.ts[0m:[93m270[0m:[93m9[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.[39m
[31m  The last overload gave the following error.[39m
[31m    Object literal may only specify known properties, and 'name' does not exist in type '(object?: any) => any[] | { [key: string]: number; }'.[39m

[31m[7m270[0m         name: 'IDX_notifications_type_createdAt',[39m
[31m[7m   [0m [91m        ~~~~[0m[39m

[31m  [96mnode_modules/typeorm/decorator/Index.d.ts[0m:[93m39[0m:[93m25[0m[39m
[31m    [7m39[0m export declare function Index(fields: (object?: any) => any[] | {[39m
[31m    [7m  [0m [96m                        ~~~~~[0m[39m
[31m    The last overload is declared here.[39m
[31m[39m
2025-06-11 21:43:02:432 [31merror[39m: [31mError initializing Firebase Admin SDK: Firebase configuration not found. Please set FIREBASE_SERVICE_ACCOUNT_KEY or FIREBASE_PROJECT_ID[39m
2025-06-11 21:45:14:4514 [31merror[39m: [31mError initializing Firebase Admin SDK: Firebase configuration not found. Please set FIREBASE_SERVICE_ACCOUNT_KEY or FIREBASE_PROJECT_ID[39m
2025-06-11 23:04:21:421 [31merror[39m: [31mError sending push notification: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-11 23:04:21:421 [31merror[39m: [31mError sending notifications to users: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 01:35:21:3521 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 04:35:37:3537 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 08:35:14:3514 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 08:35:14:3514 [31merror[39m: [31mRedis error: connect EHOSTUNREACH ************:6379[39m
2025-06-12 08:35:14:3514 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-12 09:11:16:1116 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 09:28:33:2833 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 09:28:36:2836 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/device-token | DELETE | ::ffff:************[39m
2025-06-12 09:59:16:5916 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 10:18:55:1855 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/customer-salon-access/available-salons?accountId=2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::ffff:************[39m
2025-06-12 10:36:08:368 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/salons/account/2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::ffff:************[39m
2025-06-12 10:36:11:3611 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications | GET | ::ffff:************[39m
2025-06-12 10:36:11:3611 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/count | GET | ::ffff:************[39m
2025-06-12 10:36:33:3633 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/salons/account/2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::ffff:************[39m
2025-06-12 10:36:36:3636 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications | GET | ::ffff:************[39m
2025-06-12 10:36:36:3636 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/count | GET | ::ffff:************[39m
2025-06-12 10:37:58:3758 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/salons/account/2e29013b-19ca-4243-bb4e-3dae75fc1fe7 | GET | ::ffff:************[39m
2025-06-12 10:38:04:384 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/device-token | POST | ::ffff:************[39m
2025-06-12 10:38:05:385 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications | GET | ::ffff:************[39m
2025-06-12 10:38:05:385 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/count | GET | ::ffff:************[39m
2025-06-12 10:39:33:3933 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/auth/logout | GET | ::ffff:************[39m
2025-06-12 10:39:34:3934 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/device-token | DELETE | ::ffff:************[39m
2025-06-12 11:00:13:013 [31merror[39m: [31mError sending push notification: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 11:00:13:013 [31merror[39m: [31mError sending notifications to users: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 11:00:13:013 [31merror[39m: [31mError sending offer notification: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 11:08:22:822 [31merror[39m: [31mError sending push notification: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 11:08:22:822 [31merror[39m: [31mFirebase project configuration error. Please check:[39m
2025-06-12 11:08:22:822 [31merror[39m: [31m1. FIREBASE_PROJECT_ID is correct[39m
2025-06-12 11:08:22:822 [31merror[39m: [31m2. Firebase project exists and has FCM enabled[39m
2025-06-12 11:08:22:822 [31merror[39m: [31m3. Service account has proper permissions[39m
2025-06-12 11:08:22:822 [31merror[39m: [31m4. Firebase Admin SDK is properly configured[39m
2025-06-12 11:10:10:1010 [31merror[39m: [31mError sending push notification: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 11:10:10:1010 [31merror[39m: [31mFirebase project configuration error. Please check:[39m
2025-06-12 11:10:10:1010 [31merror[39m: [31m1. FIREBASE_PROJECT_ID is correct[39m
2025-06-12 11:10:10:1010 [31merror[39m: [31m2. Firebase project exists and has FCM enabled[39m
2025-06-12 11:10:10:1010 [31merror[39m: [31m3. Service account has proper permissions[39m
2025-06-12 11:10:10:1010 [31merror[39m: [31m4. Firebase Admin SDK is properly configured[39m
2025-06-12 11:20:19:2019 [31merror[39m: [31mError sending push notification: An unknown server error was returned. Raw server response: "<!DOCTYPE html>[39m
[31m<html lang=en>[39m
[31m  <meta charset=utf-8>[39m
[31m  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">[39m
[31m  <title>Error 404 (Not Found)!!1</title>[39m
[31m  <style>[39m
[31m    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}[39m
[31m  </style>[39m
[31m  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>[39m
[31m  <p><b>404.</b> <ins>That’s an error.</ins>[39m
[31m  <p>The requested URL <code>/batch</code> was not found on this server.  <ins>That’s all we know.</ins>[39m
[31m". Status code: 404.[39m
2025-06-12 11:20:19:2019 [31merror[39m: [31mFirebase project configuration error. Please check:[39m
2025-06-12 11:20:19:2019 [31merror[39m: [31m1. FIREBASE_PROJECT_ID is correct[39m
2025-06-12 11:20:19:2019 [31merror[39m: [31m2. Firebase project exists and has FCM enabled[39m
2025-06-12 11:20:19:2019 [31merror[39m: [31m3. Service account has proper permissions[39m
2025-06-12 11:20:19:2019 [31merror[39m: [31m4. Firebase Admin SDK is properly configured[39m
2025-06-12 13:44:46:4446 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 16:05:12:512 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 17:23:59:2359 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 17:24:01:241 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/device-token | DELETE | ::ffff:************[39m
2025-06-12 17:24:22:2422 [31merror[39m: [31mError | Invalid credentials | /api/v1/auth/login | POST | ::ffff:************[39m
2025-06-12 20:28:13:2813 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 20:28:13:2813 [31merror[39m: [31mRedis error: connect ENETUNREACH ************:6379[39m
2025-06-12 20:28:16:2816 [31merror[39m: [31mRedis error: connect ECONNABORTED ************:6379[39m
2025-06-12 20:53:53:5353 [31merror[39m: [31mRedis error: read ECONNRESET[39m
2025-06-12 20:53:56:5356 [31merror[39m: [31mError | Not authorized to access this route | /api/v1/notifications/device-token | DELETE | ::ffff:***********[39m
