{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-293020b3355e565a6025.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-Debug-48ef96eb5140b3ef59b2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-e798bb6b05d27fac766b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-Debug-c61c08ee8152260d8568.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-a35248a22397ee63d0cb.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-0381cafcce595184bb91.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [6]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-d0968c86c56b7e7d0ebd.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/sgic-product/salon-owner-mob-app/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-c0db83c27d8151014ba6.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-Debug-56a284f79d3f87a0df1f.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-Debug-ee8cbbd194445b2c3309.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-dbe1822a5de122354479.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-a710810c09da84316dfe.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-2ab715787e77af8797e0.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-d9ca58567a1f7fea2edb.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-ebd5b7f810c3de2698cf.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/sgic-product/salon-owner-mob-app/android/app/.cxx/Debug/3195n2nj/x86_64", "source": "D:/sgic-product/salon-owner-mob-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}