import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useCustomerSalon, CustomerSalonData } from '../../context/CustomerSalonContext';
import { useAuth } from '../../context/AuthContext';

const CustomerSalonSelectionScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const {
    accessibleSalons,
    isLoading,
    selectSalon,
    refreshAccessibleSalons,
  } = useCustomerSalon();
 console.log("accessibleSalons========",accessibleSalons);
  useEffect(() => {
   
    
    // Refresh salons when screen loads
    refreshAccessibleSalons();
  }, []);

  const handleSalonSelect = (salon: CustomerSalonData) => {
    try {
      selectSalon(salon);
      
      // Navigate to customer home page
      navigation.navigate('CustomerHome' as never);
    } catch (error) {
      console.error('❌ Error selecting salon:', error);
      Alert.alert('Error', 'Failed to select salon. Please try again.');
    }
  };

  const renderSalonCard = ({ item }: { item: CustomerSalonData }) => (
    <TouchableOpacity
      style={styles.salonCard}
      onPress={() => handleSalonSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.salonCardContent}>
        {item.salon?.logo ? (
          <Image source={{ uri: item.salon.logo }} style={styles.salonLogo} />
        ) : (
          <View style={styles.salonLogoPlaceholder}>
            <Text style={styles.salonLogoText}>
              {item.salon?.name?.charAt(0)?.toUpperCase() || 'S'}
            </Text>
          </View>
        )}
        
        <View style={styles.salonInfo}>
          <Text style={styles.salonName}>{item.salon?.name || 'Unknown Salon'}</Text>
          {item.salon?.description && (
            <Text style={styles.salonDescription} numberOfLines={2}>
              {item.salon.description}
            </Text>
          )}
          {item.salon?.address && (
            <Text style={styles.salonAddress} numberOfLines={1}>
              📍 {item.salon.address}
            </Text>
          )}
          {item.salon?.city && (
            <Text style={styles.salonCity}>{item.salon.city}</Text>
          )}
        </View>
        
        <View style={styles.selectButton}>
          <Text style={styles.selectButtonText}>Select</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#b363e0" />
        <Text style={styles.loadingText}>Loading your salons...</Text>
      </View>
    );
  }

  if (accessibleSalons.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No Salons Available</Text>
        <Text style={styles.emptyMessage}>
          You don't have access to any salons yet. Please contact the salon owner to get access.
        </Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={refreshAccessibleSalons}
        >
          <Text style={styles.refreshButtonText}>Refresh</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Select Your Salon</Text>
        <Text style={styles.subtitle}>
          Welcome {user?.firstName}! Choose a salon to continue.
        </Text>
      </View>

      <FlatList
        data={accessibleSalons}
        renderItem={renderSalonCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Poppins-Regular',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyMessage: {
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  refreshButton: {
    backgroundColor: '#b363e0',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 24,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    color: '#666',
  },
  listContainer: {
    padding: 16,
  },
  salonCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  salonCardContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  salonLogo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  salonLogoPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#b363e0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  salonLogoText: {
    color: '#fff',
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
  },
  salonInfo: {
    flex: 1,
    marginRight: 16,
  },
  salonName: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#333',
    marginBottom: 4,
  },
  salonDescription: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#666',
    marginBottom: 4,
  },
  salonAddress: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#888',
    marginBottom: 2,
  },
  salonCity: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#888',
  },
  selectButton: {
    backgroundColor: '#b363e0',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  selectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins-SemiBold',
  },
});

export default CustomerSalonSelectionScreen;
