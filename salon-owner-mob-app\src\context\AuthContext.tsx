import React, { create<PERSON>ontext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AuthState, User, LoginRequest, RegisterRequest, SignUpData } from '../types';
import { authService } from '../services/authService';
import { getAccountId } from '../constants/app';
import {
  saveAuthToken,
  saveUserData,
  getAuthToken,
  getUserData,
  clearAllData
} from '../utils/storage';

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'SIGNUP_SUCCESS' }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'CLEAR_ERROR' };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: true, // Start with loading true to check auth state
  error: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        loading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };

    case 'AUTH_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };

    case 'SIGNUP_SUCCESS':
      return {
        ...state,
        loading: false,
        error: null,
      };

    case 'LOGOUT':
      return {
        ...initialState,
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// Auth context type
interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest, imageUri?: string) => Promise<void>;
  signUp: (userData: SignUpData, imageUri?: string) => Promise<{ success: boolean; user: User }>;
  logout: () => Promise<void>;
  updateUser: (user: User) => void;
  clearError: () => void;
  checkAuthState: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication state on app start
  const checkAuthState = async () => {
    try {
      // Don't dispatch AUTH_START since we start with loading: true

      const token = await getAuthToken();
      const userData = await getUserData();

      if (token && userData) {
        // For now, just use stored data without API call to avoid network issues
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user: userData, token }
        });
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Login function
  const login = async (credentials: LoginRequest) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authService.login(credentials);
console.log(response);

      // Save to storage
      if (!response.data?.token) {
        throw new Error('Token is missing in the response');
      }
      await saveAuthToken(response.data.token);
      await saveUserData(response.data);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user: response.data, token: response.token }
      });
    } catch (error: any) {
      dispatch({
        type: 'AUTH_FAILURE',
        payload: error.message || 'Login failed'
      });
      throw error;
    }
  };

  // Register function
  const register = async (userData: RegisterRequest, imageUri?: string) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authService.registerWithImage(userData, imageUri);

      // Save to storage
      await saveAuthToken(response.token);
      await saveUserData(response.data);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user: response.data, token: response.token }
      });
    } catch (error: any) {
      dispatch({
        type: 'AUTH_FAILURE',
        payload: error.message || 'Registration failed'
      });
      throw error;
    }
  };

  // Sign up function (for the new signup screen)
  const signUp = async (userData: SignUpData, imageUri?: string) => {
    try {
      dispatch({ type: 'AUTH_START' });

      // Get the current account ID from environment config
      const accountId = getAccountId();
      console.log('🔧 AuthContext: Using Account ID for registration:', accountId);

      // SignUpData now matches RegisterRequest format with accountId and salonIds
      const registerData: RegisterRequest = {
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        password: userData.password,
        phoneNumber: userData.phoneNumber,
        accountId: accountId, // ✅ Include account ID from config
        salonIds: userData.salonIds, // ✅ Include selected salon IDs
      };

      console.log('📤 AuthContext: Sending registration data:', {
        ...registerData,
        password: '[HIDDEN]', // Don't log the actual password
        salonIds: userData.salonIds ? `[${userData.salonIds.length} salons]` : 'none'
      });

      // Use registerWithImage if image is provided, otherwise use regular register
      const response = imageUri
        ? await authService.registerWithImage(registerData, imageUri)
        : await authService.register(registerData);

      // For signup, we don't automatically log the user in
      // Instead, we dispatch SIGNUP_SUCCESS to clear loading state
      dispatch({ type: 'SIGNUP_SUCCESS' });

      // Return success to indicate registration was successful
      return { success: true, user: response.data };
    } catch (error: any) {
      dispatch({
        type: 'AUTH_FAILURE',
        payload: error.message || 'Sign up failed'
      });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      await clearAllData();
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Update user function
  const updateUser = (user: User) => {
    dispatch({ type: 'UPDATE_USER', payload: user });
    saveUserData(user);
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Check auth state on mount
  useEffect(() => {
    checkAuthState();
  }, []);

  const value: AuthContextType = {
    ...state,
    login,
    register,
    signUp,
    logout,
    updateUser,
    clearError,
    checkAuthState,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
