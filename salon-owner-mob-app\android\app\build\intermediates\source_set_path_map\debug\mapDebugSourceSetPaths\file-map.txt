com.saloon_app-activity-1.8.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\00fc3751e3234e26481b34a69455ed2c\transformed\activity-1.8.0\res
com.saloon_app-play-services-basement-18.5.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1c3fd62b5fb636be47a17086a4f32c\transformed\play-services-basement-18.5.0\res
com.saloon_app-appcompat-v7-27.1.1-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\1124e5f30867269037d17b8ed56e41f5\transformed\appcompat-v7-27.1.1\res
com.saloon_app-fragment-ktx-1.6.1-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\2700358a246c5f76c91c80fbb92c317b\transformed\fragment-ktx-1.6.1\res
com.saloon_app-core-1.13.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\3135f157d17fbe3966fe84b667792db9\transformed\core-1.13.1\res
com.saloon_app-lifecycle-livedata-core-2.6.2-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\33eaa7455c0103498af753b8041cc730\transformed\lifecycle-livedata-core-2.6.2\res
com.saloon_app-constraintlayout-2.0.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\388f3cc8a071fa51f43527d5b5f63a52\transformed\constraintlayout-2.0.1\res
com.saloon_app-swiperefreshlayout-1.1.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\40629169c250d192dfedef27739adc85\transformed\swiperefreshlayout-1.1.0\res
com.saloon_app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5ee5998808c76a190b8e30b5456f6e\transformed\startup-runtime-1.1.1\res
com.saloon_app-firebase-messaging-24.1.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\54566d79601280a06b862aa8eff7e89c\transformed\firebase-messaging-24.1.1\res
com.saloon_app-lifecycle-process-2.6.2-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\695321ee91075e9a75fbbac8373949a3\transformed\lifecycle-process-2.6.2\res
com.saloon_app-lifecycle-runtime-2.6.2-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\6b431919dc18517c7faf4ac00998ef0d\transformed\lifecycle-runtime-2.6.2\res
com.saloon_app-viewpager2-1.0.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd42ff7a7a68aeac5f10c382cdad2c1\transformed\viewpager2-1.0.0\res
com.saloon_app-lifecycle-viewmodel-ktx-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\70112eb244cb39c1c18e4761c66535c7\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.saloon_app-play-services-base-18.5.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\7312d9ce0e64b42e563cee99ce0c7cbc\transformed\play-services-base-18.5.0\res
com.saloon_app-autofill-1.1.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\7741bcfeb1536b12a5d2b07f41854416\transformed\autofill-1.1.0\res
com.saloon_app-appcompat-resources-1.7.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\781ecc74f6e3e45b435a6046b1519983\transformed\appcompat-resources-1.7.0\res
com.saloon_app-lifecycle-livedata-core-ktx-2.6.2-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\7a47d67cc52951c1d8b983e34a624a4c\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.saloon_app-cardview-1.0.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\81b440a5833c4876bdd458cd9c55f863\transformed\cardview-1.0.0\res
com.saloon_app-ads-adservices-1.1.0-beta11-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\8204ee1f1962fe20a95999cc7181b1ae\transformed\ads-adservices-1.1.0-beta11\res
com.saloon_app-lifecycle-viewmodel-2.6.2-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\8297b959b2070abbb51e63c4b7e040f2\transformed\lifecycle-viewmodel-2.6.2\res
com.saloon_app-coordinatorlayout-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\83fd5f20da5885e279d5bca40a618d96\transformed\coordinatorlayout-1.2.0\res
com.saloon_app-lifecycle-runtime-ktx-2.6.2-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\8432739d0e6c4848c4377e44f1156169\transformed\lifecycle-runtime-ktx-2.6.2\res
com.saloon_app-emoji2-views-helper-1.3.0-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\87e21e4e8ba998df4b43b104ba52880a\transformed\emoji2-views-helper-1.3.0\res
com.saloon_app-support-core-ui-27.1.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96f217608aa033d786ce53285430e9\transformed\support-core-ui-27.1.1\res
com.saloon_app-savedstate-ktx-1.2.1-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\8fc976f242a0be8107eddc355aebe24e\transformed\savedstate-ktx-1.2.1\res
com.saloon_app-tracing-1.1.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\9860a1790fa94742326c79041c4b20b9\transformed\tracing-1.1.0\res
com.saloon_app-activity-ktx-1.8.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\9bab7ec81c9519a34891d2c25407173d\transformed\activity-ktx-1.8.0\res
com.saloon_app-support-compat-27.1.1-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\9ea03364890a64cdb625a191a3975b0f\transformed\support-compat-27.1.1\res
com.saloon_app-emoji2-1.3.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\a338e36f8e4bb82b332b6f077ecfc270\transformed\emoji2-1.3.0\res
com.saloon_app-appcompat-1.7.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\af63e50a53e992ea9c751fc2deb2fc57\transformed\appcompat-1.7.0\res
com.saloon_app-fragment-1.6.1-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\af87e291a6224e8aa518fad3d9a4819e\transformed\fragment-1.6.1\res
com.saloon_app-play-services-auth-21.2.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\b1d52d0c97d7b207ef559be401853ead\transformed\play-services-auth-21.2.0\res
com.saloon_app-firebase-common-21.0.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\b3499dbe586f8567d1811344f8bbe37c\transformed\firebase-common-21.0.0\res
com.saloon_app-ads-adservices-java-1.1.0-beta11-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\b8980613ae1e9bd2d6f5af3b34ada75d\transformed\ads-adservices-java-1.1.0-beta11\res
com.saloon_app-recyclerview-1.1.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\c41e74d136eafa502f00b0c654b998da\transformed\recyclerview-1.1.0\res
com.saloon_app-core-runtime-2.2.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\c798d2582b6892bffdb37697f4e5e735\transformed\core-runtime-2.2.0\res
com.saloon_app-drawerlayout-1.1.1-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\c81709be75b2b3d2cfa7962c64ccd193\transformed\drawerlayout-1.1.1\res
com.saloon_app-lifecycle-viewmodel-savedstate-2.6.2-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\cc3fbc86a4c8cd9e8224f9e233391e8a\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.saloon_app-core-ktx-1.13.1-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\cd2b7144f8a7f1d1cf2144b4e1d28335\transformed\core-ktx-1.13.1\res
com.saloon_app-material-1.12.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\cf9bc6b4f1abcf764423b202ca0f1044\transformed\material-1.12.0\res
com.saloon_app-transition-1.5.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\d0d9abaaf939dcc64a373cc802e7c144\transformed\transition-1.5.0\res
com.saloon_app-profileinstaller-1.3.1-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\d26f832b65286f035527484d43757bb1\transformed\profileinstaller-1.3.1\res
com.saloon_app-drawee-3.6.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3ff8810e3587e8fbb260fd6795ea7c\transformed\drawee-3.6.0\res
com.saloon_app-annotation-experimental-1.4.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\f461b1769fe34ef74d3985637e4ce39c\transformed\annotation-experimental-1.4.0\res
com.saloon_app-savedstate-1.2.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\f97fa94e6a2772d5640c133ce2200389\transformed\savedstate-1.2.1\res
com.saloon_app-react-android-0.79.2-debug-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b0ce559ae890269420b3dec0e4060\transformed\react-android-0.79.2-debug\res
com.saloon_app-pngs-47 D:\sgic-product\salon-owner-mob-app\android\app\build\generated\res\pngs\debug
com.saloon_app-res-48 D:\sgic-product\salon-owner-mob-app\android\app\build\generated\res\processDebugGoogleServices
com.saloon_app-resValues-49 D:\sgic-product\salon-owner-mob-app\android\app\build\generated\res\resValues\debug
com.saloon_app-packageDebugResources-50 D:\sgic-product\salon-owner-mob-app\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.saloon_app-packageDebugResources-51 D:\sgic-product\salon-owner-mob-app\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.saloon_app-debug-52 D:\sgic-product\salon-owner-mob-app\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.saloon_app-debug-53 D:\sgic-product\salon-owner-mob-app\android\app\src\debug\res
com.saloon_app-main-54 D:\sgic-product\salon-owner-mob-app\android\app\src\main\res
com.saloon_app-debug-55 D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-56 D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-57 D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-58 D:\sgic-product\salon-owner-mob-app\node_modules\@react-native-firebase\messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-59 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-60 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-image-picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-61 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-linear-gradient\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-62 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-push-notification\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-63 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-64 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.saloon_app-debug-65 D:\sgic-product\salon-owner-mob-app\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug\packageDebugResources
